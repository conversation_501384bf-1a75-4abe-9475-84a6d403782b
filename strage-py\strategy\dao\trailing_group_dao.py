"""
跟踪组DAO类
迁移自: com.project.strategy.mapper.TrailingGroupMapper
"""
import logging
from typing import List, Optional
from datetime import datetime
from dao.base_dao import BaseDAO
from ..domain.entity.trailing_group import TrailingGroup

logger = logging.getLogger(__name__)


class TrailingGroupDAO(BaseDAO):
    """跟踪组DAO类"""
    
    def __init__(self):
        """初始化跟踪组DAO"""
        logger.info("TrailingGroupDAO initialized")
    
    def _row_to_entity(self, row: dict) -> TrailingGroup:
        """
        将数据库行转换为实体对象
        
        Args:
            row: 数据库行字典
            
        Returns:
            跟踪组实体对象
        """
        return TrailingGroup(
            id=row.get('id'),
            user_id=row.get('user_id'),
            parent_id=row.get('parent_id'),
            group_name=row.get('group_name'),
            profit_state=row.get('profit_state'),
            state=row.get('state'),
            loss_times=row.get('loss_times'),
            message=row.get('message'),
            create_time=row.get('create_time'),
            update_time=row.get('update_time')
        )
    
    def select_trailing_group_by_id(self, id: int) -> Optional[TrailingGroup]:
        """
        根据ID查询跟踪组
        对应: selectTrailingGroupById
        
        Args:
            id: 组ID
            
        Returns:
            跟踪组对象，不存在返回None
        """
        try:
            sql = "SELECT * FROM trailing_group WHERE id = %s"
            results = self.execute_query(sql, (id,))
            
            if results:
                row = results[0]
                return self._row_to_entity(row)
            return None
        except Exception as e:
            logger.error(f"根据ID查询跟踪组失败: {e}")
            return None
    
    def select_trailing_group_list(self, trailing_group: Optional[TrailingGroup] = None) -> List[TrailingGroup]:
        """
        查询跟踪组列表
        对应: selectTrailingGroupList
        
        Args:
            trailing_group: 查询条件对象
            
        Returns:
            跟踪组列表
        """
        try:
            sql = "SELECT * FROM trailing_group"
            params = []
            where_conditions = []
            
            if trailing_group:
                # 用户ID精确查询
                if trailing_group.user_id is not None:
                    where_conditions.append("user_id = %s")
                    params.append(trailing_group.user_id)
                
                # 父组ID精确查询
                if trailing_group.parent_id is not None:
                    where_conditions.append("parent_id = %s")
                    params.append(trailing_group.parent_id)
                
                # 组名称模糊查询
                if trailing_group.group_name:
                    where_conditions.append("group_name LIKE %s")
                    params.append(f"%{trailing_group.group_name}%")
                
                # 盈利状态精确查询
                if trailing_group.profit_state is not None:
                    where_conditions.append("profit_state = %s")
                    params.append(trailing_group.profit_state)
                
                # 状态精确查询
                if trailing_group.state is not None:
                    where_conditions.append("state = %s")
                    params.append(trailing_group.state)
                
                # 亏损次数精确查询
                if trailing_group.loss_times is not None:
                    where_conditions.append("loss_times = %s")
                    params.append(trailing_group.loss_times)
                
                # 消息精确查询
                if trailing_group.message:
                    where_conditions.append("message = %s")
                    params.append(trailing_group.message)
            
            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)
            
            sql += " ORDER BY id DESC"
            
            results = self.execute_query(sql, params if params else None)
            
            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))
            
            return entities
        except Exception as e:
            logger.error(f"查询跟踪组列表失败: {e}")
            return []
    
    def insert_trailing_group(self, trailing_group: TrailingGroup) -> bool:
        """
        插入跟踪组
        对应: insertTrailingGroup
        
        Args:
            trailing_group: 跟踪组对象
            
        Returns:
            是否插入成功
        """
        try:
            sql = """
                INSERT INTO trailing_group (user_id, parent_id, group_name, profit_state, 
                                          state, loss_times, message, create_time, update_time)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                trailing_group.user_id,
                trailing_group.parent_id,
                trailing_group.group_name,
                trailing_group.profit_state,
                trailing_group.state,
                trailing_group.loss_times,
                trailing_group.message,
                trailing_group.create_time or datetime.now(),
                trailing_group.update_time or datetime.now()
            )
            
            result = self.execute_insert(sql, params)
            if result:
                trailing_group.id = result
                return True
            return False
        except Exception as e:
            logger.error(f"插入跟踪组失败: {e}")
            return False
    
    def update_trailing_group(self, trailing_group: TrailingGroup) -> bool:
        """
        更新跟踪组
        对应: updateTrailingGroup
        
        Args:
            trailing_group: 跟踪组对象
            
        Returns:
            是否更新成功
        """
        try:
            sql = """
                UPDATE trailing_group 
                SET user_id = %s, parent_id = %s, group_name = %s, profit_state = %s, 
                    state = %s, loss_times = %s, message = %s, update_time = %s
                WHERE id = %s
            """
            params = (
                trailing_group.user_id,
                trailing_group.parent_id,
                trailing_group.group_name,
                trailing_group.profit_state,
                trailing_group.state,
                trailing_group.loss_times,
                trailing_group.message,
                trailing_group.update_time or datetime.now(),
                trailing_group.id
            )
            
            affected_rows = self.execute_update(sql, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"更新跟踪组失败: {e}")
            return False
    
    def delete_trailing_group_by_id(self, id: int) -> bool:
        """
        根据ID删除跟踪组
        对应: deleteTrailingGroupById
        
        Args:
            id: 组ID
            
        Returns:
            是否删除成功
        """
        try:
            sql = "DELETE FROM trailing_group WHERE id = %s"
            affected_rows = self.execute_update(sql, (id,))
            return affected_rows > 0
        except Exception as e:
            logger.error(f"根据ID删除跟踪组失败: {e}")
            return False
    
    def delete_trailing_group_by_ids(self, ids: List[str]) -> int:
        """
        根据ID列表批量删除跟踪组
        对应: deleteTrailingGroupByIds
        
        Args:
            ids: ID字符串列表
            
        Returns:
            删除的记录数量
        """
        try:
            if not ids:
                return 0
            
            # 构建IN子句的占位符
            placeholders = ','.join(['%s'] * len(ids))
            sql = f"DELETE FROM trailing_group WHERE id IN ({placeholders})"
            
            affected_rows = self.execute_update(sql, ids)
            return affected_rows
        except Exception as e:
            logger.error(f"批量删除跟踪组失败: {e}")
            return 0
    
    def select_by_user_id(self, user_id: int) -> List[TrailingGroup]:
        """
        根据用户ID查询跟踪组列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            跟踪组列表
        """
        try:
            sql = "SELECT * FROM trailing_group WHERE user_id = %s ORDER BY id DESC"
            results = self.execute_query(sql, (user_id,))
            
            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))
            
            return entities
        except Exception as e:
            logger.error(f"根据用户ID查询跟踪组列表失败: {e}")
            return []
    
    def select_by_parent_id(self, parent_id: int) -> List[TrailingGroup]:
        """
        根据父组ID查询子组列表
        
        Args:
            parent_id: 父组ID
            
        Returns:
            子组列表
        """
        try:
            sql = "SELECT * FROM trailing_group WHERE parent_id = %s ORDER BY id DESC"
            results = self.execute_query(sql, (parent_id,))
            
            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))
            
            return entities
        except Exception as e:
            logger.error(f"根据父组ID查询子组列表失败: {e}")
            return []

    def select_active_groups(self) -> List[TrailingGroup]:
        """
        查询所有激活的跟踪组

        Returns:
            激活的跟踪组列表
        """
        try:
            sql = "SELECT * FROM trailing_group WHERE state = 1 ORDER BY id DESC"
            results = self.execute_query(sql)

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))

            return entities
        except Exception as e:
            logger.error(f"查询激活的跟踪组失败: {e}")
            return []

    def select_by_state(self, state: int) -> List[TrailingGroup]:
        """
        根据状态查询跟踪组列表

        Args:
            state: 状态值

        Returns:
            跟踪组列表
        """
        try:
            sql = "SELECT * FROM trailing_group WHERE state = %s ORDER BY id DESC"
            results = self.execute_query(sql, (state,))

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))

            return entities
        except Exception as e:
            logger.error(f"根据状态查询跟踪组列表失败: {e}")
            return []

    def update_state(self, id: int, state: int) -> bool:
        """
        更新组状态

        Args:
            id: 组ID
            state: 新状态

        Returns:
            是否更新成功
        """
        try:
            sql = "UPDATE trailing_group SET state = %s, update_time = %s WHERE id = %s"
            params = (state, datetime.now(), id)

            affected_rows = self.execute_update(sql, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"更新组状态失败: {e}")
            return False

    def increment_loss_times(self, id: int) -> bool:
        """
        增加亏损次数

        Args:
            id: 组ID

        Returns:
            是否更新成功
        """
        try:
            sql = "UPDATE trailing_group SET loss_times = loss_times + 1, update_time = %s WHERE id = %s"
            params = (datetime.now(), id)

            affected_rows = self.execute_update(sql, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"增加亏损次数失败: {e}")
            return False

    def reset_loss_times(self, id: int) -> bool:
        """
        重置亏损次数

        Args:
            id: 组ID

        Returns:
            是否更新成功
        """
        try:
            sql = "UPDATE trailing_group SET loss_times = 0, update_time = %s WHERE id = %s"
            params = (datetime.now(), id)

            affected_rows = self.execute_update(sql, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"重置亏损次数失败: {e}")
            return False

    def count_by_user_id(self, user_id: int) -> int:
        """
        统计用户的跟踪组数量

        Args:
            user_id: 用户ID

        Returns:
            组数量
        """
        try:
            sql = "SELECT COUNT(*) as count FROM trailing_group WHERE user_id = %s"
            results = self.execute_query(sql, (user_id,))

            if results:
                return results[0].get('count', 0)
            return 0
        except Exception as e:
            logger.error(f"统计用户跟踪组数量失败: {e}")
            return 0

    def count_by_state(self, state: int) -> int:
        """
        统计指定状态的组数量

        Args:
            state: 状态值

        Returns:
            组数量
        """
        try:
            sql = "SELECT COUNT(*) as count FROM trailing_group WHERE state = %s"
            results = self.execute_query(sql, (state,))

            if results:
                return results[0].get('count', 0)
            return 0
        except Exception as e:
            logger.error(f"统计指定状态组数量失败: {e}")
            return 0

    def count_loss_time(self, parent_id: int) -> int:
        """
        统计连续亏损次数
        对应Java中的countLossTime方法

        Args:
            parent_id: 父组ID

        Returns:
            连续亏损次数总和
        """
        try:
            sql = """
                SELECT COALESCE(SUM(loss_times), 0) AS total_loss_times
                FROM (
                    SELECT loss_times
                    FROM trailing_group
                    WHERE parent_id = %s OR id = %s
                    ORDER BY id DESC
                    LIMIT 3
                ) AS subquery
            """
            results = self.execute_query(sql, (parent_id, parent_id))

            if results:
                return results[0].get('total_loss_times', 0)
            return 0
        except Exception as e:
            logger.error(f"统计连续亏损次数失败: {e}")
            return 0

"""
订阅交易对实体类
迁移自: com.project.strategy.domain.entity.SubscribeSymbol
"""
from dataclasses import dataclass
from typing import Optional
from decimal import Decimal
from datetime import datetime


@dataclass
class SubscribeSymbol:
    """订阅交易对实体"""
    
    id: Optional[int] = None
    platform: Optional[str] = None  # 平台
    symbol: Optional[str] = None  # 交易所对应币对名
    price: Optional[Decimal] = None  # 价格
    state: Optional[int] = None  # 0: 不开启， 1：开启订阅
    message: Optional[str] = None  # 描述信息
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        if self.id is not None and not isinstance(self.id, int):
            self.id = int(self.id) if str(self.id).isdigit() else None
            
        if self.state is not None and not isinstance(self.state, int):
            self.state = int(self.state) if str(self.state).isdigit() else None
        
        # 处理Decimal字段
        if self.price is not None and not isinstance(self.price, Decimal):
            try:
                self.price = Decimal(str(self.price))
            except (ValueError, TypeError):
                self.price = None
    
    def is_active(self) -> bool:
        """检查是否开启订阅"""
        return self.state == 1
    
    def is_inactive(self) -> bool:
        """检查是否未开启订阅"""
        return self.state == 0
    
    def is_valid(self) -> bool:
        """检查订阅信息是否有效"""
        return (
            self.platform is not None and 
            self.symbol is not None and
            len(self.platform.strip()) > 0 and
            len(self.symbol.strip()) > 0
        )
    
    def get_symbol_key(self) -> str:
        """获取交易对键值（平台-交易对）"""
        if self.platform and self.symbol:
            return f"{self.platform}-{self.symbol}"
        return ""
    
    @classmethod
    def builder(cls) -> 'SubscribeSymbolBuilder':
        """创建构建器"""
        return SubscribeSymbolBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, Decimal):
                    result[key] = str(value)
                elif isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'SubscribeSymbol':
        """从字典创建实例"""
        # 处理datetime字段
        if 'create_time' in data and data['create_time']:
            if isinstance(data['create_time'], str):
                try:
                    data['create_time'] = datetime.fromisoformat(data['create_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['create_time'] = None
        
        if 'update_time' in data and data['update_time']:
            if isinstance(data['update_time'], str):
                try:
                    data['update_time'] = datetime.fromisoformat(data['update_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['update_time'] = None
        
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"SubscribeSymbol(id={self.id}, platform='{self.platform}', symbol='{self.symbol}', state={self.state})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"SubscribeSymbol(id={self.id}, platform='{self.platform}', "
                f"symbol='{self.symbol}', price={self.price}, state={self.state})")


class SubscribeSymbolBuilder:
    """SubscribeSymbol构建器"""
    
    def __init__(self):
        self._data = {}
    
    def id(self, id: int) -> 'SubscribeSymbolBuilder':
        self._data['id'] = id
        return self
    
    def platform(self, platform: str) -> 'SubscribeSymbolBuilder':
        self._data['platform'] = platform
        return self
    
    def symbol(self, symbol: str) -> 'SubscribeSymbolBuilder':
        self._data['symbol'] = symbol
        return self
    
    def price(self, price: Decimal) -> 'SubscribeSymbolBuilder':
        self._data['price'] = price
        return self
    
    def state(self, state: int) -> 'SubscribeSymbolBuilder':
        self._data['state'] = state
        return self
    
    def message(self, message: str) -> 'SubscribeSymbolBuilder':
        self._data['message'] = message
        return self
    
    def create_time(self, create_time: datetime) -> 'SubscribeSymbolBuilder':
        self._data['create_time'] = create_time
        return self
    
    def update_time(self, update_time: datetime) -> 'SubscribeSymbolBuilder':
        self._data['update_time'] = update_time
        return self
    
    def build(self) -> SubscribeSymbol:
        """构建SubscribeSymbol实例"""
        return SubscribeSymbol(**self._data)

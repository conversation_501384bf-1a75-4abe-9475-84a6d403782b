"""
Flask应用程序入口
重构后的版本，采用标准MVC分层架构
"""
import os
import logging
from flask import Flask, render_template, request, redirect, url_for, session, jsonify, flash, send_file, make_response
from config import config
from utils.database import init_database
from services.auth_service import AuthService, login_required
from controllers.auth import auth_bp
from controllers.user_controller import user_bp
from controllers.system_controller import system_bp
from controllers.monitor_controller import monitor_bp
from strategy.controllers.subscribe_symbol_controller import subscribe_symbol_bp
from strategy.controllers.ex_account_controller import ex_account_bp
from werkzeug.utils import secure_filename
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from io import BytesIO
import tempfile
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    config_name = config_name or os.getenv('FLASK_CONFIG') or 'default'
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 初始化数据库
    if not init_database():
        logger.error("数据库初始化失败")
        raise Exception("数据库连接失败")

    # 注册蓝图
    app.register_blueprint(auth_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(system_bp)
    app.register_blueprint(monitor_bp)
    app.register_blueprint(subscribe_symbol_bp)
    app.register_blueprint(ex_account_bp)

    return app

app = create_app()

# 根路径路由
@app.route('/')
@login_required
def index():
    """首页"""
    user = AuthService.get_current_user()
    return render_template('index.html',
                         user=user,
                         footer=True,
                         copyright_year=2024,
                         side_theme='skin-blue',
                         skin_name='sidebar-mini')

@app.route('/index')
@login_required
def index_alias():
    """首页别名"""
    return index()

# 锁屏相关路由（保留在app.py中，因为它们是系统级功能）
@app.route('/lockscreen', methods=['GET'])
@login_required
def lockscreen():
    """锁屏页面"""
    try:
        # 获取当前用户信息
        current_user = AuthService.get_current_user()
        if current_user and current_user.get('user_id'):
            from services.user_service import UserService
            user_id = current_user.get('user_id')
            user = UserService.select_user_by_id(user_id)
            return render_template('lock.html', user=user)
        else:
            return redirect('/login')

    except Exception as e:
        logger.error(f"锁屏页面失败: {e}")
        return redirect('/login')

@app.route('/unlockscreen', methods=['POST'])
@login_required
def unlockscreen():
    """解锁屏幕"""
    try:
        password = request.form.get('password', '').strip()
        current_user = AuthService.get_current_user()
        user_id = current_user.get('user_id') if current_user else None

        if not password:
            return jsonify({
                'code': 1,
                'msg': '请输入密码'
            })

        if not user_id:
            return jsonify({
                'code': 1,
                'msg': '服务器超时，请重新登录'
            })

        # 验证密码
        from services.user_service import UserService
        user = UserService.select_user_by_id(user_id)
        if not user:
            return jsonify({
                'code': 1,
                'msg': '用户不存在'
            })

        if user.check_password(password):
            return jsonify({
                'code': 0,
                'msg': '解锁成功'
            })
        else:
            return jsonify({
                'code': 1,
                'msg': '密码错误'
            })

    except Exception as e:
        logger.error(f"解锁屏幕失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '解锁失败'
        })

# 策略交易相关路由（这些可以后续迁移到专门的Controller中）
@app.route('/strategy/trailing')
@login_required
def trailing_list():
    """策略列表页面"""
    return render_template('trailing/list.html')

@app.route('/strategy/trailing/add')
@login_required
def trailing_add():
    """新增策略页面"""
    return render_template('trailing/add.html')

@app.route('/strategy/trailing/edit/<int:id>')
@login_required
def trailing_edit(id):
    """编辑策略页面"""
    return render_template('trailing/edit.html', id=id)

@app.route('/strategy/trailing/follow/<int:id>')
@login_required
def trailing_follow(id):
    """跟单策略页面"""
    return render_template('trailing/follow.html', id=id)

# 交易账户相关路由
@app.route('/strategy/account')
@login_required
def account_list():
    """交易账户列表页面"""
    return render_template('strategy/account/list.html')


# 错误处理
@app.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    return render_template('error/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return render_template('error/500.html'), 500

@app.errorhandler(403)
def forbidden_error(error):
    """403错误处理"""
    return render_template('error/403.html'), 403

# 应用上下文处理器
@app.context_processor
def inject_user():
    """注入当前用户信息到模板上下文"""
    return dict(current_user=AuthService.get_current_user())

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

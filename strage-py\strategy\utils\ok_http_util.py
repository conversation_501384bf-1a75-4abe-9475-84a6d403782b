"""
HTTP请求工具类
迁移自: com.project.strategy.utils.OkHttpUtil
"""
import requests
import logging
from typing import Dict, Optional, Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


logger = logging.getLogger(__name__)


class OkHttpUtil:
    """HTTP请求工具类"""
    
    # 默认配置
    _is_proxy = True
    _proxy_host = "127.0.0.1"
    _proxy_port = 7890
    _connect_timeout = 15
    _read_timeout = 20
    _write_timeout = 20
    
    # 会话对象
    _session = None
    
    @classmethod
    def _get_session(cls) -> requests.Session:
        """获取HTTP会话对象"""
        if cls._session is None:
            cls._session = requests.Session()
            
            # 设置重试策略
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            cls._session.mount("http://", adapter)
            cls._session.mount("https://", adapter)
            
            # 设置代理
            if cls._is_proxy:
                proxies = {
                    'http': f'http://{cls._proxy_host}:{cls._proxy_port}',
                    'https': f'http://{cls._proxy_host}:{cls._proxy_port}'
                }
                cls._session.proxies.update(proxies)
        
        return cls._session
    
    @classmethod
    def set_is_proxy(cls, use_proxy: bool) -> None:
        """
        设置是否使用代理
        
        Args:
            use_proxy: 是否使用代理
        """
        cls._is_proxy = use_proxy
        # 重置会话以应用新配置
        cls._session = None
    
    @classmethod
    def set_proxy(cls, host: str, port: int) -> None:
        """
        设置代理服务器
        
        Args:
            host: 代理主机
            port: 代理端口
        """
        cls._proxy_host = host
        cls._proxy_port = port
        # 重置会话以应用新配置
        cls._session = None
    
    @classmethod
    def set_timeout(cls, connect_timeout: int = 15, read_timeout: int = 20) -> None:
        """
        设置超时时间
        
        Args:
            connect_timeout: 连接超时时间（秒）
            read_timeout: 读取超时时间（秒）
        """
        cls._connect_timeout = connect_timeout
        cls._read_timeout = read_timeout
    
    @classmethod
    def do_get(cls, url: str, header_map: Optional[Dict[str, str]] = None) -> str:
        """
        执行GET请求
        
        Args:
            url: 请求URL
            header_map: 请求头字典
            
        Returns:
            响应内容字符串
        """
        try:
            session = cls._get_session()
            
            headers = header_map or {}
            
            response = session.get(
                url,
                headers=headers,
                timeout=(cls._connect_timeout, cls._read_timeout)
            )
            
            response.raise_for_status()
            return response.text
            
        except Exception as e:
            logger.error(f"调用url={url}异常！{e}", exc_info=True)
            return ""
    
    @classmethod
    def do_delete(cls, url: str, header_map: Optional[Dict[str, str]] = None) -> str:
        """
        执行DELETE请求
        
        Args:
            url: 请求URL
            header_map: 请求头字典
            
        Returns:
            响应内容字符串
        """
        try:
            session = cls._get_session()
            
            headers = header_map or {}
            
            response = session.delete(
                url,
                headers=headers,
                timeout=(cls._connect_timeout, cls._read_timeout)
            )
            
            response.raise_for_status()
            return response.text
            
        except Exception as e:
            logger.error(f"DELETE请求异常: {e}", exc_info=True)
            return ""
    
    @classmethod
    def do_post(cls, url: str, data: Any = None, header_map: Optional[Dict[str, str]] = None) -> str:
        """
        执行POST请求
        
        Args:
            url: 请求URL
            data: 请求数据（可以是字符串、字典等）
            header_map: 请求头字典
            
        Returns:
            响应内容字符串
        """
        try:
            session = cls._get_session()
            
            headers = header_map or {}
            
            # 根据数据类型设置请求
            if isinstance(data, str):
                # 如果是字符串，假设是JSON
                if 'Content-Type' not in headers:
                    headers['Content-Type'] = 'application/json; charset=utf-8'
                response = session.post(
                    url,
                    data=data,
                    headers=headers,
                    timeout=(cls._connect_timeout, cls._read_timeout)
                )
            elif isinstance(data, dict):
                # 如果是字典，发送JSON
                response = session.post(
                    url,
                    json=data,
                    headers=headers,
                    timeout=(cls._connect_timeout, cls._read_timeout)
                )
            else:
                # 其他类型直接发送
                response = session.post(
                    url,
                    data=data,
                    headers=headers,
                    timeout=(cls._connect_timeout, cls._read_timeout)
                )
            
            response.raise_for_status()
            return response.text
            
        except Exception as e:
            logger.error(f"POST请求异常: {e}", exc_info=True)
            return ""
    
    @classmethod
    def do_post_json(cls, url: str, json_data: str, header_map: Optional[Dict[str, str]] = None) -> str:
        """
        执行POST JSON请求
        
        Args:
            url: 请求URL
            json_data: JSON字符串
            header_map: 请求头字典
            
        Returns:
            响应内容字符串
        """
        headers = header_map or {}
        headers['Content-Type'] = 'application/json; charset=utf-8'
        
        return cls.do_post(url, json_data, headers)
    
    @classmethod
    def do_post_with_content_type(cls, url: str, data: str, content_type: str) -> str:
        """
        执行POST请求（指定Content-Type）
        
        Args:
            url: 请求URL
            data: 请求数据
            content_type: 内容类型
            
        Returns:
            响应内容字符串
        """
        headers = {'Content-Type': content_type}
        return cls.do_post(url, data, headers)
    
    @classmethod
    def do_put(cls, url: str, data: Any = None, header_map: Optional[Dict[str, str]] = None) -> str:
        """
        执行PUT请求
        
        Args:
            url: 请求URL
            data: 请求数据
            header_map: 请求头字典
            
        Returns:
            响应内容字符串
        """
        try:
            session = cls._get_session()
            
            headers = header_map or {}
            
            if isinstance(data, str):
                if 'Content-Type' not in headers:
                    headers['Content-Type'] = 'application/json; charset=utf-8'
                response = session.put(
                    url,
                    data=data,
                    headers=headers,
                    timeout=(cls._connect_timeout, cls._read_timeout)
                )
            elif isinstance(data, dict):
                response = session.put(
                    url,
                    json=data,
                    headers=headers,
                    timeout=(cls._connect_timeout, cls._read_timeout)
                )
            else:
                response = session.put(
                    url,
                    data=data,
                    headers=headers,
                    timeout=(cls._connect_timeout, cls._read_timeout)
                )
            
            response.raise_for_status()
            return response.text
            
        except Exception as e:
            logger.error(f"PUT请求异常: {e}", exc_info=True)
            return ""
    
    @classmethod
    def get_response_with_status(cls, url: str, method: str = 'GET', 
                                data: Any = None, header_map: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        获取包含状态码的响应
        
        Args:
            url: 请求URL
            method: 请求方法
            data: 请求数据
            header_map: 请求头字典
            
        Returns:
            包含status_code、text、headers的字典
        """
        try:
            session = cls._get_session()
            
            headers = header_map or {}
            
            response = session.request(
                method,
                url,
                data=data if isinstance(data, (str, bytes)) else None,
                json=data if isinstance(data, dict) else None,
                headers=headers,
                timeout=(cls._connect_timeout, cls._read_timeout)
            )
            
            return {
                'status_code': response.status_code,
                'text': response.text,
                'headers': dict(response.headers),
                'success': response.status_code < 400
            }
            
        except Exception as e:
            logger.error(f"请求异常: {e}", exc_info=True)
            return {
                'status_code': 0,
                'text': '',
                'headers': {},
                'success': False,
                'error': str(e)
            }


# 测试函数
def main():
    """测试HTTP请求功能"""
    # 测试GET请求
    print("测试GET请求...")
    response = OkHttpUtil.do_get("https://httpbin.org/get")
    print(f"GET响应: {response[:200]}...")
    
    # 测试POST请求
    print("\n测试POST请求...")
    test_data = {"test": "data", "timestamp": "2024-01-01"}
    response = OkHttpUtil.do_post("https://httpbin.org/post", test_data)
    print(f"POST响应: {response[:200]}...")
    
    # 测试带状态码的响应
    print("\n测试带状态码的响应...")
    response_with_status = OkHttpUtil.get_response_with_status("https://httpbin.org/status/200")
    print(f"状态码: {response_with_status['status_code']}")
    print(f"成功: {response_with_status['success']}")


if __name__ == "__main__":
    main()

"""
策略状态枚举类
迁移自: com.project.strategy.enums.StrategyStateEnum
"""
from enum import Enum
from typing import Optional


class StrategyStateEnum(Enum):
    """策略状态枚举"""
    
    FAIL = (-1, "fail", "失败")
    UN_HANDLE = (0, "un_handle", "待处理")
    HANDLING = (1, "handling", "策略进行中")
    COMPLETED = (2, "completed", "策略完成")
    OTHERS = (99, "OTHERS", "Others")
    
    def __init__(self, code: int, name: str, desc: str):
        """
        初始化策略状态枚举
        
        Args:
            code: 状态代码
            name: 状态名称
            desc: 状态描述
        """
        self.code = code
        self.type_name = name  # 使用type_name避免与Enum.name冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取状态代码"""
        return self.code
    
    def get_name(self) -> str:
        """获取状态名称"""
        return self.type_name
    
    def get_desc(self) -> str:
        """获取状态描述"""
        return self.desc
    
    @classmethod
    def parse_value(cls, code: int) -> Optional['StrategyStateEnum']:
        """
        根据代码解析策略状态枚举
        
        Args:
            code: 状态代码
            
        Returns:
            对应的策略状态枚举，如果未找到则返回None
        """
        for strategy_enum in cls:
            if strategy_enum.get_code() == code:
                return strategy_enum
        return None
    
    @classmethod
    def parse_name(cls, name: str) -> Optional['StrategyStateEnum']:
        """
        根据名称解析策略状态枚举
        
        Args:
            name: 状态名称
            
        Returns:
            对应的策略状态枚举，如果未找到则返回None
        """
        for strategy_enum in cls:
            if strategy_enum.get_name() == name:
                return strategy_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_name}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"StrategyStateEnum.{self.name}"

# 交易策略系统项目架构分析文档

## 1. 项目概述

### 1.1 项目简介
这是一个基于Spring Boot的交易策略系统，主要实现**跟踪止盈(Trailing Profit)**策略。系统支持多个交易所（主要是币安Binance），可以进行现货和期货交易，通过实时监控市场价格变化来执行自动化交易策略。

### 1.2 核心功能
- **账户管理**：管理多个交易所账户的API密钥和余额信息
- **策略管理**：创建、启动、停止跟踪止盈策略
- **实时监控**：通过WebSocket获取实时市场数据
- **自动交易**：根据策略规则自动执行买卖操作
- **风险控制**：支持止损、分阶段止盈等风险管理功能

### 1.3 技术栈
- **框架**：Spring Boot + Spring MVC
- **数据访问**：MyBatis
- **安全框架**：Apache Shiro
- **JSON处理**：Fastjson
- **HTTP客户端**：OkHttp3
- **实时通信**：WebSocket
- **任务调度**：Spring Scheduling
- **工具库**：Lombok, Apache Commons

## 2. 项目目录结构

```
com.project.strategy/
├── GlobalCache.java                    # 全局缓存管理
├── context/                           # 应用上下文
│   └── AccountContext.java           # 账户服务上下文管理
├── controller/                        # 控制器层
│   ├── ExAccountController.java       # 交易所账户控制器
│   ├── SubscribeSymbolController.java # 订阅交易对控制器
│   └── TrailingProfitController.java  # 跟踪止盈控制器
├── domain/                           # 领域模型
│   ├── entity/                       # 实体类
│   │   ├── AccountBalance.java       # 账户余额实体
│   │   ├── ExAccount.java           # 交易所账户实体
│   │   ├── SubscribeSymbol.java     # 订阅交易对实体
│   │   ├── TrailingDetail.java      # 跟踪止盈详情实体
│   │   ├── TrailingFollow.java      # 跟踪跟单实体
│   │   ├── TrailingGroup.java       # 跟踪策略组实体
│   │   └── TrailingProfit.java      # 跟踪止盈主实体
│   ├── request/                      # 请求对象（空）
│   ├── response/                     # 响应对象
│   │   └── BinanceOrderBookResp.java # 币安订单簿响应
│   ├── BinanceAccountInfo.java       # 币安账户信息
│   ├── BinanceSymbol.java           # 币安交易对信息
│   ├── OrderBookItem.java           # 订单簿项目
│   ├── OrderBookPlatform.java       # 订单簿平台
│   └── OrderItem.java               # 订单项目
├── enums/                           # 枚举类
│   ├── AccountTypeEnum.java         # 账户类型枚举
│   ├── ExchangeEnum.java           # 交易所枚举
│   ├── InstrumentEnum.java         # 金融工具枚举
│   ├── OrderStateEnum.java         # 订单状态枚举
│   ├── OrderTypeEnum.java          # 订单类型枚举
│   ├── PositionSideEnum.java       # 仓位方向枚举
│   ├── StrategyStateEnum.java      # 策略状态枚举
│   ├── TradeModeEnum.java          # 交易模式枚举
│   └── TransferInternalEnum.java   # 内部转账枚举
├── manage/                         # 管理模块
│   ├── controller/                 # 管理控制器
│   │   └── StrategyManageController.java # 策略管理控制器
│   └── domain/                     # 管理领域对象（空）
├── mapper/                         # 数据访问层
│   ├── ExAccountMapper.java        # 交易所账户映射器
│   ├── SubscribeSymbolMapper.java  # 订阅交易对映射器
│   ├── TrailingGroupMapper.java    # 跟踪策略组映射器
│   └── TrailingProfitMapper.java   # 跟踪止盈映射器
├── service/                        # 服务层
│   ├── impl/                       # 服务实现
│   │   ├── AccountBeanFactory.java      # 账户Bean工厂
│   │   ├── BinanceFuturesService.java   # 币安期货服务
│   │   ├── BinanceService.java          # 币安服务
│   │   ├── BinanceSpotService.java      # 币安现货服务
│   │   ├── BinanceStreamService.java    # 币安流服务
│   │   ├── ExAccountBeanFactory.java    # 交易所账户Bean工厂
│   │   ├── ExAccountServiceImpl.java    # 交易所账户服务实现
│   │   ├── SubscribeSymbolServiceImpl.java # 订阅交易对服务实现
│   │   ├── TrailingBinanceServiceImpl.java # 跟踪币安服务实现
│   │   ├── TrailingGroupServiceImpl.java   # 跟踪策略组服务实现
│   │   └── TrailingProfitServiceImpl.java  # 跟踪止盈服务实现
│   ├── AbstractAccountCommon.java   # 抽象账户通用服务
│   ├── IAccountService.java        # 账户服务接口
│   ├── IExAccountService.java      # 交易所账户服务接口
│   ├── IFuturesService.java        # 期货服务接口
│   ├── ISubscribeSymbolService.java # 订阅交易对服务接口
│   ├── ITrailingGroupService.java  # 跟踪策略组服务接口
│   └── ITrailingProfitService.java # 跟踪止盈服务接口
├── task/                           # 任务调度
│   ├── StrategyTask.java           # 策略定时任务
│   └── WSSListenTask.java          # WebSocket监听任务
├── utils/                          # 工具类
│   ├── AESUtil.java                # AES加密工具
│   ├── GZIPUtils.java              # GZIP压缩工具
│   ├── OkHttpUtil.java             # HTTP请求工具
│   └── OkUtils.java                # 通用工具
└── websocket/                      # WebSocket相关
    └── subscribe/                  # 订阅相关（空）
```

## 3. 核心模块分析

### 3.1 全局缓存模块 (GlobalCache)
**文件**: `GlobalCache.java`

**功能**:
- 存储实时市场深度数据 (`DEPTH`)
- 存储实时价格数据 (`PRICE`)
- 存储策略跟踪数据 (`STRATEGY_TRAILING`)

**依赖关系**:
- 被所有需要访问实时数据的服务使用
- 与WebSocket数据流紧密关联

### 3.2 枚举定义模块 (enums)
**核心枚举类**:
- `ExchangeEnum`: 定义支持的交易所（币安、OKX等）
- `StrategyStateEnum`: 定义策略状态（失败、待处理、进行中、完成）
- `OrderTypeEnum`: 定义订单类型（市价单、限价单）
- `PositionSideEnum`: 定义仓位方向（多头、空头）

**作用**: 提供系统中所有枚举常量的统一定义，确保数据一致性

### 3.3 领域模型模块 (domain)
**核心实体**:

#### 3.3.1 TrailingProfit (跟踪止盈主实体)
- **作用**: 系统的核心业务实体，表示一个跟踪止盈策略
- **关键属性**:
  - 用户信息、账户信息、交易对
  - 开仓价格、市场价格、止损价格
  - 交易数量、盈利值、盈利率
  - 策略状态、策略类型
- **关联关系**: 与TrailingDetail一对多关系

#### 3.3.2 ExAccount (交易所账户实体)
- **作用**: 存储交易所账户的API密钥等敏感信息
- **安全性**: 包含加密的API密钥和密码信息

#### 3.3.3 TrailingDetail (跟踪止盈详情)
- **作用**: 存储分阶段止盈的详细配置
- **关键属性**: 涨幅触发条件、止盈幅度、状态等

### 3.4 服务层模块 (service)
**架构模式**: 接口 + 实现类的标准模式

#### 3.4.1 ITrailingProfitService & TrailingProfitServiceImpl
- **核心业务逻辑**: 跟踪止盈策略的执行引擎
- **主要方法**:
  - `startFollow()`: 启动跟单策略
  - `startTraling()`: 启动跟踪止盈
  - `positionUpdate()`: 更新仓位信息
  - `refreshOrder()`: 刷新订单状态

#### 3.4.2 AccountContext (账户上下文)
- **作用**: 管理所有账户服务实例的注册和获取
- **设计模式**: 工厂模式 + 上下文模式
- **功能**: 根据交易所、工具类型、账户名获取对应的服务实例

### 3.5 数据访问层 (mapper)
**ORM框架**: MyBatis
**主要映射器**:
- `TrailingProfitMapper`: 跟踪止盈数据访问
- `ExAccountMapper`: 账户信息数据访问
- `SubscribeSymbolMapper`: 订阅交易对数据访问

### 3.6 控制器层 (controller)
**架构**: Spring MVC RESTful API
**主要控制器**:
- `TrailingProfitController`: 处理策略相关的HTTP请求
- `ExAccountController`: 处理账户管理请求
- **安全控制**: 使用Shiro进行权限验证

### 3.7 任务调度模块 (task)
**StrategyTask**: 核心定时任务类
**调度频率**:
- `startFollow()`: 每10秒执行一次
- `startTraling()`: 每8秒执行一次  
- `positionUpdate()`: 每8秒执行一次
- `strategGroup()`: 每8秒执行一次

### 3.8 工具类模块 (utils)
- `OkHttpUtil`: HTTP请求封装，支持代理配置
- `AESUtil`: 敏感数据加密解密
- `GZIPUtils`: 数据压缩处理

## 4. 数据流分析

### 4.1 策略创建流程
1. 用户通过Controller创建策略
2. Controller调用Service层验证和保存
3. Service层通过Mapper持久化到数据库
4. 策略信息缓存到GlobalCache

### 4.2 策略执行流程
1. StrategyTask定时任务触发
2. 从GlobalCache获取实时价格数据
3. TrailingProfitService执行策略逻辑
4. 根据策略规则调用交易所API
5. 更新策略状态和结果

### 4.3 实时数据流程
1. WebSocket连接获取市场数据
2. 数据解析后存储到GlobalCache
3. 策略执行时从缓存读取最新数据

## 5. 关键依赖关系

### 5.1 核心依赖链
```
Controller -> Service -> Mapper -> Database
     ↓
GlobalCache <- WebSocket Data
     ↑
StrategyTask -> Service
```

### 5.2 服务间依赖
- `TrailingProfitServiceImpl` 依赖 `AccountContext`
- `AccountContext` 管理所有 `IAccountService` 实现
- 所有Service都可能依赖 `GlobalCache`

### 5.3 外部依赖
- 交易所API（币安、OKX等）
- 数据库（通过MyBatis）
- WebSocket数据源

## 6. 系统特点

### 6.1 优点
- **模块化设计**: 清晰的分层架构，易于维护
- **可扩展性**: 支持多交易所，易于添加新的交易所
- **实时性**: 通过WebSocket获取实时数据
- **安全性**: API密钥加密存储
- **容错性**: 异常处理和日志记录完善

### 6.2 技术亮点
- **策略模式**: 不同交易所的服务实现
- **工厂模式**: AccountContext管理服务实例
- **观察者模式**: 实时数据更新机制
- **定时任务**: 高频策略执行
- **缓存机制**: 提高数据访问效率

## 7. 部署和运行建议

### 7.1 环境要求
- JDK 8+
- Spring Boot 2.x
- MySQL数据库
- Redis（可选，用于分布式缓存）

### 7.2 配置要点
- 数据库连接配置
- 交易所API密钥配置
- WebSocket连接配置
- 定时任务调度配置

### 7.3 监控建议
- 策略执行状态监控
- API调用频率监控
- 系统性能监控
- 异常告警机制

## 8. API交易调用详细解析

### 8.1 交易API架构设计

系统采用**策略模式**设计，通过`IAccountService`接口统一不同交易所的API调用：

```java
// 核心接口定义
public interface IAccountService {
    JSONObject openPosition(String symbol, Float quantity, Integer leverage, PositionSideEnum positionSideEnum);
    JSONObject closePosition(String symbol, Float quantity, PositionSideEnum positionSideEnum);
    BigDecimal getBalance();
    Map<String, JSONObject> getPosition(String symbol);
    boolean transferAsset(String accountName, BigDecimal amount, TransferInternalEnum transferInternalEnum);
}
```

### 8.2 币安期货交易API实现 (BinanceFuturesService)

#### 8.2.1 API端点配置
```java
static String BASE_FTURES = "https://fapi.binance.com";
static String TRADE = "/fapi/v1/order";           // 交易订单
static String LEVERAGE = "/fapi/v1/leverage";     // 杠杆设置
static String MARGIN_TYPE = "/fapi/v1/marginType"; // 保证金模式
static String BALANCE = "/fapi/v3/balance";       // 账户余额
static String POSITION = "/fapi/v3/positionRisk"; // 仓位信息
```

#### 8.2.2 开仓交易流程

**1. 开仓前准备 (`prepareBefore`)**
```java
private boolean prepareBefore(String symbol, Integer leverage) {
    // 1. 检查交易对配置
    JSONObject symbolJson = symbolConfig(symbol);

    // 2. 设置保证金模式为逐仓
    if(!TradeModeEnum.ISOLATED_BN.getValue().equals(symbolJson.getString("tradeMode"))) {
        setTradeModeType(symbol, TradeModeEnum.ISOLATED_BN);
    }

    // 3. 设置杠杆倍数
    if(leverage != symbolJson.getInteger("leverage")) {
        setLeverage(symbol, leverage);
    }

    return isIsolated && isLeverage;
}
```

**2. 执行开仓 (`openPosition`)**
```java
public JSONObject openPosition(String symbol, Float quantity, Integer leverage, PositionSideEnum positionSideEnum) {
    // 预检查和配置
    boolean isReady = prepareBefore(symbol, leverage);
    if(!isReady) return null;

    // 构建订单参数
    String queryString = "symbol=" + symbol;
    if(positionSideEnum == PositionSideEnum.LONG) {
        // 做多：BUY + LONG + MARKET
        queryString += "&side=BUY&positionSide=LONG&type=MARKET&quantity=" + quantity;
    } else {
        // 做空：SELL + SHORT + MARKET
        queryString += "&side=SELL&positionSide=SHORT&type=MARKET&quantity=" + quantity;
    }
    queryString += "&recvWindow=" + recvWindow + "&timestamp=" + System.currentTimeMillis();

    // 生成签名
    String signature = SignatureGenerator.getSignature(queryString, secretKey);

    // 发送POST请求
    String url = BASE_FTURES + TRADE + "?" + queryString + "&signature=" + signature;
    String res = OkHttpUtil.doPost(url, reqBody, getHeader());

    // 解析响应
    JSONObject order = JSON.parseObject(res);
    return parseOrderResponse(order);
}
```

#### 8.2.3 平仓交易流程

**平仓逻辑 (`closePosition`)**
```java
public JSONObject closePosition(String symbol, Float quantity, PositionSideEnum positionSideEnum) {
    String queryString = "symbol=" + symbol;

    if(positionSideEnum == PositionSideEnum.LONG) {
        // 平多仓：SELL + LONG + MARKET
        queryString += "&side=SELL&positionSide=LONG&type=MARKET&quantity=" + quantity;
    } else {
        // 平空仓：BUY + SHORT + MARKET
        queryString += "&side=BUY&positionSide=SHORT&type=MARKET&quantity=" + quantity;
    }

    // 签名和发送请求
    String signature = SignatureGenerator.getSignature(queryString, secretKey);
    String url = BASE_FTURES + TRADE + "?" + queryString + "&signature=" + signature;
    String res = OkHttpUtil.doPost(url, reqBody, getHeader());

    return parseOrderResponse(JSON.parseObject(res));
}
```

#### 8.2.4 API认证机制

**请求头设置**
```java
private Map<String, String> getHeader() {
    Map<String, String> headerMap = new HashMap<>();
    headerMap.put("User-Agent", "binance-futures-connector-java/3.0.5");
    headerMap.put("Content-Type", "application/x-www-form-urlencoded");
    headerMap.put("X-MBX-APIKEY", apiKey);  // API密钥
    return headerMap;
}
```

**签名生成**
- 使用HMAC SHA256算法
- 签名内容：查询字符串
- 密钥：secretKey

### 8.3 策略执行中的交易调用

#### 8.3.1 策略开仓流程 (`TrailingProfitServiceImpl.openPosition`)

```java
private void openPosition(TrailingProfit trailing) {
    // 1. 获取仓位方向
    PositionSideEnum posSideEnum = PositionSideEnum.parseValue(trailing.getPositionSide());

    // 2. 通过AccountContext获取对应的账户服务
    IAccountService accountClient = accountContext.getAccountClient(
        ExchangeEnum.BINANCE,
        InstrumentEnum.FUTURES_U,
        trailing.getAccountName()
    );

    // 3. 调用开仓API
    JSONObject resJson = accountClient.openPosition(
        trailing.getSymbol(),           // 交易对
        trailing.getAmount().floatValue(), // 数量
        trailing.getLeverage(),         // 杠杆
        posSideEnum                     // 仓位方向
    );

    // 4. 处理响应结果
    if(resJson.getBoolean("isCompleted")) {
        String orderId = resJson.getString("orderId");
        trailing.setRefOrderOpen(orderId);
        trailing.setState(OrderStateEnum.HOLDING.getCode());
    } else {
        trailing.setMessage(resJson.getString("msg"));
    }

    // 5. 更新数据库
    this.updateById(trailing);
}
```

#### 8.3.2 策略平仓流程 (`TrailingBinanceServiceImpl.closeTrailing`)

```java
private void closeTrailing(IAccountService accountClient, BigDecimal curPrice,
                          PositionSideEnum positionSideEnum, OrderStateEnum stateEnum) {
    // 1. 执行平仓
    JSONObject closePosJson = accountClient.closePosition(
        trailing.getSymbol(),
        trailing.getAmount().floatValue(),
        positionSideEnum
    );

    // 2. 处理平仓结果
    if(closePosJson.getBoolean("isCompleted")) {
        String orderId = closePosJson.getString("orderId");
        trailing.setRefOrderClose(orderId);
        trailing.setState(stateEnum.getCode());
        trailingProfitService.updateById(trailing);

        // 从缓存中移除
        GlobalCache.STRATEGY_TRAILING.get(trailing.getSymbol()).remove(trailing.getId());
    }

    // 3. 二次确认仓位是否完全平仓
    Map<String, JSONObject> positionMap = accountClient.getPosition(trailing.getSymbol());
    if(positionMap.size() != 0) {
        // 如果仓位仍存在，再次尝试平仓
        JSONObject closePosJson1 = accountClient.closePosition(
            trailing.getSymbol(),
            trailing.getAmount().floatValue(),
            positionSideEnum
        );
        // 处理二次平仓结果...
    }
}
```

### 8.4 交易执行的触发机制

#### 8.4.1 定时任务触发

**StrategyTask定时任务**
```java
@Scheduled(fixedDelay = 8 * 1000)  // 每8秒执行
public void startTraling(){
    trailingProfitService.startTraling();  // 触发开仓检查
}

@Scheduled(fixedDelay = 8 * 1000)  // 每8秒执行
public void positionUpdate(){
    trailingProfitService.positionUpdate(); // 触发平仓检查
}
```

#### 8.4.2 实时价格触发

**WebSocket价格更新触发策略执行**
```java
// 价格更新后触发策略处理
public void syncHandleTrailing(String symbol) {
    Map<Long, TrailingProfit> map = GlobalCache.STRATEGY_TRAILING.get(symbol);

    for(TrailingProfit trailing: map.values()) {
        // 提交到线程池异步处理
        threadPoolService.submit(new HandleTrailingJob(trailing, trailingProfitService));
    }
}
```

### 8.5 交易安全机制

#### 8.5.1 API限流保护
- 使用`recvWindow=5000`设置请求有效期
- 通过线程池控制并发请求数量
- 异常重试机制

#### 8.5.2 订单状态验证
```java
// 订单响应解析
private JSONObject parseOrderResponse(JSONObject order) {
    JSONObject result = new JSONObject();

    if(order.getLong("code") != null) {
        // API返回错误
        result.put("isCompleted", false);
        result.put("msg", order.getString("msg"));
    } else {
        // 订单成功
        result.put("isCompleted", true);
        result.put("orderId", order.getString("orderId"));
    }

    return result;
}
```

#### 8.5.3 仓位二次确认
- 平仓后检查仓位是否真正关闭
- 如有残余仓位，执行二次平仓
- 确保策略执行的完整性

### 8.6 现货交易API (BinanceSpotService)

#### 8.6.1 资产转账功能
```java
public boolean transferAsset(BigDecimal amount, TransferInternalEnum transferInternalEnum) {
    String queryString = "type=" + transferInternalEnum.getValue() +
                        "&asset=USDT&amount=" + amount +
                        "&recvWindow=5000&timestamp=" + System.currentTimeMillis();

    String signature = SignatureGenerator.getSignature(queryString, secretKey);
    String url = BASE_SPOT + TRANSFER_INTERNAL + "?" + queryString + "&signature=" + signature;
    String res = OkHttpUtil.doPost(url, reqBody, getHeader());

    JSONObject order = JSON.parseObject(res);
    Long tranId = order.getLong("tranId");
    return tranId != null && tranId > 0;
}
```

### 8.7 交易流程总结

**完整的交易执行链路**：
1. **策略触发** → 定时任务或价格变化触发
2. **账户获取** → 通过AccountContext获取对应账户服务
3. **参数准备** → 构建交易参数（交易对、数量、杠杆等）
4. **API调用** → 发送HTTP请求到交易所
5. **响应处理** → 解析API响应，更新订单状态
6. **状态同步** → 更新数据库和缓存
7. **异常处理** → 记录日志，处理失败情况

**关键技术特点**：
- **异步执行**：使用线程池处理交易请求
- **状态管理**：完整的订单状态流转
- **容错机制**：API调用失败重试和异常处理
- **实时性**：基于WebSocket的价格更新触发
- **安全性**：API签名认证和参数验证

---

*本文档基于代码分析生成，建议结合实际业务需求进行详细设计和实现。*

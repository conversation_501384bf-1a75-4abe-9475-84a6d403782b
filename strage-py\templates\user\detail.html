<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户详细</title>
    <!-- CSS文件 - 使用CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet" />

    <style>
        body {
            background-color: #f8f8f9;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }

        .main-content {
            padding: 20px;
            background-color: #fff;
            margin: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .form-header {
            color: #676a6c;
            border-bottom: 1px solid #e7eaec;
            padding-bottom: 10px;
            margin-bottom: 20px;
            margin-top: 30px;
        }

        .form-header:first-child {
            margin-top: 0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .control-label {
            font-weight: 600;
            color: #676a6c;
            text-align: right;
            padding-top: 7px;
        }

        .form-control-plaintext {
            padding-top: 7px;
            padding-bottom: 7px;
            margin-bottom: 0;
            line-height: 1.42857143;
            color: #555;
            background-color: transparent;
            border: none;
            border-radius: 0;
        }

        .status-normal {
            color: #1ab394;
            font-weight: 600;
        }

        .status-disabled {
            color: #ed5565;
            font-weight: 600;
        }

        .no-data {
            color: #999;
            font-style: italic;
        }
    </style>
</head>

<body>
    <div class="main-content">
        <form class="form-horizontal">
            <h4 class="form-header h4">基本信息</h4>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">用户名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.user_name or '未设置' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">用户性别：</label>
                        <div class="col-sm-8">
                            {% if user.sex == '0' %}
                            <p class="form-control-plaintext">男</p>
                            {% elif user.sex == '1' %}
                            <p class="form-control-plaintext">女</p>
                            {% else %}
                            <p class="form-control-plaintext">未知</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">手机号码：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.phonenumber or '未设置' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">邮箱：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.email or '未设置' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">登录账号：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.login_name }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">用户状态：</label>
                        <div class="col-sm-8">
                            {% if user.status == '0' %}
                            <p class="form-control-plaintext status-normal">正常</p>
                            {% else %}
                            <p class="form-control-plaintext status-disabled">停用</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>


            <h4 class="form-header h4">其他信息</h4>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">创建者：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.create_by or '系统' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">创建时间：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.create_time or '未知' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">更新者：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.update_by or '未更新' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">更新时间：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.update_time or '未更新' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">最后登录IP：</label>
                        <div class="col-sm-8">
                            <p class="form-control-plaintext">{{ user.login_ip or '未登录' }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-xs-2 control-label">备注：</label>
                        <div class="col-xs-10">
                            <p class="form-control-plaintext">{{ user.remark or '无备注' }}</p>
                        </div>
                    </div>
                </div>
            </div>

        </form>
    </div>

    <!-- JS文件 - 使用CDN -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
</body>

</html>
"""
订阅交易对数据访问层
迁移自: com.project.strategy.mapper.SubscribeSymbolMapper
"""
import logging
from typing import List, Optional
from datetime import datetime
from dao.base_dao import BaseDAO
from ..domain.entity.subscribe_symbol import SubscribeSymbol

logger = logging.getLogger(__name__)


class SubscribeSymbolDAO(BaseDAO):
    """订阅交易对数据访问对象"""
    
    def __init__(self):
        """初始化订阅交易对DAO"""
        logger.info("SubscribeSymbolDAO initialized")
    
    def select_subscribe_symbol_by_id(self, id: int) -> Optional[SubscribeSymbol]:
        """
        根据ID查询订阅交易对
        
        Args:
            id: 订阅ID
            
        Returns:
            订阅交易对对象，不存在返回None
        """
        try:
            sql = "SELECT * FROM subscribe_symbol WHERE id = %s"
            results = self.execute_query(sql, (id,))
            
            if results:
                row = results[0]
                return self._row_to_entity(row)
            return None
        except Exception as e:
            logger.error(f"根据ID查询订阅交易对失败: {e}")
            return None
    
    def select_subscribe_symbol_list(self, subscribe_symbol: Optional[SubscribeSymbol] = None) -> List[SubscribeSymbol]:
        """
        查询订阅交易对列表
        
        Args:
            subscribe_symbol: 查询条件对象
            
        Returns:
            订阅交易对列表
        """
        try:
            sql = "SELECT * FROM subscribe_symbol"
            params = []
            where_conditions = []
            
            if subscribe_symbol:
                if subscribe_symbol.platform:
                    where_conditions.append("platform LIKE %s")
                    params.append(f"%{subscribe_symbol.platform}%")
                if subscribe_symbol.symbol:
                    where_conditions.append("symbol LIKE %s")
                    params.append(f"%{subscribe_symbol.symbol}%")
                if subscribe_symbol.state is not None:
                    where_conditions.append("state = %s")
                    params.append(subscribe_symbol.state)
                if subscribe_symbol.message:
                    where_conditions.append("message LIKE %s")
                    params.append(f"%{subscribe_symbol.message}%")
            
            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)
            
            sql += " ORDER BY id DESC"
            logger.info(f"SQL: {sql}, params: {params}")
            results = self.execute_query(sql, params if params else None)
            
            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))
            
            return entities
        except Exception as e:
            logger.error(f"查询订阅交易对列表失败: {e}")
            return []
    
    def insert_subscribe_symbol(self, subscribe_symbol: SubscribeSymbol) -> int:
        """
        插入订阅交易对

        Args:
            subscribe_symbol: 订阅交易对对象

        Returns:
            插入的记录数量
        """
        try:
            sql = """
                INSERT INTO subscribe_symbol (platform, symbol, state, message, create_time, update_time)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            params = (
                subscribe_symbol.platform,
                subscribe_symbol.symbol,
                subscribe_symbol.state,
                subscribe_symbol.message,
                subscribe_symbol.create_time or datetime.now(),
                subscribe_symbol.update_time or datetime.now()
            )

            result = self.execute_insert(sql, params)
            if result:
                subscribe_symbol.id = result
                return 1
            return 0
        except Exception as e:
            logger.error(f"插入订阅交易对失败: {e}")
            return 0
    
    def update_subscribe_symbol(self, subscribe_symbol: SubscribeSymbol) -> int:
        """
        更新订阅交易对

        Args:
            subscribe_symbol: 订阅交易对对象

        Returns:
            更新的记录数量
        """
        try:
            sql = """
                UPDATE subscribe_symbol
                SET platform = %s, symbol = %s, state = %s, message = %s, update_time = %s
                WHERE id = %s
            """
            params = (
                subscribe_symbol.platform,
                subscribe_symbol.symbol,
                subscribe_symbol.state,
                subscribe_symbol.message,
                subscribe_symbol.update_time or datetime.now(),
                subscribe_symbol.id
            )

            affected_rows = self.execute_update(sql, params)
            return affected_rows
        except Exception as e:
            logger.error(f"更新订阅交易对失败: {e}")
            return 0
    
    def delete_subscribe_symbol_by_id(self, id: int) -> int:
        """
        根据ID删除订阅交易对

        Args:
            id: 订阅ID

        Returns:
            删除的记录数量
        """
        try:
            sql = "DELETE FROM subscribe_symbol WHERE id = %s"
            affected_rows = self.execute_update(sql, (id,))
            return affected_rows
        except Exception as e:
            logger.error(f"根据ID删除订阅交易对失败: {e}")
            return 0
    
    def delete_subscribe_symbol_by_ids(self, ids: str) -> int:
        """
        根据ID列表批量删除订阅交易对

        Args:
            ids: ID字符串，逗号分隔

        Returns:
            删除的记录数量
        """
        try:
            if not ids or not ids.strip():
                return 0

            # 将逗号分隔的字符串转换为列表
            id_list = [id_str.strip() for id_str in ids.split(',') if id_str.strip()]

            if not id_list:
                return 0

            # 构建IN子句的占位符
            placeholders = ','.join(['%s'] * len(id_list))
            sql = f"DELETE FROM subscribe_symbol WHERE id IN ({placeholders})"

            affected_rows = self.execute_update(sql, id_list)
            return affected_rows
        except Exception as e:
            logger.error(f"批量删除订阅交易对失败: {e}")
            return 0
    
    def _row_to_entity(self, row: dict) -> SubscribeSymbol:
        """
        将数据库行转换为实体对象
        
        Args:
            row: 数据库行字典
            
        Returns:
            订阅交易对实体对象
        """
        return SubscribeSymbol(
            id=row.get('id'),
            platform=row.get('platform'),
            symbol=row.get('symbol'),
            state=row.get('state'),
            message=row.get('message'),
            create_time=row.get('create_time'),
            update_time=row.get('update_time')
        )

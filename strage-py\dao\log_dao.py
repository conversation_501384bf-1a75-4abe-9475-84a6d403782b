"""
日志数据访问层
处理操作日志和登录日志相关的数据库操作
"""
import logging
from datetime import datetime
from dao.base_dao import BaseDAO
from models.log import OperLog, LoginLog

logger = logging.getLogger(__name__)

class OperLogDAO(BaseDAO):
    """操作日志数据访问对象"""
    
    @staticmethod
    def select_oper_log_list(page=1, size=10, title=None, oper_name=None, oper_ip=None, 
                           status=None, business_types=None, begin_time=None, end_time=None):
        """分页查询操作日志列表"""
        try:
            # 构建查询条件
            conditions = {}
            
            if title:
                conditions['title_like'] = title
            if oper_name:
                conditions['oper_name_like'] = oper_name
            if oper_ip:
                conditions['oper_ip_like'] = oper_ip
            if status is not None:
                conditions['status'] = status
            if business_types:
                conditions['business_type_in'] = business_types
            if begin_time:
                conditions['oper_time_gte'] = begin_time
            if end_time:
                conditions['oper_time_lte'] = end_time
            
            where_clause, where_params = BaseDAO.build_where_clause(conditions, [])
            
            # 查询总数
            total = BaseDAO.get_total_count('sys_oper_log', where_clause, where_params)
            
            # 查询数据
            base_sql = """
                SELECT oper_id, title, business_type, method, request_method, oper_name,
                       oper_url, oper_ip, oper_location, oper_param, json_result,
                       status, error_msg, oper_time, cost_time
                FROM sys_oper_log
            """
            if where_clause:
                base_sql += f" WHERE {where_clause}"
            base_sql += " ORDER BY oper_time DESC"
            
            paginated_sql, pagination_params = BaseDAO.build_pagination_sql(base_sql, page, size)
            all_params = where_params + pagination_params
            
            rows = BaseDAO.execute_query(paginated_sql, all_params)
            
            # 转换为OperLog对象
            logs = []
            for row in rows:
                log = OperLog()
                log.oper_id = row['oper_id']
                log.title = row['title']
                log.business_type = row['business_type']
                log.method = row['method']
                log.request_method = row['request_method']
                log.oper_name = row['oper_name']
                log.oper_url = row['oper_url']
                log.oper_ip = row['oper_ip']
                log.oper_location = row['oper_location']
                log.oper_param = row['oper_param']
                log.json_result = row['json_result']
                log.status = row['status']
                log.error_msg = row['error_msg']
                log.oper_time = row['oper_time']
                log.cost_time = row['cost_time']
                logs.append(log)
            
            return {
                'total': total,
                'rows': logs
            }
            
        except Exception as e:
            logger.error(f"查询操作日志列表失败: {e}")
            raise
    
    @staticmethod
    def export_oper_log(title=None, oper_name=None, oper_ip=None, status=None, 
                       business_types=None, begin_time=None, end_time=None):
        """导出操作日志"""
        try:
            # 构建查询条件
            conditions = {}
            
            if title:
                conditions['title_like'] = title
            if oper_name:
                conditions['oper_name_like'] = oper_name
            if oper_ip:
                conditions['oper_ip_like'] = oper_ip
            if status is not None:
                conditions['status'] = status
            if business_types:
                conditions['business_type_in'] = business_types
            if begin_time:
                conditions['oper_time_gte'] = begin_time
            if end_time:
                conditions['oper_time_lte'] = end_time
            
            where_clause, where_params = BaseDAO.build_where_clause(conditions, [])
            
            # 查询数据
            sql = """
                SELECT oper_id, title, business_type, oper_name, oper_ip,
                       status, oper_time, cost_time
                FROM sys_oper_log
            """
            if where_clause:
                sql += f" WHERE {where_clause}"
            sql += " ORDER BY oper_time DESC"
            
            rows = BaseDAO.execute_query(sql, where_params)
            
            # 转换为OperLog对象
            logs = []
            for row in rows:
                log = OperLog()
                log.oper_id = row['oper_id']
                log.title = row['title']
                log.business_type = row['business_type']
                log.oper_name = row['oper_name']
                log.oper_ip = row['oper_ip']
                log.status = row['status']
                log.oper_time = row['oper_time']
                log.cost_time = row['cost_time']
                logs.append(log)
            
            return logs

        except Exception as e:
            logger.error(f"导出操作日志失败: {e}")
            raise

    @staticmethod
    def select_oper_log_by_id(oper_id):
        """根据ID查询操作日志"""
        try:
            sql = """
                SELECT oper_id, title, business_type, method, request_method, oper_name,
                       oper_url, oper_ip, oper_location, oper_param, json_result,
                       status, error_msg, oper_time, cost_time
                FROM sys_oper_log
                WHERE oper_id = %s
            """
            rows = BaseDAO.execute_query(sql, [oper_id])

            if rows:
                row = rows[0]
                log = OperLog()
                log.oper_id = row['oper_id']
                log.title = row['title']
                log.business_type = row['business_type']
                log.method = row['method']
                log.request_method = row['request_method']
                log.oper_name = row['oper_name']
                log.oper_url = row['oper_url']
                log.oper_ip = row['oper_ip']
                log.oper_location = row['oper_location']
                log.oper_param = row['oper_param']
                log.json_result = row['json_result']
                log.status = row['status']
                log.error_msg = row['error_msg']
                log.oper_time = row['oper_time']
                log.cost_time = row['cost_time']
                return log

            return None

        except Exception as e:
            logger.error(f"根据ID查询操作日志失败: {e}")
            raise

    @staticmethod
    def insert_oper_log(oper_log):
        """插入操作日志"""
        try:
            sql = """
                INSERT INTO sys_oper_log (title, business_type, method, request_method,
                                        oper_name, oper_url, oper_ip, oper_location,
                                        oper_param, json_result, status, error_msg,
                                        oper_time, cost_time)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = [
                oper_log.title,
                oper_log.business_type,
                oper_log.method,
                oper_log.request_method,
                oper_log.oper_name,
                oper_log.oper_url,
                oper_log.oper_ip,
                oper_log.oper_location,
                oper_log.oper_param,
                oper_log.json_result,
                oper_log.status,
                oper_log.error_msg,
                oper_log.oper_time,
                oper_log.cost_time
            ]

            return BaseDAO.execute_insert(sql, params)

        except Exception as e:
            logger.error(f"插入操作日志失败: {e}")
            raise
    
    @staticmethod
    def insert_oper_log(log):
        """插入操作日志"""
        try:
            sql = """
                INSERT INTO sys_oper_log (title, business_type, method, request_method, 
                                        oper_name, oper_url, oper_ip, oper_location, 
                                        oper_param, json_result, status, error_msg, 
                                        oper_time, cost_time)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = [
                log.title, log.business_type, log.method, log.request_method,
                log.oper_name, log.oper_url, log.oper_ip, log.oper_location,
                log.oper_param, log.json_result, log.status, log.error_msg,
                log.oper_time, log.cost_time
            ]
            
            return BaseDAO.execute_insert(sql, params)
            
        except Exception as e:
            logger.error(f"插入操作日志失败: {e}")
            raise
    
    @staticmethod
    def delete_oper_log_by_ids(oper_ids):
        """批量删除操作日志"""
        try:
            if not oper_ids:
                return 0
            
            placeholders = ','.join(['%s'] * len(oper_ids))
            sql = f"DELETE FROM sys_oper_log WHERE oper_id IN ({placeholders})"
            
            return BaseDAO.execute_update(sql, list(oper_ids))
            
        except Exception as e:
            logger.error(f"批量删除操作日志失败: {e}")
            raise
    
    @staticmethod
    def clean_oper_log():
        """清空操作日志"""
        try:
            sql = "DELETE FROM sys_oper_log"
            return BaseDAO.execute_update(sql)
            
        except Exception as e:
            logger.error(f"清空操作日志失败: {e}")
            raise


class LoginLogDAO(BaseDAO):
    """登录日志数据访问对象"""
    
    @staticmethod
    def select_login_log_list(page=1, size=10, login_name=None, status=None, 
                            begin_time=None, end_time=None):
        """分页查询登录日志列表"""
        try:
            # 构建查询条件
            conditions = {}
            
            if login_name:
                conditions['login_name_like'] = login_name
            if status is not None:
                conditions['status'] = status
            if begin_time:
                conditions['login_time_gte'] = begin_time
            if end_time:
                conditions['login_time_lte'] = end_time
            
            where_clause, where_params = BaseDAO.build_where_clause(conditions, [])
            
            # 查询总数
            total = BaseDAO.get_total_count('sys_logininfor', where_clause, where_params)
            
            # 查询数据
            base_sql = """
                SELECT info_id, login_name, ipaddr, login_location, browser, os,
                       status, msg, login_time
                FROM sys_logininfor
            """
            if where_clause:
                base_sql += f" WHERE {where_clause}"
            base_sql += " ORDER BY login_time DESC"
            
            paginated_sql, pagination_params = BaseDAO.build_pagination_sql(base_sql, page, size)
            all_params = where_params + pagination_params
            
            rows = BaseDAO.execute_query(paginated_sql, all_params)
            
            # 转换为LoginLog对象
            logs = []
            for row in rows:
                log = LoginLog()
                log.info_id = row['info_id']
                log.login_name = row['login_name']
                log.ipaddr = row['ipaddr']
                log.login_location = row['login_location']
                log.browser = row['browser']
                log.os = row['os']
                log.status = row['status']
                log.msg = row['msg']
                log.login_time = row['login_time']
                logs.append(log)
            
            return {
                'total': total,
                'rows': logs
            }
            
        except Exception as e:
            logger.error(f"查询登录日志列表失败: {e}")
            raise
    
    @staticmethod
    def insert_login_log(login_name, ipaddr, browser, os, status, msg):
        """插入登录日志"""
        try:
            sql = """
                INSERT INTO sys_logininfor (login_name, ipaddr, login_location, browser, 
                                          os, status, msg, login_time)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            login_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 这里可以根据IP获取地理位置，暂时设为空
            login_location = ""
            
            params = [login_name, ipaddr, login_location, browser, os, status, msg, login_time]
            
            return BaseDAO.execute_insert(sql, params)
            
        except Exception as e:
            logger.error(f"插入登录日志失败: {e}")
            raise
    
    @staticmethod
    def delete_login_log_by_ids(info_ids):
        """批量删除登录日志"""
        try:
            if not info_ids:
                return 0
            
            placeholders = ','.join(['%s'] * len(info_ids))
            sql = f"DELETE FROM sys_logininfor WHERE info_id IN ({placeholders})"
            
            return BaseDAO.execute_update(sql, list(info_ids))
            
        except Exception as e:
            logger.error(f"批量删除登录日志失败: {e}")
            raise
    
    @staticmethod
    def clean_login_log():
        """清空登录日志"""
        try:
            sql = "DELETE FROM sys_logininfor"
            return BaseDAO.execute_update(sql)
            
        except Exception as e:
            logger.error(f"清空登录日志失败: {e}")
            raise

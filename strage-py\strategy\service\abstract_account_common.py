"""
抽象账户通用类
迁移自: com.project.strategy.service.AbstractAccountCommon
"""
import logging
from decimal import Decimal
from typing import Dict, Any

from .i_account_service import IAccountService
from ..domain.entity.trailing_profit import TrailingProfit
from ..enums import ExchangeEnum, InstrumentEnum, PositionSideEnum, TradeModeEnum, TransferInternalEnum

logger = logging.getLogger(__name__)


class AbstractAccountCommon(IAccountService):
    """抽象账户通用类 - 提供默认的不支持实现"""
    
    def fill_position(self, trailing: TrailingProfit) -> None:
        """
        填充持仓信息 - 默认不支持
        
        Args:
            trailing: 跟踪盈利对象
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def get_position(self, symbol: str) -> Dict[str, Dict[str, Any]]:
        """
        获取持仓信息 - 默认不支持
        
        Args:
            symbol: 交易对
            
        Returns:
            持仓信息字典
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def close_position(self, symbol: str, quantity: float, position_side: PositionSideEnum) -> Dict[str, Any]:
        """
        平仓 - 默认不支持
        
        Args:
            symbol: 交易对
            quantity: 数量
            position_side: 持仓方向
            
        Returns:
            平仓结果
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def open_position(self, symbol: str, quantity: float, leverage: int, position_side: PositionSideEnum) -> Dict[str, Any]:
        """
        开仓 - 默认不支持
        
        Args:
            symbol: 交易对
            quantity: 数量
            leverage: 杠杆
            position_side: 持仓方向
            
        Returns:
            开仓结果
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def set_trade_mode_type(self, symbol: str, margin_type: TradeModeEnum) -> Dict[str, Any]:
        """
        设置交易模式类型 - 默认不支持
        
        Args:
            symbol: 交易对
            margin_type: 保证金类型
            
        Returns:
            设置结果
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def set_leverage(self, symbol: str, leverage_level: int) -> Dict[str, Any]:
        """
        设置杠杆 - 默认不支持
        
        Args:
            symbol: 交易对
            leverage_level: 杠杆倍数
            
        Returns:
            设置结果
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def transfer_asset(self, account_name: str, amount: Decimal, transfer_internal_enum: TransferInternalEnum) -> bool:
        """
        资产转账 - 默认不支持
        
        Args:
            account_name: 账户名称
            amount: 转账金额
            transfer_internal_enum: 转账类型
            
        Returns:
            是否转账成功
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def get_balance(self) -> Decimal:
        """
        获取余额 - 默认不支持
        
        Returns:
            账户余额
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def is_support(self) -> bool:
        """
        是否支持 - 默认不支持
        
        Returns:
            是否支持此服务
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def get_exchange(self) -> ExchangeEnum:
        """
        获取交易所 - 默认不支持
        
        Returns:
            交易所枚举
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def get_instrument(self) -> InstrumentEnum:
        """
        获取交易工具 - 默认不支持
        
        Returns:
            交易工具枚举
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def get_account_name(self) -> str:
        """
        获取账户名称 - 默认不支持
        
        Returns:
            账户名称
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def get_account(self) -> str:
        """
        获取账户 - 默认不支持
        
        Returns:
            账户信息
            
        Raises:
            RuntimeError: 不支持此方法
        """
        raise RuntimeError("not support this method!")
    
    def get_header(self) -> Dict[str, str]:
        """
        获取HTTP请求头 - 通用方法
        
        Returns:
            请求头字典
        """
        headers = {}
        
        # 如果有API密钥，添加到请求头
        if hasattr(self, 'api_key') and self.api_key:
            headers['X-MBX-APIKEY'] = self.api_key
        
        # 添加通用请求头
        headers['Content-Type'] = 'application/json'
        headers['User-Agent'] = 'python-binance-client'
        
        return headers

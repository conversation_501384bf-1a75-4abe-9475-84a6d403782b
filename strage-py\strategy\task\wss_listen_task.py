"""
WebSocket监听任务类
迁移自: com.project.strategy.task.WSSListenTask
"""
import asyncio
import logging
import threading
import time
from typing import Optional


logger = logging.getLogger(__name__)


class WSSListenTask:
    """WebSocket监听任务类 - 负责监听WebSocket数据流"""
    
    def __init__(self):
        """初始化WebSocket监听任务"""
        self._binance_stream_service = None
        self._is_running = False
        self._listen_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        logger.info("WSSListenTask initialized")
    
    def set_binance_stream_service(self, service) -> None:
        """
        设置Binance流服务
        
        Args:
            service: Binance流服务实例
        """
        self._binance_stream_service = service
        logger.info("设置Binance流服务")
    
    def listen_depth(self) -> None:
        """
        监听深度数据
        对应Java中的@Scheduled(fixedDelay = 10 * 1000) listenDepth()
        """
        try:
            if self._binance_stream_service is None:
                logger.warning("Binance流服务未设置，跳过深度监听任务")
                return
            
            logger.debug("执行深度监听任务...")
            self._binance_stream_service.symbol_subscribe()
            logger.debug("深度监听任务执行完成")
            
        except Exception as e:
            logger.error(f"WSSListenTask error: {e}", exc_info=True)
    
    def _run_listen_loop(self) -> None:
        """
        运行监听循环
        """
        logger.info("启动WebSocket监听循环，间隔: 10秒")
        
        while not self._stop_event.is_set():
            try:
                start_time = time.time()
                self.listen_depth()
                execution_time = time.time() - start_time
                
                # 计算下次执行时间（固定延迟10秒）
                sleep_time = max(0, 10.0 - execution_time)
                if sleep_time > 0:
                    self._stop_event.wait(sleep_time)
                    
            except Exception as e:
                logger.error(f"WebSocket监听循环异常: {e}", exc_info=True)
                # 异常后等待一段时间再继续
                self._stop_event.wait(5.0)
        
        logger.info("WebSocket监听循环已停止")
    
    def start_listen_task(self) -> None:
        """启动WebSocket监听任务"""
        if self._is_running:
            logger.warning("WebSocket监听任务已在运行中")
            return
        
        self._is_running = True
        self._stop_event.clear()
        
        # 启动监听线程
        self._listen_thread = threading.Thread(
            target=self._run_listen_loop,
            name="WSSListenTask-listen_depth",
            daemon=True
        )
        self._listen_thread.start()
        
        logger.info("WebSocket监听任务已启动")
    
    def stop_listen_task(self) -> None:
        """停止WebSocket监听任务"""
        if not self._is_running:
            logger.warning("WebSocket监听任务未在运行")
            return
        
        logger.info("正在停止WebSocket监听任务...")
        self._stop_event.set()
        
        # 等待线程结束
        if self._listen_thread:
            try:
                self._listen_thread.join(timeout=5.0)
                if self._listen_thread.is_alive():
                    logger.warning("WebSocket监听任务未能在5秒内停止")
            except Exception as e:
                logger.error(f"停止WebSocket监听任务时出错: {e}")
        
        self._listen_thread = None
        self._is_running = False
        logger.info("WebSocket监听任务已停止")
    
    def is_running(self) -> bool:
        """检查任务是否在运行"""
        return self._is_running
    
    def get_task_status(self) -> dict:
        """
        获取任务状态
        
        Returns:
            任务状态字典
        """
        status = {
            'is_running': self._is_running,
            'thread_alive': self._listen_thread.is_alive() if self._listen_thread else False,
            'thread_name': self._listen_thread.name if self._listen_thread else None,
            'service_available': self._binance_stream_service is not None
        }
        
        return status
    
    def execute_single_listen(self) -> bool:
        """
        执行单次监听（用于测试或手动触发）
        
        Returns:
            是否执行成功
        """
        try:
            logger.info("手动执行深度监听任务")
            self.listen_depth()
            return True
        except Exception as e:
            logger.error(f"手动执行深度监听任务失败: {e}", exc_info=True)
            return False
    
    def restart_listen_task(self) -> None:
        """重启WebSocket监听任务"""
        logger.info("重启WebSocket监听任务")
        self.stop_listen_task()
        time.sleep(1)  # 等待1秒确保完全停止
        self.start_listen_task()
    
    def get_service_info(self) -> dict:
        """
        获取服务信息
        
        Returns:
            服务信息字典
        """
        info = {
            'binance_stream_service_set': self._binance_stream_service is not None,
            'service_type': type(self._binance_stream_service).__name__ if self._binance_stream_service else None
        }
        
        # 如果服务有额外的状态信息，可以在这里获取
        if self._binance_stream_service and hasattr(self._binance_stream_service, 'get_status'):
            try:
                info['service_status'] = self._binance_stream_service.get_status()
            except Exception as e:
                logger.error(f"获取服务状态失败: {e}")
                info['service_status'] = 'error'
        
        return info
    
    def health_check(self) -> dict:
        """
        健康检查
        
        Returns:
            健康检查结果
        """
        health = {
            'healthy': True,
            'issues': []
        }
        
        # 检查服务是否设置
        if self._binance_stream_service is None:
            health['healthy'] = False
            health['issues'].append('Binance流服务未设置')
        
        # 检查任务是否正常运行
        if self._is_running and (not self._listen_thread or not self._listen_thread.is_alive()):
            health['healthy'] = False
            health['issues'].append('监听线程已停止但任务状态显示运行中')
        
        # 检查停止事件状态
        if self._stop_event.is_set() and self._is_running:
            health['healthy'] = False
            health['issues'].append('停止事件已设置但任务状态显示运行中')
        
        return health
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"WSSListenTask(running={self._is_running})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"WSSListenTask(is_running={self._is_running}, "
                f"thread_alive={self._listen_thread.is_alive() if self._listen_thread else False})")


# 全局WebSocket监听任务实例
wss_listen_task = WSSListenTask()

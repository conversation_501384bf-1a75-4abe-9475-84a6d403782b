<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅交易对列表</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.min.css') }}" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/bootstrap-table.min.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId" class="form-horizontal">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <i class="fa fa-search"></i> 搜索条件
                            </h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="control-label">平台：</label>
                                        <input type="text" name="platform" class="form-control" placeholder="请输入平台" />
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="control-label">币对名：</label>
                                        <input type="text" name="symbol" class="form-control" placeholder="请输入币对名" />
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="control-label">状态：</label>
                                        <select name="state" class="form-control">
                                            <option value="">请选择状态</option>
                                            <option value="1">开启订阅</option>
                                            <option value="0">不开启</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="control-label">描述信息：</label>
                                        <input type="text" name="message" class="form-control" placeholder="请输入描述信息" />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="control-label" style="visibility: hidden;">操作：</label>
                                        <div>
                                            <button type="button" class="btn btn-primary btn-sm"
                                                onclick="searchTable()">
                                                <i class="fa fa-search"></i> 搜索
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm" onclick="resetForm()">
                                                <i class="fa fa-refresh"></i> 重置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="showAddModal()">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="showEditModal()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="removeSelected()">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="exportData()">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <!-- Bootstrap Table -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/bootstrap-table.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.18.3/locale/bootstrap-table-zh-CN.min.js"></script>
    <!-- 通用JS -->
    <script src="{{ url_for('static', filename='ruoyi/js/common.js') }}"></script>
    <script src="{{ url_for('static', filename='ruoyi/js/ry-ui.js') }}"></script>

    <script>
        var editFlag = true;  // 编辑权限
        var removeFlag = true;  // 删除权限
        var prefix = "/strategy/symbol";

        $(function () {
            // 直接初始化Bootstrap Table，不依赖$.table.init
            $('#bootstrap-table').bootstrapTable({
                url: prefix + "/list",
                method: 'post',
                sidePagination: "server",
                pagination: true,
                pageSize: 10,
                pageList: [10, 25, 50],
                search: false,
                showColumns: true,
                showRefresh: true,
                showToggle: true,
                clickToSelect: true,
                toolbar: "#toolbar",
                queryParams: function (params) {
                    return {
                        pageNum: params.offset / params.limit + 1,
                        pageSize: params.limit,
                        platform: $('input[name="platform"]').val(),
                        symbol: $('input[name="symbol"]').val(),
                        state: $('select[name="state"]').val(),
                        message: $('input[name="message"]').val()
                    };
                },
                responseHandler: function (res) {
                    return {
                        total: res.total,
                        rows: res.rows
                    };
                },
                onCheck: function (row) {
                    updateToolbarButtons();
                },
                onUncheck: function (row) {
                    updateToolbarButtons();
                },
                onCheckAll: function (rows) {
                    updateToolbarButtons();
                },
                onUncheckAll: function (rows) {
                    updateToolbarButtons();
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'platform',
                    title: '平台'
                },
                {
                    field: 'symbol',
                    title: '交易所对应币对名'
                },
                {
                    field: 'state',
                    title: '状态',
                    formatter: function (value, row, index) {
                        if (value == 1) {
                            return '<span class="label label-success">1：开启订阅</span>';
                        } else {
                            return '<span class="label label-danger">0：不开启</span>';
                        }
                    }
                },
                {
                    field: 'message',
                    title: '描述信息'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (editFlag) {
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editRowInline(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        }
                        if (removeFlag) {
                            actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="removeRowInline(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
            });
        });

        // 更新工具栏按钮状态
        function updateToolbarButtons() {
            var selections = $('#bootstrap-table').bootstrapTable('getSelections');
            var selectedCount = selections.length;

            // 修改按钮：只有选择一行时才启用
            if (selectedCount === 1) {
                $('.btn-primary.single').removeClass('disabled');
            } else {
                $('.btn-primary.single').addClass('disabled');
            }

            // 删除按钮：选择一行或多行时启用
            if (selectedCount > 0) {
                $('.btn-danger.multiple').removeClass('disabled');
            } else {
                $('.btn-danger.multiple').addClass('disabled');
            }
        }

        // 搜索功能
        function searchTable() {
            $('#bootstrap-table').bootstrapTable('refresh');
        }

        // 重置功能
        function resetForm() {
            $('#formId')[0].reset();
            $('#bootstrap-table').bootstrapTable('refresh');
        }

        // 添加功能已移至弹窗模式

        // 编辑功能已移至弹窗模式

        // 删除功能
        function removeRow(id) {
            if (confirm('确定删除该条记录吗？')) {
                $.ajax({
                    url: prefix + "/remove",
                    type: "POST",
                    data: { ids: id },
                    success: function (result) {
                        if (result.code == 0) {
                            alert('删除成功');
                            $('#bootstrap-table').bootstrapTable('refresh');
                        } else {
                            alert(result.msg || '删除失败');
                        }
                    },
                    error: function () {
                        alert('删除失败');
                    }
                });
            }
        }

        // 编辑功能已移至弹窗模式

        // 删除选中的记录
        function removeSelected() {
            // 检查按钮是否被禁用
            if ($('.btn-danger.multiple').hasClass('disabled')) {
                return;
            }

            var selections = $('#bootstrap-table').bootstrapTable('getSelections');
            if (selections.length == 0) {
                alert('请选择要删除的记录');
                return;
            }
            var ids = selections.map(function (item) { return item.id; }).join(',');
            if (confirm('确定删除选中的 ' + selections.length + ' 条记录吗？')) {
                $.ajax({
                    url: prefix + "/remove",
                    type: "POST",
                    data: { ids: ids },
                    success: function (result) {
                        if (result.code == 0) {
                            alert('删除成功');
                            $('#bootstrap-table').bootstrapTable('refresh');
                        } else {
                            alert(result.msg || '删除失败');
                        }
                    },
                    error: function () {
                        alert('删除失败');
                    }
                });
            }
        }

        // 导出数据
        function exportData() {
            alert('导出功能待实现');
        }

        // 显示添加弹窗
        function showAddModal() {
            $('#addModal').modal('show');
            $('#addForm')[0].reset();
        }

        // 显示编辑弹窗
        function showEditModal() {
            var rows = $('#bootstrap-table').bootstrapTable('getSelections');
            if (rows.length != 1) {
                alert('请选择一条记录');
                return;
            }
            var row = rows[0];
            $('#editModal').modal('show');
            $('#editForm input[name="id"]').val(row.id);
            $('#editForm select[name="platform"]').val(row.platform);
            $('#editForm input[name="symbol"]').val(row.symbol);
            $('#editForm select[name="state"]').val(row.state);
            $('#editForm input[name="message"]').val(row.message);
        }

        // 提交添加表单
        function submitAddForm() {
            var formData = $('#addForm').serialize();
            $.ajax({
                url: prefix + "/add",
                type: "POST",
                data: formData,
                success: function (result) {
                    if (result.code == 0) {
                        alert('添加成功');
                        $('#addModal').modal('hide');
                        $('#bootstrap-table').bootstrapTable('refresh');
                    } else {
                        alert(result.msg || '添加失败');
                    }
                },
                error: function () {
                    alert('添加失败');
                }
            });
        }

        // 提交编辑表单
        function submitEditForm() {
            var formData = $('#editForm').serialize();
            $.ajax({
                url: prefix + "/edit",
                type: "POST",
                data: formData,
                success: function (result) {
                    if (result.code == 0) {
                        alert('修改成功');
                        $('#editModal').modal('hide');
                        $('#bootstrap-table').bootstrapTable('refresh');
                    } else {
                        alert(result.msg || '修改失败');
                    }
                },
                error: function () {
                    alert('修改失败');
                }
            });
        }

        // 行内编辑功能
        function editRowInline(id) {
            // 根据ID查找对应的行数据
            var allData = $('#bootstrap-table').bootstrapTable('getData');
            var row = allData.find(function (item) {
                return item.id == id;
            });

            if (row) {
                $('#editModal').modal('show');
                $('#editForm input[name="id"]').val(row.id);
                $('#editForm select[name="platform"]').val(row.platform);
                $('#editForm input[name="symbol"]').val(row.symbol);
                $('#editForm select[name="state"]').val(row.state);
                $('#editForm input[name="message"]').val(row.message);
            }
        }

        // 行内删除功能
        function removeRowInline(id) {
            if (confirm('确定删除该条记录吗？')) {
                $.ajax({
                    url: prefix + "/remove",
                    type: "POST",
                    data: { ids: id },
                    success: function (result) {
                        if (result.code == 0) {
                            alert('删除成功');
                            $('#bootstrap-table').bootstrapTable('refresh');
                        } else {
                            alert(result.msg || '删除失败');
                        }
                    },
                    error: function () {
                        alert('删除失败');
                    }
                });
            }
        }
    </script>

    <!-- 添加弹窗 -->
    <div class="modal fade" id="addModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">新增订阅交易对</h4>
                </div>
                <div class="modal-body">
                    <form id="addForm">
                        <div class="form-group">
                            <label>平台：</label>
                            <select name="platform" class="form-control" required>
                                <option value="BINANCE">币安</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>币对名：</label>
                            <input type="text" name="symbol" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>状态：</label>
                            <select name="state" class="form-control" required>
                                <option value="1">开启订阅</option>
                                <option value="0">不开启</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>描述信息：</label>
                            <input type="text" name="message" class="form-control">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="submitAddForm()">确定</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑弹窗 -->
    <div class="modal fade" id="editModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">修改订阅交易对</h4>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" name="id">
                        <div class="form-group">
                            <label>平台：</label>
                            <select name="platform" class="form-control" required>
                                <option value="BINANCE">币安</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>交易所对应币对名：</label>
                            <input type="text" name="symbol" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>状态：</label>
                            <select name="state" class="form-control" required>
                                <option value="1">开启订阅</option>
                                <option value="0">不开启</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>描述信息：</label>
                            <input type="text" name="message" class="form-control">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="submitEditForm()">确定</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
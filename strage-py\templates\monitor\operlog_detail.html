<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志详细</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet"/>
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="https://cdn.bootcdn.net/ajax/libs/jquery-jsonview/1.2.3/jquery.jsonview.min.css" rel="stylesheet"/>
    <link href="{{ url_for('static', filename='css/style.min.css') }}" rel="stylesheet"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m-t" id="signupForm">
            <div class="form-group">
                <label class="col-sm-2 control-label">操作模块：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        {{ oper_log.title }} / {{ business_type_label }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">登录信息：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        {{ oper_log.oper_name }} / {{ oper_log.oper_ip }} / {{ oper_log.oper_location or '未知' }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">请求地址：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        {{ oper_log.request_method }} - {{ oper_log.oper_url }} (耗时{{ oper_log.cost_time }}毫秒)
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">操作方法：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        {{ oper_log.method }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">请求参数：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        <pre id="oper_param">{{ oper_log.oper_param or '无' }}</pre>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">返回参数：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        <pre id="json_result">{{ oper_log.json_result or '无' }}</pre>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">状态：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        {% if oper_log.status == 0 %}
                            <span class="label label-primary">正常</span>
                        {% else %}
                            <span class="label label-danger">异常</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% if oper_log.status != 0 and oper_log.error_msg %}
            <div class="form-group">
                <label class="col-sm-2 control-label">异常信息：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        {{ oper_log.error_msg }}
                    </div>
                </div>
            </div>
            {% endif %}
            <div class="form-group">
                <label class="col-sm-2 control-label">操作时间：</label>
                <div class="col-sm-10">
                    <div class="form-control-static">
                        {{ oper_log.oper_time.strftime('%Y-%m-%d %H:%M:%S') if oper_log.oper_time else '未知' }}
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 全局js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/2.2.4/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-jsonview/1.2.3/jquery.jsonview.min.js"></script>
    <script src="{{ url_for('static', filename='js/ry-ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>

    <script>
        $(function() {
            // 格式化JSON参数显示
            var operParam = '{{ oper_log.oper_param | safe }}';
            if ($.common.isNotEmpty(operParam) && operParam.length < 2000) {
                try {
                    var paramObj = JSON.parse(operParam);
                    $("#oper_param").JSONView(paramObj);
                } catch(e) {
                    $("#oper_param").text(operParam);
                }
            } else {
                $("#oper_param").text(operParam || '无');
            }
            
            var jsonResult = '{{ oper_log.json_result | safe }}';
            if ($.common.isNotEmpty(jsonResult) && jsonResult.length < 2000) {
                try {
                    var resultObj = JSON.parse(jsonResult);
                    $("#json_result").JSONView(resultObj);
                } catch(e) {
                    $("#json_result").text(jsonResult);
                }
            } else {
                $("#json_result").text(jsonResult || '无');
            }
        });
    </script>
</body>
</html>

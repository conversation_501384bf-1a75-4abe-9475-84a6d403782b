"""
AES加密解密工具类
迁移自: com.project.strategy.utils.AESUtil
"""
import base64
import logging
from typing import Optional
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad


logger = logging.getLogger(__name__)


class AESUtil:
    """AES加密解密工具类"""
    
    @staticmethod
    def encrypt(content: str, key: str = "") -> Optional[str]:
        """
        加密
        
        Args:
            content: 需要加密的内容
            key: 加密密钥
            
        Returns:
            加密后的字符串，失败返回None
        """
        if content is None:
            raise ValueError("content参数不能为None")
        if key is None:
            raise ValueError("key参数不能为None")
        
        try:
            # 将密钥转换为字节
            key_bytes = key.encode('utf-8')
            
            # 如果密钥长度不足16字节，进行填充；如果超过，截取前16字节
            if len(key_bytes) < 16:
                key_bytes = key_bytes.ljust(16, b'\0')
            elif len(key_bytes) > 16:
                key_bytes = key_bytes[:16]
            
            # 创建AES加密器
            cipher = AES.new(key_bytes, AES.MODE_ECB)
            
            # 对内容进行PKCS7填充
            content_bytes = content.encode('utf-8')
            padded_content = pad(content_bytes, AES.block_size)
            
            # 加密
            encrypted = cipher.encrypt(padded_content)
            
            # Base64编码
            return base64.b64encode(encrypted).decode('utf-8')
            
        except Exception as e:
            logger.error(f"AES加密失败: {e}")
            raise RuntimeError(f"AES加密失败: {e}")
    
    @staticmethod
    def decrypt(content: str, key: str = "") -> Optional[str]:
        """
        解密
        
        Args:
            content: 需要解密的内容
            key: 解密密钥
            
        Returns:
            解密后的字符串，失败返回None
        """
        if content is None:
            raise ValueError("content参数不能为None")
        if key is None:
            raise ValueError("key参数不能为None")
        
        try:
            # 将密钥转换为字节
            key_bytes = key.encode('utf-8')
            
            # 如果密钥长度不足16字节，进行填充；如果超过，截取前16字节
            if len(key_bytes) < 16:
                key_bytes = key_bytes.ljust(16, b'\0')
            elif len(key_bytes) > 16:
                key_bytes = key_bytes[:16]
            
            # 创建AES解密器
            cipher = AES.new(key_bytes, AES.MODE_ECB)
            
            # Base64解码
            encrypted_bytes = base64.b64decode(content.encode('utf-8'))
            
            # 解密
            decrypted_padded = cipher.decrypt(encrypted_bytes)
            
            # 去除填充
            decrypted = unpad(decrypted_padded, AES.block_size)
            
            return decrypted.decode('utf-8')
            
        except Exception as e:
            logger.error(f"AES解密失败: {e}")
            raise RuntimeError(f"AES解密失败: {e}")
    
    @staticmethod
    def url_safe_decrypt(content: str, key: str) -> Optional[str]:
        """
        URL安全解密（迁移原来的代码）
        
        Args:
            content: 待解密内容
            key: 解密密钥
            
        Returns:
            解密后的字符串，失败返回None
        """
        try:
            # 内容转码
            content = AESUtil._urlsafe_b64decode(content)
            
            # 判断Key是否正确
            if key is None:
                logger.error("Key为空null")
                return None
                
            # 判断Key是否为16位
            if len(key) != 16:
                logger.error("Key长度不是16位")
                return None
            
            # 创建AES解密器
            key_bytes = key.encode('utf-8')
            cipher = AES.new(key_bytes, AES.MODE_ECB)
            
            # Base64解码
            encrypted_bytes = base64.b64decode(content.encode('utf-8'))
            
            # 解密
            decrypted_padded = cipher.decrypt(encrypted_bytes)
            
            # 去除填充
            decrypted = unpad(decrypted_padded, AES.block_size)
            
            return decrypted.decode('utf-8')
            
        except Exception as e:
            logger.error(f"URL安全解密失败: {e}")
            return None
    
    @staticmethod
    def url_safe_encrypt(content: str, key: str) -> Optional[str]:
        """
        URL安全加密
        
        Args:
            content: 需要加密的内容
            key: 加密密钥
            
        Returns:
            加密后的字符串，失败返回None
        """
        try:
            if key is None:
                logger.error("Key为空null")
                return None
                
            # 判断Key是否为16位
            if len(key) != 16:
                logger.error("Key长度不是16位")
                return None
            
            # 创建AES加密器
            key_bytes = key.encode('utf-8')
            cipher = AES.new(key_bytes, AES.MODE_ECB)
            
            # 对内容进行PKCS7填充
            content_bytes = content.encode('utf-8')
            padded_content = pad(content_bytes, AES.block_size)
            
            # 加密
            encrypted = cipher.encrypt(padded_content)
            
            # Base64编码
            encoded_str = base64.b64encode(encrypted).decode('utf-8')
            
            # URL安全编码
            return AESUtil._urlsafe_b64encode(encoded_str)
            
        except Exception as e:
            logger.error(f"URL安全加密失败: {e}")
            return None
    
    @staticmethod
    def _urlsafe_b64decode(content: str) -> str:
        """URL安全Base64解码"""
        if content:
            content = content.replace("-", "/")
        return content
    
    @staticmethod
    def _urlsafe_b64encode(content: str) -> str:
        """URL安全Base64编码"""
        if content:
            content = content.replace("/", "-")
        return content


# 测试函数
def main():
    """测试AES加密解密功能"""
    private_key_str = "cU47ukuUqKHKejM99z4pvJReQD2CeqkzBW4kXzZDmeVjG6P7Z5Fx"
    address = "tb1q690dgshkel4amep8thgylp3kwt3s2f0842lmax"
    aes_key = "https://cex" + address[:5]
    
    print(f"AES Key: {aes_key}")
    
    # 加密
    encrypted = AESUtil.encrypt(private_key_str, aes_key)
    print(f"Encrypted: {encrypted}")
    
    # 解密
    decrypted = AESUtil.decrypt(encrypted, aes_key)
    print(f"Decrypted: {decrypted}")
    
    # 验证
    print(f"验证成功: {private_key_str == decrypted}")


if __name__ == "__main__":
    main()

"""
Binance服务类
迁移自: com.project.strategy.service.impl.BinanceService
"""
import logging
from decimal import Decimal, ROUND_DOWN
from typing import Optional

from .i_account_service import IAccountService
from ..context.account_context import AccountContext
from ..domain.entity.account_balance import AccountBalance
from ..enums import ExchangeEnum, InstrumentEnum, TransferInternalEnum

logger = logging.getLogger(__name__)


class BinanceService:
    """Binance服务类 - 对应Java版本的BinanceService"""

    def __init__(self, account_context: AccountContext):
        """
        初始化Binance服务

        Args:
            account_context: 账户上下文
        """
        self.account_context = account_context
        logger.info("BinanceService initialized")

    def get_balance(self, account_name: str) -> AccountBalance:
        """
        获取账户余额
        对应Java中的getBalance方法

        Args:
            account_name: 账户名称

        Returns:
            账户余额对象
        """
        try:
            # 获取现货账户服务
            spot_account: Optional[IAccountService] = self.account_context.get_account_client(
                ExchangeEnum.BINANCE, InstrumentEnum.SPOT, account_name
            )

            # 获取期货账户服务
            futures_account: Optional[IAccountService] = self.account_context.get_account_client(
                ExchangeEnum.BINANCE, InstrumentEnum.FUTURES_U, account_name
            )

            # 创建账户余额对象
            account_balance = AccountBalance()
            account_balance.account_name = account_name

            # 获取现货余额并设置精度
            if spot_account and hasattr(spot_account, 'get_balance'):
                spot_balance = spot_account.get_balance()
                account_balance.spot_balance = spot_balance.quantize(Decimal('0.0001'), rounding=ROUND_DOWN)
            else:
                account_balance.spot_balance = Decimal('0')

            # 获取期货余额并设置精度
            if futures_account and hasattr(futures_account, 'get_balance'):
                futures_balance = futures_account.get_balance()
                account_balance.futures_u_balance = futures_balance.quantize(Decimal('0.0001'), rounding=ROUND_DOWN)
            else:
                account_balance.futures_u_balance = Decimal('0')

            return account_balance

        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            # 返回默认的账户余额对象
            account_balance = AccountBalance()
            account_balance.account_name = account_name
            account_balance.spot_balance = Decimal('0')
            account_balance.futures_u_balance = Decimal('0')
            return account_balance

    def transfer_asset(self, account_name: str, amount: Decimal, transfer_internal_enum: TransferInternalEnum) -> bool:
        """
        资产转账
        对应Java中的transferAsset方法

        Args:
            account_name: 账户名称
            amount: 转账金额
            transfer_internal_enum: 转账类型枚举

        Returns:
            是否转账成功
        """
        try:
            # 获取现货账户服务
            spot_account: Optional[IAccountService] = self.account_context.get_account_client(
                ExchangeEnum.BINANCE, InstrumentEnum.SPOT, account_name
            )

            if spot_account and hasattr(spot_account, 'transfer_asset'):
                return spot_account.transfer_asset(account_name, amount, transfer_internal_enum)
            else:
                logger.error(f"未找到账户 {account_name} 的现货服务")
                return False

        except Exception as e:
            logger.error(f"资产转账失败: {e}")
            return False


"""
操作日志装饰器
"""
import time
import json
import logging
from functools import wraps
from flask import request, session, g
from services.log_service import OperLogService
from utils.ip_location import get_ip_location

logger = logging.getLogger(__name__)

def log_operation(title, business_type=0):
    """
    操作日志装饰器
    
    Args:
        title: 操作模块名称
        business_type: 业务类型 (0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            status = 0
            error_msg = ''
            json_result = ''
            
            try:
                # 执行原函数
                result = func(*args, **kwargs)
                
                # 记录返回结果
                if hasattr(result, 'get_json'):
                    # Flask Response对象
                    try:
                        json_result = json.dumps(result.get_json(), ensure_ascii=False)
                    except:
                        json_result = str(result.data)
                elif isinstance(result, dict):
                    json_result = json.dumps(result, ensure_ascii=False)
                else:
                    json_result = str(result)
                
                return result
                
            except Exception as e:
                status = 1
                error_msg = str(e)
                logger.error(f"操作异常: {e}")
                raise
                
            finally:
                # 计算耗时
                end_time = time.time()
                cost_time = int((end_time - start_time) * 1000)  # 毫秒
                
                # 记录操作日志
                try:
                    # 获取用户信息
                    oper_name = ''
                    if hasattr(g, 'user') and g.user:
                        oper_name = g.user.get('login_name', '')
                    elif 'user' in session:
                        oper_name = session['user'].get('login_name', '')
                    
                    # 获取请求信息
                    oper_ip = get_client_ip()
                    oper_location = get_ip_location(oper_ip)
                    oper_url = request.url
                    request_method = request.method
                    method = f"{func.__module__}.{func.__name__}"

                    # 获取请求参数
                    oper_param = get_request_params()

                    # 插入日志
                    OperLogService.insert_oper_log(
                        title=title,
                        business_type=business_type,
                        method=method,
                        request_method=request_method,
                        oper_name=oper_name,
                        oper_url=oper_url,
                        oper_ip=oper_ip,
                        oper_location=oper_location,
                        oper_param=oper_param,
                        json_result=json_result,
                        status=status,
                        error_msg=error_msg,
                        cost_time=cost_time
                    )
                except Exception as log_e:
                    logger.error(f"记录操作日志失败: {log_e}")
        
        return wrapper
    return decorator

def get_client_ip():
    """获取客户端IP地址"""
    try:
        # 检查是否有代理
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr
    except:
        return '127.0.0.1'

def get_request_params():
    """获取请求参数"""
    try:
        params = {}
        
        # GET参数
        if request.args:
            params.update(dict(request.args))
        
        # POST参数
        if request.form:
            params.update(dict(request.form))
        
        # JSON参数
        if request.is_json and request.get_json():
            params.update(request.get_json())
        
        # 过滤敏感信息
        sensitive_keys = ['password', 'pwd', 'token', 'secret', 'key']
        for key in list(params.keys()):
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                params[key] = '***'
        
        return json.dumps(params, ensure_ascii=False)
    except Exception as e:
        logger.error(f"获取请求参数失败: {e}")
        return ''

# 业务类型常量
class BusinessType:
    """业务类型常量"""
    OTHER = 0      # 其它
    INSERT = 1     # 新增
    UPDATE = 2     # 修改
    DELETE = 3     # 删除
    GRANT = 4      # 授权
    EXPORT = 5     # 导出
    IMPORT = 6     # 导入
    FORCE = 7      # 强退
    GENCODE = 8    # 生成代码
    CLEAN = 9      # 清空数据

# 便捷装饰器
def log_insert(title):
    """新增操作日志装饰器"""
    return log_operation(title, BusinessType.INSERT)

def log_update(title):
    """修改操作日志装饰器"""
    return log_operation(title, BusinessType.UPDATE)

def log_delete(title):
    """删除操作日志装饰器"""
    return log_operation(title, BusinessType.DELETE)

def log_export(title):
    """导出操作日志装饰器"""
    return log_operation(title, BusinessType.EXPORT)

def log_clean(title):
    """清空操作日志装饰器"""
    return log_operation(title, BusinessType.CLEAN)

def log_other(title):
    """其他操作日志装饰器"""
    return log_operation(title, BusinessType.OTHER)

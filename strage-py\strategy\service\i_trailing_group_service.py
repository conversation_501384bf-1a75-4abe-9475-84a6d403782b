"""
跟踪组服务接口
迁移自: com.project.strategy.service.ITrailingGroupService
"""
from abc import ABC, abstractmethod
from typing import List, Optional

from ..domain.entity.trailing_group import TrailingGroup


class ITrailingGroupService(ABC):
    """跟踪组服务接口"""
    
    @abstractmethod
    def handle_group_state(self) -> None:
        """
        处理组状态
        """
        pass
    
    @abstractmethod
    def select_trailing_group_by_id(self, id: int) -> Optional[TrailingGroup]:
        """
        根据ID查询跟踪组
        
        Args:
            id: 组ID
            
        Returns:
            跟踪组对象，不存在返回None
        """
        pass
    
    @abstractmethod
    def select_trailing_group_list(self, trailing_group: Optional[TrailingGroup] = None) -> List[TrailingGroup]:
        """
        查询跟踪组列表
        
        Args:
            trailing_group: 查询条件对象
            
        Returns:
            跟踪组列表
        """
        pass
    
    @abstractmethod
    def insert_trailing_group(self, trailing_group: TrailingGroup) -> bool:
        """
        插入跟踪组
        
        Args:
            trailing_group: 跟踪组对象
            
        Returns:
            是否插入成功
        """
        pass
    
    @abstractmethod
    def update_trailing_group(self, trailing_group: TrailingGroup) -> bool:
        """
        更新跟踪组
        
        Args:
            trailing_group: 跟踪组对象
            
        Returns:
            是否更新成功
        """
        pass
    
    @abstractmethod
    def delete_trailing_group_by_ids(self, ids: str) -> int:
        """
        根据ID列表批量删除跟踪组
        
        Args:
            ids: ID字符串，逗号分隔
            
        Returns:
            删除的记录数量
        """
        pass
    
    @abstractmethod
    def delete_trailing_group_by_id(self, id: int) -> bool:
        """
        根据ID删除跟踪组
        
        Args:
            id: 组ID
            
        Returns:
            是否删除成功
        """
        pass

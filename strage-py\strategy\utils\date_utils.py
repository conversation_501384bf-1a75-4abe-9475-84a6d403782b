"""
日期工具类
迁移自: com.common.utils.DateUtils
"""
from datetime import datetime, timezone
from typing import Optional


class DateUtils:
    """日期工具类"""
    
    @staticmethod
    def get_now_date() -> datetime:
        """
        获取当前日期时间
        
        Returns:
            当前日期时间
        """
        return datetime.now()
    
    @staticmethod
    def get_now_date_utc() -> datetime:
        """
        获取当前UTC日期时间
        
        Returns:
            当前UTC日期时间
        """
        return datetime.now(timezone.utc)
    
    @staticmethod
    def format_date(date: Optional[datetime], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """
        格式化日期
        
        Args:
            date: 日期对象
            format_str: 格式字符串
            
        Returns:
            格式化后的日期字符串
        """
        if date is None:
            return ""
        return date.strftime(format_str)
    
    @staticmethod
    def parse_date(date_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串
            format_str: 格式字符串
            
        Returns:
            日期对象，解析失败返回None
        """
        try:
            return datetime.strptime(date_str, format_str)
        except (ValueError, TypeError):
            return None

"""
策略交易模块
迁移自Java项目：com.project.strategy

包含以下子模块：
- enums: 枚举类定义
- domain: 领域模型和实体类
- context: 上下文管理
- task: 任务处理
- utils: 工具类
- global_cache: 全局缓存管理
"""

# 导入子模块
from . import enums
from . import domain
from . import utils
from . import context
from . import task
from . import dao
from . import service
from .global_cache import GlobalCache

# 版本信息
__version__ = '1.0.0'
__author__ = 'Strategy Migration Team'
__description__ = '策略交易模块 - 从Java迁移到Python Flask'

# 导出主要模块
__all__ = [
    'enums',
    'domain',
    'utils',
    'context',
    'task',
    'dao',
    'service',
    'GlobalCache',
]

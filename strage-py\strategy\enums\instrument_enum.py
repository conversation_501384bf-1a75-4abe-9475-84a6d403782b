"""
交易工具枚举类
迁移自: com.project.strategy.enums.InstrumentEnum
"""
from enum import Enum
from typing import Optional


class InstrumentEnum(Enum):
    """交易工具枚举"""
    
    SPOT = (1, "SPOT", "现货")
    FUTURES_U = (2, "FUTURES", "U本位合约")
    UNKNOWN = (-1, "null", "未知")
    
    def __init__(self, code: int, value: str, desc: str):
        """
        初始化交易工具枚举
        
        Args:
            code: 工具代码
            value: 工具值
            desc: 工具描述
        """
        self.code = code
        self.type_value = value  # 使用type_value避免与Enum.value冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取工具代码"""
        return self.code
    
    def get_value(self) -> str:
        """获取工具值"""
        return self.type_value
    
    def get_desc(self) -> str:
        """获取工具描述"""
        return self.desc
    
    @classmethod
    def parse_code(cls, code: int) -> Optional['InstrumentEnum']:
        """
        根据代码解析交易工具枚举
        
        Args:
            code: 工具代码
            
        Returns:
            对应的交易工具枚举，如果未找到则返回None
        """
        for instrument_enum in cls:
            if instrument_enum.get_code() == code:
                return instrument_enum
        return None
    
    @classmethod
    def parse_value(cls, value: str) -> Optional['InstrumentEnum']:
        """
        根据值解析交易工具枚举
        
        Args:
            value: 工具值
            
        Returns:
            对应的交易工具枚举，如果未找到则返回None
        """
        for instrument_enum in cls:
            if instrument_enum.get_value() == value:
                return instrument_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_value}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"InstrumentEnum.{self.name}"

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增')" />
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-account-add">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">账户名称：</label>
                <div class="col-sm-8">
                    <input name="accountName" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">apiKey：</label>
                <div class="col-sm-8">
                    <input name="apikey" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">Secret Key：</label>
                <div class="col-sm-8">
                    <input name="secretKey" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">密码：</label>
                <div class="col-sm-8">
                    <input name="password" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">平台：</label>
                <div class="col-sm-8">
                    <input name="platform" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <select name="state" class="form-control" width="200">
                        <option value="1">可用</option>
                        <option value="0">不可用</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">描述信息：</label>
                <div class="col-sm-8">
                    <input name="message" class="form-control" type="text">
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "strategy/account"
    $("#form-account-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/add", $('#form-account-add').serialize());
        }
    }
</script>
</body>
</html>
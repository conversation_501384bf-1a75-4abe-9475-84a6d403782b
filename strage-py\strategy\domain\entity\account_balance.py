"""
账户余额实体类
迁移自: com.project.strategy.domain.entity.AccountBalance
"""
from dataclasses import dataclass
from typing import Optional
from decimal import Decimal


@dataclass
class AccountBalance:
    """账户余额实体"""
    
    id: Optional[int] = None
    spot_balance: Optional[Decimal] = None  # 现货余额
    futures_u_balance: Optional[Decimal] = None  # U本位合约余额
    transfer_type: Optional[str] = None  # 转账类型
    transfer_amount: Optional[Decimal] = None  # 转账金额
    account_name: Optional[str] = None  # 账户名称
    platform: Optional[str] = None  # 平台
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        if self.id is not None and not isinstance(self.id, int):
            self.id = int(self.id) if str(self.id).isdigit() else None
        
        # 处理Decimal字段
        decimal_fields = ['spot_balance', 'futures_u_balance', 'transfer_amount']
        for field_name in decimal_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, Decimal):
                try:
                    setattr(self, field_name, Decimal(str(value)))
                except (ValueError, TypeError):
                    setattr(self, field_name, None)
    
    def get_total_balance(self) -> Optional[Decimal]:
        """获取总余额"""
        total = Decimal('0')
        
        if self.spot_balance is not None:
            total += self.spot_balance
        if self.futures_u_balance is not None:
            total += self.futures_u_balance
            
        return total if total > 0 else None
    
    def has_sufficient_balance(self, required_amount: Decimal, balance_type: str = 'total') -> bool:
        """
        检查是否有足够余额
        
        Args:
            required_amount: 需要的金额
            balance_type: 余额类型 ('spot', 'futures', 'total')
            
        Returns:
            是否有足够余额
        """
        if required_amount is None or required_amount <= 0:
            return False
            
        if balance_type == 'spot':
            return self.spot_balance is not None and self.spot_balance >= required_amount
        elif balance_type == 'futures':
            return self.futures_u_balance is not None and self.futures_u_balance >= required_amount
        else:  # total
            total = self.get_total_balance()
            return total is not None and total >= required_amount
    
    def is_valid(self) -> bool:
        """检查账户余额信息是否有效"""
        return (
            self.account_name is not None and 
            self.platform is not None and
            len(self.account_name.strip()) > 0 and
            len(self.platform.strip()) > 0
        )
    
    @classmethod
    def builder(cls) -> 'AccountBalanceBuilder':
        """创建构建器"""
        return AccountBalanceBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, Decimal):
                    result[key] = str(value)
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'AccountBalance':
        """从字典创建实例"""
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"AccountBalance(id={self.id}, account_name='{self.account_name}', "
                f"spot={self.spot_balance}, futures={self.futures_u_balance})")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"AccountBalance(id={self.id}, account_name='{self.account_name}', "
                f"platform='{self.platform}', spot_balance={self.spot_balance}, "
                f"futures_u_balance={self.futures_u_balance})")


class AccountBalanceBuilder:
    """AccountBalance构建器"""
    
    def __init__(self):
        self._data = {}
    
    def id(self, id: int) -> 'AccountBalanceBuilder':
        self._data['id'] = id
        return self
    
    def spot_balance(self, spot_balance: Decimal) -> 'AccountBalanceBuilder':
        self._data['spot_balance'] = spot_balance
        return self
    
    def futures_u_balance(self, futures_u_balance: Decimal) -> 'AccountBalanceBuilder':
        self._data['futures_u_balance'] = futures_u_balance
        return self
    
    def transfer_type(self, transfer_type: str) -> 'AccountBalanceBuilder':
        self._data['transfer_type'] = transfer_type
        return self
    
    def transfer_amount(self, transfer_amount: Decimal) -> 'AccountBalanceBuilder':
        self._data['transfer_amount'] = transfer_amount
        return self
    
    def account_name(self, account_name: str) -> 'AccountBalanceBuilder':
        self._data['account_name'] = account_name
        return self
    
    def platform(self, platform: str) -> 'AccountBalanceBuilder':
        self._data['platform'] = platform
        return self
    
    def build(self) -> AccountBalance:
        """构建AccountBalance实例"""
        return AccountBalance(**self._data)

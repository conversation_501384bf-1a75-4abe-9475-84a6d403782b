"""
持仓方向枚举类
迁移自: com.project.strategy.enums.PositionSideEnum
"""
from enum import Enum
from typing import Optional


class PositionSideEnum(Enum):
    """持仓方向枚举"""
    
    LONG = (0, "LONG", "多单")
    SHORT = (1, "SHORT", "空单")
    UNKNOWN = (-1, "null", "未知")
    
    def __init__(self, code: int, value: str, desc: str):
        """
        初始化持仓方向枚举
        
        Args:
            code: 方向代码
            value: 方向值
            desc: 方向描述
        """
        self.code = code
        self.type_value = value  # 使用type_value避免与Enum.value冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取方向代码"""
        return self.code
    
    def get_value(self) -> str:
        """获取方向值"""
        return self.type_value
    
    def get_desc(self) -> str:
        """获取方向描述"""
        return self.desc
    
    @classmethod
    def parse_code(cls, code: int) -> Optional['PositionSideEnum']:
        """
        根据代码解析持仓方向枚举
        
        Args:
            code: 方向代码
            
        Returns:
            对应的持仓方向枚举，如果未找到则返回None
        """
        for side_enum in cls:
            if side_enum.get_code() == code:
                return side_enum
        return None
    
    @classmethod
    def parse_value(cls, value: str) -> Optional['PositionSideEnum']:
        """
        根据值解析持仓方向枚举
        
        Args:
            value: 方向值
            
        Returns:
            对应的持仓方向枚举，如果未找到则返回None
        """
        for side_enum in cls:
            if side_enum.get_value() == value:
                return side_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_value}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"PositionSideEnum.{self.name}"

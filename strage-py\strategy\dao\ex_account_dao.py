"""
交易所账户DAO类
迁移自: com.project.strategy.mapper.ExAccountMapper
"""
import logging
from typing import List, Optional
from datetime import datetime
from dao.base_dao import BaseDAO
from ..domain.entity.ex_account import ExAccount

logger = logging.getLogger(__name__)


class ExAccountDAO(BaseDAO):
    """交易所账户DAO类"""

    def __init__(self):
        """初始化交易所账户DAO"""
        logger.info("ExAccountDAO initialized")
    
    def select_ex_account_by_id(self, id: int) -> Optional[ExAccount]:
        """
        根据ID查询交易所账户
        对应: selectExAccountById

        Args:
            id: 账户ID

        Returns:
            交易所账户对象，不存在返回None
        """
        try:
            sql = "SELECT * FROM ex_account WHERE id = %s"
            results = self.execute_query(sql, (id,))

            if results:
                row = results[0]
                return self._row_to_entity(row)
            return None
        except Exception as e:
            logger.error(f"根据ID查询交易所账户失败: {e}")
            return None
    
    def select_ex_account_list(self, ex_account: Optional[ExAccount] = None) -> List[ExAccount]:
        """
        查询交易所账户列表
        对应: selectExAccountList

        Args:
            ex_account: 查询条件对象

        Returns:
            交易所账户列表
        """
        try:
            sql = "SELECT * FROM ex_account"
            params = []
            where_conditions = []

            if ex_account:
                # 账户名称模糊查询
                if ex_account.account_name:
                    where_conditions.append("account_name LIKE %s")
                    params.append(f"%{ex_account.account_name}%")

                # 用户ID精确查询
                if ex_account.user_id is not None:
                    where_conditions.append("user_id = %s")
                    params.append(ex_account.user_id)

                # 平台精确查询
                if ex_account.platform:
                    where_conditions.append("platform = %s")
                    params.append(ex_account.platform)

                # 状态精确查询
                if ex_account.state is not None:
                    where_conditions.append("state = %s")
                    params.append(ex_account.state)

                # 消息精确查询
                if ex_account.message:
                    where_conditions.append("message = %s")
                    params.append(ex_account.message)

            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)

            sql += " ORDER BY id DESC"

            results = self.execute_query(sql, params if params else None)

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))

            return entities
        except Exception as e:
            logger.error(f"查询交易所账户列表失败: {e}")
            return []
    
    def insert_ex_account(self, ex_account: ExAccount) -> int:
        """
        插入交易所账户
        对应: insertExAccount

        Args:
            ex_account: 交易所账户对象

        Returns:
            插入的记录数量
        """
        try:
            sql = """
                INSERT INTO ex_account (user_id, ex_account, account_name, platform, apikey,
                                      secret_key, password, state, message, secret_msg,
                                      create_time, update_time)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                ex_account.user_id,
                ex_account.ex_account,
                ex_account.account_name,
                ex_account.platform,
                ex_account.apikey,
                ex_account.secret_key,
                ex_account.password,
                ex_account.state,
                ex_account.message,
                ex_account.secret_msg,
                ex_account.create_time or datetime.now(),
                ex_account.update_time or datetime.now()
            )

            result = self.execute_insert(sql, params)
            if result:
                ex_account.id = result
                return 1
            return 0
        except Exception as e:
            logger.error(f"插入交易所账户失败: {e}")
            return 0

    def update_ex_account(self, ex_account: ExAccount) -> int:
        """
        更新交易所账户
        对应: updateExAccount

        Args:
            ex_account: 交易所账户对象

        Returns:
            更新的记录数量
        """
        try:
            sql = """
                UPDATE ex_account
                SET user_id = %s, ex_account = %s, account_name = %s, platform = %s,
                    apikey = %s, secret_key = %s, password = %s, state = %s,
                    message = %s, secret_msg = %s, update_time = %s
                WHERE id = %s
            """
            params = (
                ex_account.user_id,
                ex_account.ex_account,
                ex_account.account_name,
                ex_account.platform,
                ex_account.apikey,
                ex_account.secret_key,
                ex_account.password,
                ex_account.state,
                ex_account.message,
                ex_account.secret_msg,
                ex_account.update_time or datetime.now(),
                ex_account.id
            )

            affected_rows = self.execute_update(sql, params)
            return affected_rows
        except Exception as e:
            logger.error(f"更新交易所账户失败: {e}")
            return 0

    def delete_ex_account_by_id(self, id: int) -> int:
        """
        根据ID删除交易所账户
        对应: deleteExAccountById

        Args:
            id: 账户ID

        Returns:
            删除的记录数量
        """
        try:
            sql = "DELETE FROM ex_account WHERE id = %s"
            affected_rows = self.execute_update(sql, (id,))
            return affected_rows
        except Exception as e:
            logger.error(f"根据ID删除交易所账户失败: {e}")
            return 0
    
    def delete_ex_account_by_ids(self, ids: str) -> int:
        """
        根据ID列表批量删除交易所账户
        对应: deleteExAccountByIds

        Args:
            ids: ID字符串，逗号分隔

        Returns:
            删除的记录数量
        """
        try:
            if not ids or not ids.strip():
                return 0

            # 将逗号分隔的字符串转换为列表
            id_list = [id_str.strip() for id_str in ids.split(',') if id_str.strip()]

            if not id_list:
                return 0

            # 构建IN子句的占位符
            placeholders = ','.join(['%s'] * len(id_list))
            sql = f"DELETE FROM ex_account WHERE id IN ({placeholders})"

            affected_rows = self.execute_update(sql, id_list)
            return affected_rows
        except Exception as e:
            logger.error(f"批量删除交易所账户失败: {e}")
            return 0
    
    def _row_to_entity(self, row: dict) -> ExAccount:
        """
        将数据库行转换为实体对象

        Args:
            row: 数据库行字典

        Returns:
            交易所账户实体对象
        """
        return ExAccount(
            id=row.get('id'),
            user_id=row.get('user_id'),
            ex_account=row.get('ex_account'),
            account_name=row.get('account_name'),
            platform=row.get('platform'),
            apikey=row.get('apikey'),
            secret_key=row.get('secret_key'),
            password=row.get('password'),
            state=row.get('state'),
            message=row.get('message'),
            secret_msg=row.get('secret_msg'),
            create_time=row.get('create_time'),
            update_time=row.get('update_time')
        )

    def select_by_user_id(self, user_id: int) -> List[ExAccount]:
        """
        根据用户ID查询交易所账户列表

        Args:
            user_id: 用户ID

        Returns:
            交易所账户列表
        """
        try:
            sql = "SELECT * FROM ex_account WHERE user_id = %s ORDER BY id DESC"
            results = self.execute_query(sql, (user_id,))

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))

            return entities
        except Exception as e:
            logger.error(f"根据用户ID查询交易所账户列表失败: {e}")
            return []
    
    def select_by_platform(self, platform: str) -> List[ExAccount]:
        """
        根据平台查询交易所账户列表

        Args:
            platform: 平台名称

        Returns:
            交易所账户列表
        """
        try:
            sql = "SELECT * FROM ex_account WHERE platform = %s ORDER BY id DESC"
            results = self.execute_query(sql, (platform,))

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))

            return entities
        except Exception as e:
            logger.error(f"根据平台查询交易所账户列表失败: {e}")
            return []

    def select_active_accounts(self) -> List[ExAccount]:
        """
        查询所有激活的交易所账户

        Returns:
            激活的交易所账户列表
        """
        try:
            sql = "SELECT * FROM ex_account WHERE state = 1 ORDER BY id DESC"
            results = self.execute_query(sql)

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))

            return entities
        except Exception as e:
            logger.error(f"查询激活的交易所账户失败: {e}")
            return []
    
    def select_by_account_name(self, account_name: str, exact_match: bool = False) -> List[ExAccount]:
        """
        根据账户名称查询交易所账户

        Args:
            account_name: 账户名称
            exact_match: 是否精确匹配

        Returns:
            交易所账户列表
        """
        try:
            if exact_match:
                sql = "SELECT * FROM ex_account WHERE account_name = %s ORDER BY id DESC"
                params = (account_name,)
            else:
                sql = "SELECT * FROM ex_account WHERE account_name LIKE %s ORDER BY id DESC"
                params = (f"%{account_name}%",)

            results = self.execute_query(sql, params)

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))

            return entities
        except Exception as e:
            logger.error(f"根据账户名称查询交易所账户失败: {e}")
            return []

    def count_by_user_id(self, user_id: int) -> int:
        """
        统计用户的交易所账户数量

        Args:
            user_id: 用户ID

        Returns:
            账户数量
        """
        try:
            sql = "SELECT COUNT(*) as count FROM ex_account WHERE user_id = %s"
            results = self.execute_query(sql, (user_id,))

            if results:
                return results[0].get('count', 0)
            return 0
        except Exception as e:
            logger.error(f"统计用户交易所账户数量失败: {e}")
            return 0

    def update_state(self, id: int, state: int) -> bool:
        """
        更新账户状态

        Args:
            id: 账户ID
            state: 新状态

        Returns:
            是否更新成功
        """
        try:
            sql = "UPDATE ex_account SET state = %s, update_time = %s WHERE id = %s"
            params = (state, datetime.now(), id)

            affected_rows = self.execute_update(sql, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"更新账户状态失败: {e}")
            return False

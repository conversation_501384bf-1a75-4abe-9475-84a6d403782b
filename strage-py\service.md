# Strategy Service 方法签名和依赖关系文档

## 1. AbstractAccountCommon 类

**文件路径**: `strage-py\strategy\service\abstract_account_common.py`
**作用**: 抽象账户通用类，提供默认的不支持实现

| 方法签名 | 作用 | 依赖的类/方法 | 返回类型 |
|---------|------|-------------|---------|
| `fill_position(trailing: TrailingProfit) -> None` | 填充持仓信息 - 默认不支持 | 无 | None |
| `get_position(symbol: str) -> Dict[str, Dict[str, Any]]` | 获取持仓信息 - 默认不支持 | 无 | Dict |
| `close_position(symbol: str, quantity: float, position_side: PositionSideEnum) -> Dict[str, Any]` | 平仓 - 默认不支持 | 无 | Dict |
| `open_position(symbol: str, quantity: float, leverage: int, position_side: PositionSideEnum) -> Dict[str, Any]` | 开仓 - 默认不支持 | 无 | Dict |
| `set_trade_mode_type(symbol: str, margin_type: TradeModeEnum) -> Dict[str, Any]` | 设置交易模式类型 - 默认不支持 | 无 | Dict |
| `set_leverage(symbol: str, leverage_level: int) -> Dict[str, Any]` | 设置杠杆 - 默认不支持 | 无 | Dict |
| `transfer_asset(account_name: str, amount: Decimal, transfer_internal_enum: TransferInternalEnum) -> bool` | 资产转账 - 默认不支持 | 无 | bool |
| `get_balance() -> Decimal` | 获取余额 - 默认不支持 | 无 | Decimal |
| `is_support() -> bool` | 是否支持 - 默认不支持 | 无 | bool |
| `get_exchange() -> ExchangeEnum` | 获取交易所 - 默认不支持 | 无 | ExchangeEnum |
| `get_instrument() -> InstrumentEnum` | 获取交易工具 - 默认不支持 | 无 | InstrumentEnum |
| `get_account_name() -> str` | 获取账户名称 - 默认不支持 | 无 | str |
| `get_account() -> str` | 获取账户 - 默认不支持 | 无 | str |
| `get_header() -> Dict[str, str]` | 获取HTTP请求头 - 通用方法 | 无 | Dict |

## 2. BinanceService 类

**文件路径**: `strage-py\strategy\service\binance_service.py`
**作用**: Binance服务类，对应Java版本的BinanceService，通过AccountContext管理账户

| 方法签名 | 作用 | 依赖的类/方法 | 返回类型 |
|---------|------|-------------|---------|
| `__init__(account_context: AccountContext)` | 初始化Binance服务 | 无 | None |
| `get_balance(account_name: str) -> AccountBalance` | 获取账户余额 | AccountContext.get_account_client()<br/>IAccountService.get_balance() | AccountBalance |
| `transfer_asset(account_name: str, amount: Decimal, transfer_internal_enum: TransferInternalEnum) -> bool` | 资产转账 | AccountContext.get_account_client()<br/>IAccountService.transfer_asset() | bool |

## 3. BinanceBaseService 类

**文件路径**: `strage-py\strategy\service\binance_base_service.py`
**作用**: Binance基础服务类，包含HTTP请求相关的工具方法

| 方法签名 | 作用 | 依赖的类/方法 | 返回类型 |
|---------|------|-------------|---------|
| `__init__(db_session, account: ExAccount)` | 初始化Binance基础服务 | 无 | None |
| `is_support() -> bool` | 是否支持此服务 | 无 | bool |
| `get_exchange() -> ExchangeEnum` | 获取交易所 | 无 | ExchangeEnum |
| `get_account_name() -> str` | 获取账户名称 | 无 | str |
| `get_account() -> str` | 获取账户信息 | 无 | str |
| `_generate_signature(query_string: str) -> str` | 生成签名 | hmac.new() | str |
| `_get_timestamp() -> int` | 获取时间戳 | time.time() | int |
| `_build_headers() -> Dict[str, str]` | 构建请求头 | 无 | Dict |
| `_make_request(method: str, endpoint: str, params: Optional[Dict], signed: bool, base_url: Optional[str]) -> Dict[str, Any]` | 发起HTTP请求 | OkHttpUtil.do_get/post/put/delete() | Dict |
| `get_account_info() -> Dict[str, Any]` | 获取账户信息 | _make_request() | Dict |
| `get_balance() -> Decimal` | 获取余额 | get_account_info() | Decimal |
| `get_server_time() -> Dict[str, Any]` | 获取服务器时间 | _make_request() | Dict |
| `get_exchange_info() -> Dict[str, Any]` | 获取交易所信息 | _make_request() | Dict |
| `get_symbol_price(symbol: str) -> Dict[str, Any]` | 获取交易对价格 | _make_request() | Dict |
| `test_connectivity() -> bool` | 测试连接性 | _make_request() | bool |

## 4. AccountBeanFactory 类

**文件路径**: `strage-py\strategy\service\account_bean_factory.py`  
**作用**: 账户Bean工厂类，负责创建和管理账户服务实例

| 方法签名 | 作用 | 依赖的类/方法 | 返回类型 |
|---------|------|-------------|---------|
| `__init__(db_session, account_context: AccountContext)` | 初始化账户Bean工厂 | ExAccountDAO() | None |
| `create_account_services() -> None` | 创建账户服务实例 | ExAccountDAO.select_ex_account_list()<br/>AccountContext.add_account_client() | None |
| `_create_futures_service(ex_account: ExAccount) -> Optional[IAccountService]` | 创建期货服务实例 | BinanceFuturesService() | Optional[IAccountService] |
| `_create_spot_service(ex_account: ExAccount) -> Optional[IAccountService]` | 创建现货服务实例 | BinanceSpotService() | Optional[IAccountService] |
| `add_account_service(ex_account: ExAccount) -> bool` | 添加账户服务 | _create_futures_service()<br/>_create_spot_service()<br/>AccountContext.add_account_client() | bool |
| `refresh_account_services() -> None` | 刷新账户服务 | AccountContext.refresh_clients()<br/>create_account_services() | None |
| `get_service_by_criteria(exchange: ExchangeEnum, instrument: InstrumentEnum, account_name: str) -> Optional[IAccountService]` | 根据条件获取服务 | AccountContext.get_account_client() | Optional[IAccountService] |
| `get_services_by_exchange(exchange: ExchangeEnum) -> List[IAccountService]` | 根据交易所获取服务列表 | AccountContext.get_platform_client() | List[IAccountService] |
| `remove_account_service(account_name: str, platform: str = None) -> bool` | 移除账户服务 | AccountContext.get_all_clients()<br/>AccountContext.remove_account_client() | bool |
| `get_service_count() -> int` | 获取服务数量 | AccountContext.get_client_count() | int |
| `get_service_status() -> Dict[str, bool]` | 获取服务状态 | AccountContext.validate_clients() | Dict |

## 5. BinanceFuturesService 类

**文件路径**: `strage-py\strategy\service\binance_futures_service.py`
**作用**: Binance期货服务类，继承自BinanceBaseService

### 5.1 核心业务方法

| 方法签名 | 作用 | 依赖的类/方法 | 返回类型 |
|---------|------|-------------|---------|
| `__init__(db_session, account)` | 初始化Binance期货服务 | BinanceService.__init__() | None |
| `get_instrument() -> InstrumentEnum` | 获取交易工具 | 无 | InstrumentEnum |
| `fill_position(trailing: TrailingProfit) -> None` | 填充持仓信息 | get_close_price()<br/>get_position_history() | None |
| `get_position_history(symbol: str, close_order_id: str) -> Optional[Dict[str, Any]]` | 获取持仓历史 | _make_request()<br/>_get_timestamp() | Optional[Dict] |
| `get_close_price(symbol: str, close_order_id: str) -> Optional[Decimal]` | 获取平仓价格 | _make_request()<br/>_get_timestamp() | Optional[Decimal] |
| `get_position(symbol: str) -> Dict[str, Dict[str, Any]]` | 获取持仓信息 | _make_request()<br/>_get_timestamp() | Dict |
| `symbol_config(symbol: str) -> Dict[str, Any]` | 获取交易对配置 | _make_request()<br/>_get_timestamp() | Dict |
| `prepare_before(symbol: str, leverage: int) -> bool` | 开仓前准备检查 | symbol_config()<br/>set_trade_mode_type()<br/>set_leverage() | bool |

| `get_header() -> Dict[str, str]` | 获取HTTP请求头 | 无 | Dict |

### 5.2 交易操作方法

| 方法签名 | 作用 | 依赖的类/方法 | 返回类型 |
|---------|------|-------------|---------|
| `close_position(symbol: str, quantity: float, position_side: PositionSideEnum) -> Dict[str, Any]` | 平仓操作 | _make_request()<br/>_get_timestamp() | Dict |
| `open_position(symbol: str, quantity: float, leverage: int, position_side: PositionSideEnum) -> Dict[str, Any]` | 开仓操作 | prepare_before()<br/>_make_request()<br/>_get_timestamp() | Dict |
| `set_trade_mode_type(symbol: str, margin_type: TradeModeEnum) -> Dict[str, Any]` | 设置交易模式类型 | _make_request()<br/>_get_timestamp() | Dict |
| `set_leverage(symbol: str, leverage_level: int) -> Dict[str, Any]` | 设置杠杆 | _make_request()<br/>_get_timestamp() | Dict |
| `transfer_asset(account_name: str, amount: Decimal, transfer_internal_enum: TransferInternalEnum) -> bool` | 资产转账 | _make_request()<br/>_get_transfer_type_code() | bool |
| `get_balance() -> Decimal` | 获取余额 | _make_request()<br/>_get_timestamp() | Decimal |

### 5.3 接口实现方法

| 方法签名 | 作用 | 依赖的类/方法 | 返回类型 |
|---------|------|-------------|---------|
| `is_support() -> bool` | 是否支持 | 无 | bool |
| `get_exchange() -> ExchangeEnum` | 获取交易所 | 无 | ExchangeEnum |
| `get_account_name() -> str` | 获取账户名称 | 无 | str |
| `get_account() -> str` | 获取账户 | 无 | str |

### 3.4 私有辅助方法

| 方法签名 | 作用 | 依赖的类/方法 | 返回类型 |
|---------|------|-------------|---------|
| `_place_futures_order(symbol: str, side: str, order_type: str, quantity: float, price: Optional[float] = None, reduce_only: bool = False) -> Dict[str, Any]` | 下期货订单 | _make_request() | Dict |
| `_get_transfer_type_code(transfer_type: str) -> int` | 获取转账类型代码 | 无 | int |

## 4. 常用依赖关系总结

### 4.1 数据访问层依赖
- `ExAccountDAO.select_ex_account_list()` - 查询账户列表
- `ExAccountDAO()` - 创建DAO实例

### 4.2 上下文服务依赖
- `AccountContext.add_account_client()` - 添加账户客户端
- `AccountContext.get_account_client()` - 获取账户客户端
- `AccountContext.get_platform_client()` - 获取平台客户端
- `AccountContext.remove_account_client()` - 移除账户客户端
- `AccountContext.refresh_clients()` - 刷新客户端
- `AccountContext.get_all_clients()` - 获取所有客户端
- `AccountContext.get_client_count()` - 获取客户端数量
- `AccountContext.validate_clients()` - 验证客户端

### 4.3 服务实例化依赖
- `BinanceFuturesService()` - 创建期货服务
- `BinanceSpotService()` - 创建现货服务

### 4.4 HTTP请求依赖
- `_make_request()` - 发起HTTP请求（继承自BinanceService）
- `_get_timestamp()` - 获取时间戳（继承自BinanceService）

### 4.5 枚举类依赖
- `ExchangeEnum.BINANCE.get_name()` - 获取交易所名称
- `InstrumentEnum.FUTURES_U.get_value()` - 获取期货工具值
- `InstrumentEnum.SPOT.get_value()` - 获取现货工具值
- `TradeModeEnum.ISOLATED_BN.get_value()` - 获取交易模式值
- `PositionSideEnum.LONG/SHORT` - 持仓方向枚举

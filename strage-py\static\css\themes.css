/* 主题切换样式 */

/* 明亮主题（默认） */
.theme-light {
    --bg-color: #f8f8f9;
    --text-color: #676a6c;
    --border-color: #e7eaec;
    --navbar-bg: #fff;
    --sidebar-bg: #2f4050;
    --sidebar-text: #a7b1c2;
    --sidebar-active: #1ab394;
}

/* 暗黑主题 */
.theme-dark {
    --bg-color: #1a1a1a;
    --text-color: #e0e0e0;
    --border-color: #333;
    --navbar-bg: #2d2d2d;
    --sidebar-bg: #1e1e1e;
    --sidebar-text: #b0b0b0;
    --sidebar-active: #4CAF50;
}

/* 应用主题变量 */
.theme-dark #page-wrapper {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
}

.theme-dark .navbar-static-top {
    background-color: var(--navbar-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

.theme-dark .navbar-top-links > li > a {
    color: var(--text-color) !important;
}

.theme-dark .navbar-top-links > li > a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.theme-dark .nav-second-level {
    background-color: var(--sidebar-bg) !important;
}

.theme-dark .nav-second-level li a {
    color: var(--sidebar-text) !important;
}

.theme-dark .nav-second-level li.active > a {
    background-color: var(--sidebar-active) !important;
    color: #fff !important;
}

.theme-dark .navbar-default .navbar-nav > .active > a {
    background-color: var(--sidebar-active) !important;
}

.theme-dark .page-tabs {
    background-color: var(--navbar-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

.theme-dark .page-tabs-content .menuTab {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

.theme-dark .page-tabs-content .menuTab.active {
    background-color: var(--navbar-bg) !important;
    border-bottom-color: var(--navbar-bg) !important;
}

.theme-dark .page-tabs-content .menuTab:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

.theme-dark .RuoYi_iframe {
    background-color: var(--bg-color) !important;
}

/* 表格暗黑主题 */
.theme-dark .table {
    color: var(--text-color) !important;
    background-color: transparent !important;
}

.theme-dark .table th {
    background-color: var(--navbar-bg) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .table td {
    border-color: var(--border-color) !important;
}

.theme-dark .table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.02) !important;
}

.theme-dark .table-hover > tbody > tr:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

/* 表单暗黑主题 */
.theme-dark .form-control {
    background-color: var(--bg-color) !important;
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

.theme-dark .form-control:focus {
    border-color: var(--sidebar-active) !important;
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25) !important;
}

.theme-dark .form-group label {
    color: var(--text-color) !important;
}

/* 按钮暗黑主题 */
.theme-dark .btn-default {
    background-color: var(--navbar-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

.theme-dark .btn-default:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.theme-dark .btn-primary {
    background-color: var(--sidebar-active) !important;
    border-color: var(--sidebar-active) !important;
}

/* 模态框暗黑主题 */
.theme-dark .modal-content {
    background-color: var(--navbar-bg) !important;
    color: var(--text-color) !important;
}

.theme-dark .modal-header {
    border-bottom-color: var(--border-color) !important;
}

.theme-dark .modal-footer {
    border-top-color: var(--border-color) !important;
}

/* 下拉菜单暗黑主题 */
.theme-dark .dropdown-menu {
    background-color: var(--navbar-bg) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .dropdown-menu > li > a {
    color: var(--text-color) !important;
}

.theme-dark .dropdown-menu > li > a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 面板暗黑主题 */
.theme-dark .panel {
    background-color: var(--navbar-bg) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .panel-heading {
    background-color: var(--bg-color) !important;
    border-bottom-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

.theme-dark .panel-body {
    color: var(--text-color) !important;
}

/* 工具提示暗黑主题 */
.theme-dark .tooltip-inner {
    background-color: var(--navbar-bg) !important;
    color: var(--text-color) !important;
}

/* 分页暗黑主题 */
.theme-dark .pagination > li > a {
    background-color: var(--navbar-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

.theme-dark .pagination > li > a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.theme-dark .pagination > .active > a {
    background-color: var(--sidebar-active) !important;
    border-color: var(--sidebar-active) !important;
}

/* 滚动条暗黑主题 */
.theme-dark ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.theme-dark ::-webkit-scrollbar-track {
    background: var(--bg-color);
}

.theme-dark ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

.theme-dark ::-webkit-scrollbar-thumb:hover {
    background: var(--sidebar-text);
}

/* 过渡动画 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 确保iframe内容也应用主题 */
.theme-dark iframe {
    filter: invert(1) hue-rotate(180deg);
}

/* 但是图片和视频不要反色 */
.theme-dark iframe img,
.theme-dark iframe video {
    filter: invert(1) hue-rotate(180deg);
}

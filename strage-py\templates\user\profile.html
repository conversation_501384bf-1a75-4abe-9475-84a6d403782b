<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <!-- CSS文件 - 使用CDN -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" />
    <link href="{{ url_for('static', filename='css/style.min.css') }}" rel="stylesheet" />

    <style>
        body {
            background-color: #f8f8f9;
            font: 14px Helvetica Neue, Helvetica, PingFang SC, 微软雅黑, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif !important;
        }

        .section-content {
            padding: 20px;
        }

        .user-info-head img {
            width: 120px;
            height: 120px;
            cursor: pointer;
            border: 3px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .user-info-head {
            margin-bottom: 15px;
        }

        .nav-tabs-custom {
            margin-bottom: 20px;
        }

        .nav-tabs-custom .nav-tabs {
            border-bottom: 2px solid #1ab394;
        }

        .nav-tabs-custom .nav-tabs li.active a {
            background-color: #1ab394;
            color: #fff;
            border-color: #1ab394;
        }

        .tab-content {
            padding: 20px 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .control-label {
            font-weight: 600;
            color: #676a6c;
        }

        .btn-group-actions {
            margin-top: 30px;
            text-align: center;
        }

        .ibox {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .ibox-title {
            background: #f8f8f9;
            border-bottom: 1px solid #e7eaec;
            padding: 15px 20px;
            border-radius: 4px 4px 0 0;
        }

        .ibox-content {
            padding: 20px;
        }

        .pr5 {
            padding-right: 5px;
        }

        /* 头像上传相关样式 */
        .avatar-upload {
            position: relative;
            display: inline-block;
        }

        .avatar-upload input[type="file"] {
            display: none;
        }

        .avatar-upload-btn {
            display: inline-block;
            padding: 5px 10px;
            background-color: #1ab394;
            color: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .avatar-upload-btn:hover {
            background-color: #18a085;
            text-decoration: none;
            color: white;
        }

        .upload-progress {
            margin-top: 10px;
            display: none;
        }

        .progress {
            height: 20px;
            margin-bottom: 10px;
            overflow: hidden;
            background-color: #f5f5f5;
            border-radius: 4px;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1);
        }

        .progress-bar {
            float: left;
            width: 0%;
            height: 100%;
            font-size: 12px;
            line-height: 20px;
            color: #fff;
            text-align: center;
            background-color: #1ab394;
            transition: width .6s ease;
        }
    </style>
</head>

<body class="gray-bg">
    <input id="userId" name="userId" type="hidden" value="{{ user.user_id }}" />
    <section class="section-content">
        <div class="row">
            <div class="col-sm-3 pr5">
                <div class="ibox float-e-margins">
                    <div class="ibox-title ibox-title-gray dashboard-header gray-bg">
                        <h5>个人资料</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="text-center">
                            <p class="user-info-head">
                                <img id="userAvatar" class="img-circle img-lg"
                                    src="{{ user.avatar if user.avatar else url_for('static', filename='img/profile.jpg') }}"
                                    onerror="this.src='{{ url_for('static', filename='img/profile.jpg') }}'">
                            </p>
                            <div class="avatar-upload">
                                <label for="avatarFile" class="avatar-upload-btn">
                                    <i class="fa fa-upload"></i> 修改头像
                                </label>
                                <input type="file" id="avatarFile" name="avatarFile" accept="image/*">
                            </div>
                            <div class="upload-progress" id="uploadProgress">
                                <div class="progress">
                                    <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%">
                                        <span id="progressText">0%</span>
                                    </div>
                                </div>
                                <div id="uploadStatus"></div>
                            </div>
                        </div>
                        <ul class="list-group list-group-striped">
                            <li class="list-group-item">
                                <b>登录账号</b> {{ user.login_name }}
                            </li>
                            <li class="list-group-item">
                                <b>手机号码</b> {{ user.phonenumber or '未设置' }}
                            </li>
                            <li class="list-group-item">
                                <b>用户邮箱</b> {{ user.email or '未设置' }}
                            </li>
                            <li class="list-group-item">
                                <b>创建时间</b> {{ user.create_time or '未知' }}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-sm-9 about">
                <div class="ibox float-e-margins">
                    <div class="ibox-title ibox-title-gray dashboard-header">
                        <h5>基本资料</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="nav-tabs-custom">
                            <ul class="nav nav-tabs">
                                <li class="active"><a href="#user_info" data-toggle="tab" aria-expanded="true">基本资料</a>
                                </li>
                                <li><a href="#modify_password" data-toggle="tab" aria-expanded="false">修改密码</a></li>
                            </ul>
                            <div class="tab-content">
                                <!--用户信息-->
                                <div class="tab-pane active" id="user_info">
                                    <form class="form-horizontal" id="form-user-edit">
                                        <input name="userId" type="hidden" value="{{ user.user_id }}">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">用户名称：</label>
                                            <div class="col-sm-10">
                                                <input name="userName" placeholder="请输入用户名称" class="form-control"
                                                    type="text" maxlength="30" value="{{ user.user_name }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">手机号码：</label>
                                            <div class="col-sm-10">
                                                <input name="phonenumber" placeholder="请输入手机号码" class="form-control"
                                                    type="text" maxlength="11" value="{{ user.phonenumber or '' }}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">邮箱：</label>
                                            <div class="col-sm-10">
                                                <input name="email" placeholder="请输入邮箱" class="form-control"
                                                    type="email" maxlength="50" value="{{ user.email or '' }}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">性别：</label>
                                            <div class="col-sm-10">
                                                <select name="sex" class="form-control">
                                                    <option value="0" {% if user.sex=='0' %}selected{% endif %}>男
                                                    </option>
                                                    <option value="1" {% if user.sex=='1' %}selected{% endif %}>女
                                                    </option>
                                                    <option value="2" {% if user.sex=='2' %}selected{% endif %}>未知
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">备注：</label>
                                            <div class="col-sm-10">
                                                <textarea name="remark" maxlength="500" class="form-control" rows="3"
                                                    placeholder="请输入备注信息">{{ user.remark or '' }}</textarea>
                                            </div>
                                        </div>
                                        <div class="btn-group-actions">
                                            <button type="button" class="btn btn-primary" onclick="submitUserInfo()">
                                                <i class="fa fa-check"></i> 保存
                                            </button>
                                            <button type="button" class="btn btn-default" onclick="closeItem()">
                                                <i class="fa fa-reply-all"></i> 关闭
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!--修改密码-->
                                <div class="tab-pane" id="modify_password">
                                    <form class="form-horizontal" id="form-user-resetPwd">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">旧密码：</label>
                                            <div class="col-sm-10">
                                                <input name="oldPassword" placeholder="请输入旧密码" class="form-control"
                                                    type="password" maxlength="20" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">新密码：</label>
                                            <div class="col-sm-10">
                                                <input id="newPassword" name="newPassword" placeholder="请输入新密码"
                                                    class="form-control" type="password" maxlength="20" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">确认密码：</label>
                                            <div class="col-sm-10">
                                                <input name="confirmPassword" placeholder="请确认新密码" class="form-control"
                                                    type="password" maxlength="20" required>
                                            </div>
                                        </div>
                                        <div class="btn-group-actions">
                                            <button type="button" class="btn btn-primary"
                                                onclick="submitChangePassword()">
                                                <i class="fa fa-check"></i> 保存
                                            </button>
                                            <button type="button" class="btn btn-default" onclick="resetForm()">
                                                <i class="fa fa-refresh"></i> 重置
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- JS文件 - 使用CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <!-- jQuery Validate -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.5/localization/messages_zh.min.js"></script>
    <!-- Layer -->
    <script src="{{ url_for('static', filename='ajax/libs/layer/layer.min.js') }}"></script>

    <script>
        var prefix = "/system/user/profile";

        // 头像上传功能
        $('#avatarFile').on('change', function () {
            var file = this.files[0];
            if (!file) {
                return;
            }

            // 验证文件类型
            var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (allowedTypes.indexOf(file.type) === -1) {
                layer.msg('只支持 JPG、PNG、GIF 格式的图片文件', { icon: 2 });
                this.value = '';
                return;
            }

            // 验证文件大小 (2MB)
            if (file.size > 2 * 1024 * 1024) {
                layer.msg('头像文件大小不能超过2MB', { icon: 2 });
                this.value = '';
                return;
            }

            // 显示上传进度
            $('#uploadProgress').show();
            $('#progressBar').css('width', '0%');
            $('#progressText').text('0%');
            $('#uploadStatus').text('准备上传...');

            // 创建FormData对象
            var formData = new FormData();
            formData.append('avatarfile', file);

            // 上传文件
            $.ajax({
                url: '/system/user/profile/updateAvatar',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function () {
                    var xhr = new window.XMLHttpRequest();
                    // 上传进度
                    xhr.upload.addEventListener('progress', function (evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = Math.round((evt.loaded / evt.total) * 100);
                            $('#progressBar').css('width', percentComplete + '%');
                            $('#progressText').text(percentComplete + '%');
                            $('#uploadStatus').text('上传中...');
                        }
                    }, false);
                    return xhr;
                },
                success: function (result) {
                    if (result.code === 0) {
                        // 上传成功
                        $('#progressBar').css('width', '100%');
                        $('#progressText').text('100%');
                        $('#uploadStatus').text('上传成功');

                        // 更新页面中的头像显示
                        $('#userAvatar').attr('src', result.data.avatar_url);

                        // 更新首页中的头像显示（如果在iframe中）
                        try {
                            if (window.parent && window.parent !== window) {
                                var parentDoc = window.parent.document;
                                $(parentDoc).find('img[src*="profile.jpg"], img[src*="avatars/"]').each(function () {
                                    if ($(this).hasClass('img-circle') || $(this).hasClass('user-image')) {
                                        $(this).attr('src', result.data.avatar_url);
                                    }
                                });
                            }
                        } catch (e) {
                            console.log('无法更新父页面头像，可能是跨域问题');
                        }

                        layer.msg('头像上传成功', { icon: 1 });

                        // 3秒后隐藏进度条
                        setTimeout(function () {
                            $('#uploadProgress').hide();
                        }, 3000);
                    } else {
                        // 上传失败
                        $('#uploadStatus').text('上传失败: ' + result.msg);
                        layer.msg(result.msg, { icon: 2 });

                        // 3秒后隐藏进度条
                        setTimeout(function () {
                            $('#uploadProgress').hide();
                        }, 3000);
                    }
                },
                error: function (xhr, status, error) {
                    $('#uploadStatus').text('上传失败: 网络错误');
                    layer.msg('上传失败，请检查网络连接', { icon: 2 });

                    // 3秒后隐藏进度条
                    setTimeout(function () {
                        $('#uploadProgress').hide();
                    }, 3000);
                }
            });

            // 清空文件选择，允许重复选择同一文件
            this.value = '';
        });

        // 用户信息表单验证
        $("#form-user-edit").validate({
            onkeyup: false,
            rules: {
                userName: {
                    required: true,
                    maxlength: 30
                },
                email: {
                    email: true,
                    remote: {
                        url: prefix + "/checkEmailUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "userId": function () {
                                return $("input[name='userId']").val();
                            },
                            "email": function () {
                                return $("input[name='email']").val().trim();
                            }
                        }
                    }
                },
                phonenumber: {
                    minlength: 11,
                    maxlength: 11,
                    digits: true,
                    remote: {
                        url: prefix + "/checkPhoneUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "userId": function () {
                                return $("input[name='userId']").val();
                            },
                            "phonenumber": function () {
                                return $("input[name='phonenumber']").val().trim();
                            }
                        }
                    }
                }
            },
            messages: {
                userName: {
                    required: "请输入用户名称",
                    maxlength: "用户名称长度不能超过30个字符"
                },
                email: {
                    email: "请输入正确的邮箱格式",
                    remote: "邮箱已经存在"
                },
                phonenumber: {
                    minlength: "手机号码必须为11位数字",
                    maxlength: "手机号码必须为11位数字",
                    digits: "手机号码只能包含数字",
                    remote: "手机号码已经存在"
                }
            },
            focusCleanup: true
        });

        // 提交用户信息 - 定义为全局函数
        window.submitUserInfo = function () {
            if ($("#form-user-edit").valid()) {
                var formData = $("#form-user-edit").serialize();

                $.ajax({
                    url: prefix + "/update",
                    type: 'post',
                    data: formData,
                    success: function (result) {
                        if (result.code == 0) {
                            layer.msg("修改成功", { icon: 1 });
                        } else {
                            layer.msg("修改失败：" + result.msg, { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg("修改失败，请检查网络连接", { icon: 2 });
                    }
                });
            }
        };

        // 修改密码表单验证
        $("#form-user-resetPwd").validate({
            onkeyup: false,
            rules: {
                oldPassword: {
                    required: true,
                    remote: {
                        url: prefix + "/checkPassword",
                        type: "post",
                        dataType: "json",
                        data: {
                            password: function () {
                                return $("input[name='oldPassword']").val();
                            }
                        }
                    }
                },
                newPassword: {
                    required: true,
                    minlength: 6,
                    maxlength: 20
                },
                confirmPassword: {
                    required: true,
                    equalTo: "#newPassword"
                }
            },
            messages: {
                oldPassword: {
                    required: "请输入原密码",
                    remote: "原密码错误"
                },
                newPassword: {
                    required: "请输入新密码",
                    minlength: "密码不能小于6个字符",
                    maxlength: "密码不能大于20个字符"
                },
                confirmPassword: {
                    required: "请再次输入新密码",
                    equalTo: "两次密码输入不一致"
                }
            },
            focusCleanup: true
        });

        // 提交修改密码
        function submitChangePassword() {
            if ($("#form-user-resetPwd").valid()) {
                var formData = $("#form-user-resetPwd").serialize();

                $.ajax({
                    url: prefix + "/resetPwd",
                    type: 'post',
                    data: formData,
                    success: function (result) {
                        if (result.code == 0) {
                            layer.msg("密码修改成功", { icon: 1 });
                            resetForm();
                        } else {
                            layer.msg("密码修改失败：" + result.msg, { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg("密码修改失败，请检查网络连接", { icon: 2 });
                    }
                });
            }
        }

        // 重置表单
        function resetForm() {
            $("#form-user-resetPwd")[0].reset();
            $("#form-user-resetPwd").validate().resetForm();
        }

        // 关闭页面
        function closeItem() {
            if (typeof parent.closeTab === 'function') {
                parent.closeTab();
            } else if (typeof closeTab === 'function') {
                closeTab();
            } else {
                window.close();
            }
        }
    </script>
</body>

</html>
"""
跟踪跟单实体类
迁移自: com.project.strategy.domain.entity.TrailingFollow
"""
from dataclasses import dataclass
from typing import Optional
from decimal import Decimal
import json


@dataclass
class TrailingFollow:
    """跟踪跟单实体"""
    
    trailing_id: Optional[int] = None  # 跟单的普通策略ID
    close_price: Optional[Decimal] = None  # 平仓价格
    follow_type: Optional[int] = None  # 跟单策略类型：1-止盈正开，2-止损正开，3-止盈反开
    rise_open: Optional[float] = None  # 开仓条件：上涨多少
    decline_trigger: Optional[float] = None  # 下跌触发开仓的条件
    decline_call: Optional[float] = None  # 下跌触发后，回调多少开仓
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        if self.trailing_id is not None and not isinstance(self.trailing_id, int):
            self.trailing_id = int(self.trailing_id) if str(self.trailing_id).isdigit() else None
            
        if self.follow_type is not None and not isinstance(self.follow_type, int):
            self.follow_type = int(self.follow_type) if str(self.follow_type).isdigit() else None
        
        # 处理浮点数字段
        float_fields = ['rise_open', 'decline_trigger', 'decline_call']
        for field_name in float_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, float):
                try:
                    setattr(self, field_name, float(value))
                except (ValueError, TypeError):
                    setattr(self, field_name, None)
        
        # 处理Decimal字段
        if self.close_price is not None and not isinstance(self.close_price, Decimal):
            try:
                self.close_price = Decimal(str(self.close_price))
            except (ValueError, TypeError):
                self.close_price = None
    
    def is_profit_same_direction(self) -> bool:
        """是否为止盈正开（与原订单相同方向）"""
        return self.follow_type == 1
    
    def is_loss_same_direction(self) -> bool:
        """是否为止损正开（与原订单相同方向）"""
        return self.follow_type == 2
    
    def is_profit_opposite_direction(self) -> bool:
        """是否为止盈反开（与原订单相反方向）"""
        return self.follow_type == 3
    
    def get_follow_type_name(self) -> str:
        """获取跟单类型名称"""
        if self.follow_type == 1:
            return "止盈正开"
        elif self.follow_type == 2:
            return "止损正开"
        elif self.follow_type == 3:
            return "止盈反开"
        else:
            return "未知"
    
    def is_valid(self) -> bool:
        """检查跟单信息是否有效"""
        return (
            self.trailing_id is not None and
            self.follow_type in [1, 2, 3] and
            self.rise_open is not None and
            self.rise_open > 0
        )
    
    def should_open_by_rise(self, current_price: Decimal, base_price: Decimal) -> bool:
        """
        检查是否应该因上涨而开仓
        
        Args:
            current_price: 当前价格
            base_price: 基准价格
            
        Returns:
            是否应该开仓
        """
        if (current_price is None or base_price is None or 
            self.rise_open is None or base_price == 0):
            return False
        
        rise_rate = float((current_price - base_price) / base_price * 100)
        return rise_rate >= self.rise_open
    
    def should_open_by_decline(self, current_price: Decimal, trigger_price: Decimal, 
                              highest_price: Decimal) -> bool:
        """
        检查是否应该因下跌回调而开仓
        
        Args:
            current_price: 当前价格
            trigger_price: 触发价格
            highest_price: 最高价格
            
        Returns:
            是否应该开仓
        """
        if (current_price is None or trigger_price is None or 
            highest_price is None or self.decline_call is None or
            highest_price == 0):
            return False
        
        # 检查是否已触发下跌条件
        if self.decline_trigger is not None:
            decline_from_trigger = float((trigger_price - current_price) / trigger_price * 100)
            if decline_from_trigger < self.decline_trigger:
                return False
        
        # 检查回调条件
        callback_rate = float((highest_price - current_price) / highest_price * 100)
        return callback_rate >= self.decline_call
    
    @classmethod
    def to_follow(cls, json_str: str) -> Optional['TrailingFollow']:
        """
        从JSON字符串创建TrailingFollow实例
        
        Args:
            json_str: JSON字符串
            
        Returns:
            TrailingFollow实例或None
        """
        try:
            data = json.loads(json_str)
            return cls.from_dict(data)
        except (json.JSONDecodeError, TypeError, ValueError):
            return None
    
    
    @classmethod
    def builder(cls) -> 'TrailingFollowBuilder':
        """创建构建器"""
        return TrailingFollowBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, Decimal):
                    result[key] = str(value)
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TrailingFollow':
        """从字典创建实例"""
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"TrailingFollow(trailing_id={self.trailing_id}, "
                f"follow_type={self.get_follow_type_name()}, rise_open={self.rise_open}%)")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"TrailingFollow(trailing_id={self.trailing_id}, close_price={self.close_price}, "
                f"follow_type={self.follow_type}, rise_open={self.rise_open}, "
                f"decline_trigger={self.decline_trigger}, decline_call={self.decline_call})")

    def to_json_string(self) -> str:
        """
        转换为JSON字符串
        对应Java中的toJSONString方法

        Returns:
            JSON字符串
        """
        import json
        from decimal import Decimal

        data = {
            'trailing_id': self.trailing_id,
            'close_price': str(self.close_price) if self.close_price else None,
            'follow_type': self.follow_type,
            'rise_open': self.rise_open,
            'decline_trigger': self.decline_trigger,
            'decline_call': self.decline_call
        }

        return json.dumps(data, ensure_ascii=False)

    @classmethod
    def from_json(cls, json_str: str) -> 'TrailingFollow':
        """
        从JSON字符串创建实例
        对应Java中的toFollow方法

        Args:
            json_str: JSON字符串

        Returns:
            TrailingFollow实例
        """
        import json
        from decimal import Decimal

        if not json_str:
            return cls()

        try:
            data = json.loads(json_str)

            return cls(
                trailing_id=data.get('trailing_id'),
                close_price=Decimal(str(data['close_price'])) if data.get('close_price') else None,
                follow_type=data.get('follow_type'),
                rise_open=data.get('rise_open'),
                decline_trigger=data.get('decline_trigger'),
                decline_call=data.get('decline_call')
            )
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            # 如果解析失败，返回空实例
            return cls()


class TrailingFollowBuilder:
    """TrailingFollow构建器"""
    
    def __init__(self):
        self._data = {}
    
    def trailing_id(self, trailing_id: int) -> 'TrailingFollowBuilder':
        self._data['trailing_id'] = trailing_id
        return self
    
    def close_price(self, close_price: Decimal) -> 'TrailingFollowBuilder':
        self._data['close_price'] = close_price
        return self
    
    def follow_type(self, follow_type: int) -> 'TrailingFollowBuilder':
        self._data['follow_type'] = follow_type
        return self
    
    def rise_open(self, rise_open: float) -> 'TrailingFollowBuilder':
        self._data['rise_open'] = rise_open
        return self
    
    def decline_trigger(self, decline_trigger: float) -> 'TrailingFollowBuilder':
        self._data['decline_trigger'] = decline_trigger
        return self
    
    def decline_call(self, decline_call: float) -> 'TrailingFollowBuilder':
        self._data['decline_call'] = decline_call
        return self
    
    def build(self) -> TrailingFollow:
        """构建TrailingFollow实例"""
        return TrailingFollow(**self._data)

"""
跟踪盈利服务接口
迁移自: com.project.strategy.service.ITrailingProfitService
"""
from abc import ABC, abstractmethod
from typing import List, Optional

from ..domain.entity.trailing_detail import TrailingDetail
from ..domain.entity.trailing_profit import TrailingProfit


class ITrailingProfitService(ABC):
    """跟踪盈利服务接口"""
    
    @abstractmethod
    def select_trailing_profit_by_id(self, id: int) -> Optional[TrailingProfit]:
        """
        根据ID查询跟踪盈利
        对应: selectTrailingProfitById
        
        Args:
            id: 跟踪盈利ID
            
        Returns:
            跟踪盈利对象，不存在返回None
        """
        pass
    
    @abstractmethod
    def select_trailing_follow_by_group(self, group_id: int) -> Optional[TrailingProfit]:
        """
        根据组ID查询跟踪跟单
        对应: selectTrailingFollowByGroup
        
        Args:
            group_id: 组ID
            
        Returns:
            跟踪盈利对象，不存在返回None
        """
        pass
    
    @abstractmethod
    def select_trailing_profit_list(self, trailing_profit: Optional[TrailingProfit] = None) -> List[TrailingProfit]:
        """
        查询跟踪盈利列表
        对应: selectTrailingProfitList
        
        Args:
            trailing_profit: 查询条件对象
            
        Returns:
            跟踪盈利列表
        """
        pass
    
    @abstractmethod
    def refresh_order(self) -> None:
        """
        刷新订单
        对应: refreshOrder
        """
        pass
    
    @abstractmethod
    def update_by_id(self, trailing_profit: TrailingProfit) -> bool:
        """
        根据ID更新跟踪盈利
        对应: updateById
        
        Args:
            trailing_profit: 跟踪盈利对象
            
        Returns:
            是否更新成功
        """
        pass
    
    @abstractmethod
    def start_strategy_group(self, user_id: int, id: int) -> int:
        """
        启动策略组
        对应: startStragegyGroup
        
        Args:
            user_id: 用户ID
            id: 策略ID
            
        Returns:
            影响行数
        """
        pass
    
    @abstractmethod
    def stop_strategy_group(self, user_id: int, id: int) -> int:
        """
        停止策略组
        对应: stopStragegyGroup
        
        Args:
            user_id: 用户ID
            id: 策略ID
            
        Returns:
            影响行数
        """
        pass
    
    @abstractmethod
    def add_strategy_group(self, user_id: int, ids: str) -> int:
        """
        添加策略组
        对应: addStragegyGroup
        
        Args:
            user_id: 用户ID
            ids: 策略ID字符串（逗号分隔）
            
        Returns:
            影响行数
        """
        pass
    
    @abstractmethod
    def update_detail_by_id(self, detail: TrailingDetail) -> bool:
        """
        根据ID更新跟踪详情
        对应: updateDetailById
        
        Args:
            detail: 跟踪详情对象
            
        Returns:
            是否更新成功
        """
        pass
    
    @abstractmethod
    def insert_trailing_profit(self, trailing_profit: TrailingProfit) -> bool:
        """
        插入跟踪盈利
        对应: insertTrailingProfit
        
        Args:
            trailing_profit: 跟踪盈利对象
            
        Returns:
            是否插入成功
        """
        pass
    
    @abstractmethod
    def update_trailing_profit(self, trailing_profit: TrailingProfit) -> bool:
        """
        更新跟踪盈利
        对应: updateTrailingProfit
        
        Args:
            trailing_profit: 跟踪盈利对象
            
        Returns:
            是否更新成功
        """
        pass
    
    @abstractmethod
    def delete_trailing_profit_by_ids(self, ids: str) -> int:
        """
        根据ID列表删除跟踪盈利
        对应: deleteTrailingProfitByIds
        
        Args:
            ids: ID字符串（逗号分隔）
            
        Returns:
            影响行数
        """
        pass
    
    @abstractmethod
    def delete_trailing_profit_by_id(self, id: int) -> int:
        """
        根据ID删除跟踪盈利
        对应: deleteTrailingProfitById
        
        Args:
            id: 跟踪盈利ID
            
        Returns:
            影响行数
        """
        pass
    
    @abstractmethod
    def position_update(self) -> None:
        """
        持仓更新
        对应: positionUpdate
        """
        pass
    
    @abstractmethod
    def start_follow(self) -> None:
        """
        开始跟单
        对应: startFollow
        """
        pass
    
    @abstractmethod
    def start_trailing(self) -> None:
        """
        开始跟踪
        对应: startTraling
        """
        pass

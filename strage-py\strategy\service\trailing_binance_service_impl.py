"""
跟踪Binance服务实现类
迁移自: com.project.strategy.service.impl.TrailingBinanceServiceImpl
"""
import json
import logging
from concurrent.futures import ThreadPoolExecutor
from decimal import Decimal
from typing import Any

from .i_account_service import IAccountService
from ..context.account_context import AccountContext
from ..domain.entity.trailing_follow import TrailingFollow
from ..domain.entity.trailing_profit import TrailingProfit
from ..enums import ExchangeEnum, OrderStateEnum, PositionSideEnum, InstrumentEnum
from ..global_cache import GlobalCache

logger = logging.getLogger(__name__)


class TrailingBinanceServiceImpl:
    """跟踪Binance服务实现类 - 处理跟踪策略"""
    
    def __init__(self, account_context: AccountContext, 
                 trailing_profit_service=None, 
                 thread_pool_size: int = 10):
        """
        初始化跟踪Binance服务
        
        Args:
            account_context: 账户上下文
            trailing_profit_service: 跟踪盈利服务
            thread_pool_size: 线程池大小
        """
        self.account_context = account_context
        self.trailing_profit_service = trailing_profit_service
        self.thread_pool = ThreadPoolExecutor(max_workers=thread_pool_size)
        logger.info(f"TrailingBinanceServiceImpl initialized with thread pool size: {thread_pool_size}")
    
    def sync_handle_trailing(self, symbol: str) -> None:
        """
        同步处理跟踪策略
        对应Java中的syncHandleTrailing方法

        Args:
            symbol: 交易对
        """
        strategy_map = GlobalCache.STRATEGY_TRAILING.get(symbol)
        if strategy_map is None or len(strategy_map) == 0:
            return

        for trailing in strategy_map.values():
            self.thread_pool.submit(self._handle_trailing_job, trailing, self.trailing_profit_service)
    
    def _handle_trailing_job(self, trailing: TrailingProfit, trailing_profit_service: Any) -> None:
        """
        处理跟踪任务
        对应Java中的HandleTrailingJob.run方法

        Args:
            trailing: 跟踪盈利对象
            trailing_profit_service: 跟踪盈利服务
        """
        try:
            if trailing.locked:
                return

            trailing.locked = True

            key_price = f"{ExchangeEnum.BINANCE.get_name()}_{trailing.symbol.upper()}"
            position_side_enum = PositionSideEnum.parse_value(trailing.position_side)

            # 处理跟单策略准备中状态
            if trailing.state == OrderStateEnum.FOLLOW_READY.get_code():
                cur_price = Decimal('0')
                if position_side_enum == PositionSideEnum.LONG:
                    cur_price = GlobalCache.PRICE.get(key_price).get('buying', Decimal('0'))
                    if cur_price < trailing.lowest_price:
                        trailing.lowest_price = cur_price
                        trailing_profit_service.update_by_id(trailing)
                    if cur_price > trailing.highest_price:
                        trailing.highest_price = cur_price
                        trailing_profit_service.update_by_id(trailing)
                else:
                    cur_price = GlobalCache.PRICE.get(key_price).get('selling', Decimal('0'))
                    if cur_price > trailing.lowest_price:
                        trailing.lowest_price = cur_price
                        trailing_profit_service.update_by_id(trailing)
                    if cur_price < trailing.highest_price:
                        trailing.highest_price = cur_price
                        trailing_profit_service.update_by_id(trailing)
                return

            # 检查开仓价格
            if trailing.open_price is None or trailing.open_price == Decimal('0'):
                return

            # 获取账户客户端
            account_client = self.account_context.get_account_client(
                ExchangeEnum.BINANCE,
                InstrumentEnum.FUTURES_U,
                trailing.account_name
            )

            # 处理多头持仓
            if position_side_enum == PositionSideEnum.LONG:
                cur_price = GlobalCache.PRICE.get(key_price).get('buying', Decimal('0'))
                if cur_price > trailing.highest_price:
                    trailing.highest_price = cur_price
                    trailing_profit_service.update_by_id(trailing)
                    return
                else:
                    if cur_price < trailing.lowest_price:
                        trailing.lowest_price = cur_price
                        trailing_profit_service.update_by_id(trailing)
                        if cur_price < trailing.stop_loss_price:
                            self._close_trailing(account_client, cur_price, position_side_enum, OrderStateEnum.STOP_LOSS, trailing, trailing_profit_service)
                            self._handle_follow_strategy(trailing, cur_price, OrderStateEnum.STOP_LOSS, trailing_profit_service)

                    is_profit = self._is_profit_stages(cur_price, position_side_enum, trailing, trailing_profit_service)
                    if is_profit:
                        self._close_trailing(account_client, cur_price, position_side_enum, OrderStateEnum.TAKE_PROFIT, trailing, trailing_profit_service)
                        self._handle_follow_strategy(trailing, cur_price, OrderStateEnum.TAKE_PROFIT, trailing_profit_service)
                        return

                    is_loss = self._is_loss_stages(cur_price, position_side_enum, trailing, trailing_profit_service)
                    if is_loss:
                        self._close_trailing(account_client, cur_price, position_side_enum, OrderStateEnum.STOP_LOSS, trailing, trailing_profit_service)
                        self._handle_follow_strategy(trailing, cur_price, OrderStateEnum.STOP_LOSS, trailing_profit_service)

            # 处理空头持仓
            elif position_side_enum == PositionSideEnum.SHORT:
                cur_price = GlobalCache.PRICE.get(key_price).get('selling', Decimal('0'))
                highest_price = trailing.highest_price
                # lowestPrice初始值是0
                if highest_price == Decimal('0') or cur_price < highest_price:
                    trailing.highest_price = cur_price
                    trailing_profit_service.update_by_id(trailing)
                    return
                else:
                    # 空单时，highestPrice是最低值，lowestPrice是最高值
                    if cur_price > trailing.lowest_price:
                        trailing.lowest_price = cur_price
                        trailing_profit_service.update_by_id(trailing)
                        if cur_price > trailing.stop_loss_price:
                            self._close_trailing(account_client, cur_price, position_side_enum, OrderStateEnum.STOP_LOSS, trailing, trailing_profit_service)
                            self._handle_follow_strategy(trailing, cur_price, OrderStateEnum.STOP_LOSS, trailing_profit_service)

                    is_profit = self._is_profit_stages(cur_price, position_side_enum, trailing, trailing_profit_service)
                    if is_profit:
                        self._close_trailing(account_client, cur_price, position_side_enum, OrderStateEnum.TAKE_PROFIT, trailing, trailing_profit_service)
                        self._handle_follow_strategy(trailing, cur_price, OrderStateEnum.TAKE_PROFIT, trailing_profit_service)

                    is_loss = self._is_loss_stages(cur_price, position_side_enum, trailing, trailing_profit_service)
                    if is_loss:
                        self._close_trailing(account_client, cur_price, position_side_enum, OrderStateEnum.STOP_LOSS, trailing, trailing_profit_service)
                        self._handle_follow_strategy(trailing, cur_price, OrderStateEnum.STOP_LOSS, trailing_profit_service)

        except Exception as e:
            logger.error(f"处理跟踪任务失败: {e}")
        finally:
            trailing.locked = False
    
    def _handle_follow_strategy(self, trailing: TrailingProfit, cur_price: Decimal, order_state_enum: OrderStateEnum, trailing_profit_service) -> None:
        """
        处理跟单策略
        对应Java中的handleFollowStrategy方法

        Args:
            trailing: 跟踪盈利对象
            cur_price: 当前价格
            order_state_enum: 订单状态枚举
            trailing_profit_service: 跟踪盈利服务
        """
        follow_trailing = trailing_profit_service.select_trailing_follow_by_group(trailing.group_id)
        if follow_trailing is None:
            return

        # 如果跟单策略配置的不是普通策略的币种，直接返回
        if trailing.symbol != follow_trailing.symbol:
            return

        follow_content = json.loads(follow_trailing.follow_content) if follow_trailing.follow_content else {}
        follow = TrailingFollow.from_dict(follow_content)
        follow.trailing_id = trailing.id
        follow.close_price = cur_price

        # 1-止盈正开，原策略订单止盈后，跟单的开仓方向与原订单相同
        # 2-止损正开：原策略订单止损后，跟单的开仓方向与原订单相同
        # 3-止盈反开：原策略订单止盈后，跟单的开仓方向与原订单相反
        # 4-止损反开：原策略订单止损后，跟单的开仓方向与原订单相反
        if follow.follow_type == 1 or follow.follow_type == 2:
            follow_trailing.position_side = trailing.position_side
        elif follow.follow_type == 3 or follow.follow_type == 4:
            if trailing.position_side == PositionSideEnum.LONG.get_value():
                follow_trailing.position_side = PositionSideEnum.SHORT.get_value()
            else:
                follow_trailing.position_side = PositionSideEnum.LONG.get_value()
        else:
            return

        if order_state_enum == OrderStateEnum.STOP_LOSS:
            if follow.follow_type == 2:
                self._follow_ready(follow_trailing, follow, trailing_profit_service)
            elif follow.follow_type == 4:
                self._follow_ready(follow_trailing, follow, trailing_profit_service)
        elif order_state_enum == OrderStateEnum.TAKE_PROFIT:
            if follow.follow_type == 1:
                self._follow_ready(follow_trailing, follow, trailing_profit_service)
            elif follow.follow_type == 3:
                self._follow_ready(follow_trailing, follow, trailing_profit_service)

    def _follow_ready(self, trailing: TrailingProfit, follow: TrailingFollow, trailing_profit_service) -> None:
        """
        跟单准备
        对应Java中的followReady方法

        Args:
            trailing: 跟踪盈利对象
            follow: 跟单配置对象
            trailing_profit_service: 跟踪盈利服务
        """
        trailing.follow_content = json.dumps(follow.to_dict())
        trailing.lowest_price = follow.close_price
        trailing.highest_price = follow.close_price
        trailing.state = OrderStateEnum.FOLLOW_READY.get_code()
        trailing_profit_service.update_by_id(trailing)

        strategy_map = GlobalCache.STRATEGY_TRAILING.get(trailing.symbol)
        if strategy_map is None:
            strategy_map = {}
            GlobalCache.STRATEGY_TRAILING[trailing.symbol] = strategy_map

        strategy_map[trailing.id] = trailing

    def _is_profit_stages(self, cur_price: Decimal, position_side_enum: PositionSideEnum, trailing: TrailingProfit, trailing_profit_service) -> bool:
        """
        检查分阶段止盈
        对应Java中的isProfitStages方法

        Args:
            cur_price: 当前价格
            position_side_enum: 持仓方向枚举
            trailing: 跟踪盈利对象
            trailing_profit_service: 跟踪盈利服务

        Returns:
            是否触发止盈
        """
        detail_list = trailing.get_trailing_detail_list(0)  # 0表示止盈
        if detail_list is None or len(detail_list) == 0:
            return False

        detail_list.sort(key=lambda d: d.price_gain)

        highest_price = trailing.highest_price

        # 判断是否触发第一档止盈
        is_trigger_stage = False
        if position_side_enum == PositionSideEnum.LONG:
            is_trigger_stage = highest_price > detail_list[0].trigger_price
        else:
            is_trigger_stage = highest_price < detail_list[0].trigger_price

        if is_trigger_stage:
            target_detail = detail_list[0]

            # 定位当前在哪个止盈档
            for detail in detail_list:
                if detail.state == 0:
                    # 是否触发此档止盈
                    is_trigger = False
                    if position_side_enum == PositionSideEnum.LONG:
                        is_trigger = highest_price > detail.trigger_price
                    else:
                        is_trigger = highest_price < detail.trigger_price

                    if is_trigger:
                        detail.state = 1
                        target_detail = detail
                        trailing_profit_service.update_detail_by_id(detail)
                else:
                    target_detail = detail

            # 回调幅度：计算当前价距最高点下跌了多少
            pull_back_gain = 0.0
            if position_side_enum == PositionSideEnum.LONG:
                pull_back_gain = float((highest_price - cur_price) / highest_price * 100)
            else:
                pull_back_gain = float((cur_price - highest_price) / highest_price * 100)

            # 如果回调幅度已经超过了该档位的止盈，则止盈
            if pull_back_gain >= target_detail.take_profit:
                return True

        return False

    def _is_loss_stages(self, cur_price: Decimal, position_side_enum: PositionSideEnum, trailing: TrailingProfit, trailing_profit_service) -> bool:
        """
        检查分阶段止损
        对应Java中的isLossStages方法

        Args:
            cur_price: 当前价格
            position_side_enum: 持仓方向枚举
            trailing: 跟踪盈利对象
            trailing_profit_service: 跟踪盈利服务

        Returns:
            是否触发止损
        """
        detail_list = trailing.get_trailing_detail_list(1)  # 1表示止损
        if detail_list is None or len(detail_list) == 0:
            return False

        detail_list.sort(key=lambda d: d.price_gain)

        lowest_price = trailing.lowest_price

        # 判断是否触发第一档止损
        is_trigger_stage = False
        if position_side_enum == PositionSideEnum.LONG:
            is_trigger_stage = lowest_price < detail_list[0].trigger_price
        else:
            is_trigger_stage = lowest_price > detail_list[0].trigger_price

        # 判断是否触发第一档止损
        if is_trigger_stage:
            target_detail = detail_list[0]

            # 定位当前在哪个止损档
            for detail in detail_list:
                if detail.state == 0:
                    # 是否触发此档止损
                    is_trigger = False
                    if position_side_enum == PositionSideEnum.LONG:
                        is_trigger = lowest_price < detail.trigger_price
                    else:
                        is_trigger = lowest_price > detail.trigger_price

                    if is_trigger:
                        detail.state = 1
                        target_detail = detail
                        trailing_profit_service.update_detail_by_id(detail)
                else:
                    target_detail = detail

            # 回调幅度：计算当前价距最低点上涨了多少
            pull_back_gain = 0.0
            if position_side_enum == PositionSideEnum.LONG:
                pull_back_gain = float((cur_price - lowest_price) / lowest_price * 100)
            else:
                pull_back_gain = float((lowest_price - cur_price) / lowest_price * 100)

            # 如果回调幅度已经超过了该档位的止损，则止损
            if pull_back_gain >= target_detail.take_profit:
                return True

        return False


    def _close_trailing(self, account_client: IAccountService, cur_price: Decimal,
                       position_side_enum: PositionSideEnum, state_enum: OrderStateEnum,
                       trailing: TrailingProfit, trailing_profit_service) -> None:
        """
        平仓跟踪
        对应Java中的closeTrailing方法

        Args:
            account_client: 账户客户端
            cur_price: 当前价格
            position_side_enum: 持仓方向枚举
            state_enum: 状态枚举
            trailing: 跟踪盈利对象
            trailing_profit_service: 跟踪盈利服务
        """
        close_pos_result = account_client.close_position(
            trailing.symbol,
            float(trailing.amount),
            position_side_enum
        )

        logger.info(f"closeTrailing: {trailing.id}, {state_enum.get_value()}, {close_pos_result}")

        is_close = False
        if close_pos_result.get('isCompleted', False):
            order_id = close_pos_result.get('orderId', '')
            trailing.ref_order_close = order_id
            # trailing.close_price = cur_price  # 此处的价格不准，在refresh处获取准确的值再回填
            trailing.state = state_enum.get_code()
            trailing_profit_service.update_by_id(trailing)
            logger.info(f"update trailing completed ******** {trailing.id}")
            GlobalCache.STRATEGY_TRAILING.get(trailing.symbol).pop(trailing.id, None)
            is_close = True
            logger.info(f"GlobalCache remove completed ******** {trailing.id}")

        # 再确认一下仓位是否存在
        position_map = account_client.get_position(trailing.symbol)
        if len(position_map) != 0:
            close_pos_result1 = account_client.close_position(
                trailing.symbol,
                float(trailing.amount),
                position_side_enum
            )
            logger.info(f"closeTrailing1: {trailing.id}, {state_enum.get_value()}, {close_pos_result1}")
            if close_pos_result1.get('isCompleted', False):
                order_id1 = close_pos_result1.get('orderId', '')
                trailing.ref_order_close = order_id1
                # trailing.close_price = cur_price  # 此处的价格不准，在refresh处获取准确的值再回填
                trailing.state = state_enum.get_code()
                trailing_profit_service.update_by_id(trailing)
                logger.info(f"update trailing1 completed ******** {trailing.id}")
                GlobalCache.STRATEGY_TRAILING.get(trailing.symbol).pop(trailing.id, None)
                logger.info(f"GlobalCache1 remove completed ******** {trailing.id}")
        else:
            if not is_close:
                tmp_order = close_pos_result.get('orderId', '')
                trailing.ref_order_close = tmp_order
                # trailing.close_price = cur_price  # 此处的价格不准，在refresh处获取准确的值再回填
                trailing.state = state_enum.get_code()
                trailing_profit_service.update_by_id(trailing)
                logger.info(f"update trailing2 completed ******** {trailing.id}")
                GlobalCache.STRATEGY_TRAILING.get(trailing.symbol).pop(trailing.id, None)



"""
跟踪组服务实现类
迁移自: com.project.strategy.service.impl.TrailingGroupServiceImpl
"""
import logging
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from .i_trailing_group_service import ITrailingGroupService
from ..dao.trailing_group_dao import TrailingGroupDAO
from ..domain.entity.trailing_group import TrailingGroup
from ..domain.entity.trailing_profit import TrailingProfit
from ..enums import OrderStateEnum

logger = logging.getLogger(__name__)


class TrailingGroupServiceImpl(ITrailingGroupService):
    """跟踪组服务实现类"""
    
    def __init__(self, db_session, trailing_profit_service=None, 
                 account_context=None, binance_service=None):
        """
        初始化跟踪组服务
        
        Args:
            db_session: 数据库会话
            trailing_profit_service: 跟踪盈利服务
            account_context: 账户上下文
            binance_service: Binance服务
        """
        self.db_session = db_session
        self.trailing_group_dao = TrailingGroupDAO()
        self.trailing_profit_service = trailing_profit_service
        self.account_context = account_context
        self.binance_service = binance_service
        logger.info("TrailingGroupServiceImpl initialized")
    
    def handle_group_state(self) -> None:
        """
        处理组状态
        对应Java中的handleGroupState方法
        """
        try:
            # 查询所有进行中的组
            group = TrailingGroup()
            group.state = 1  # 进行中状态
            group_list = self.select_trailing_group_list(group)
            
            for tmp_group in group_list:
                try:
                    # 查询组内的所有策略
                    if self.trailing_profit_service:
                        profit = TrailingProfit()
                        profit.group_id = tmp_group.id
                        profit_list = self.trailing_profit_service.select_trailing_profit_list(profit)
                        
                        if profit_list:
                            self._handle_group_and_profit(tmp_group, profit_list)
                    
                except Exception as e:
                    logger.error(f"处理组状态失败 - 组ID: {tmp_group.id}, 错误: {e}")
            
        except Exception as e:
            logger.error(f"处理组状态失败: {e}")
    
    def _handle_group_and_profit(self, group: TrailingGroup, profit_list: List[TrailingProfit]) -> None:
        """
        处理组和盈利策略
        
        Args:
            group: 跟踪组
            profit_list: 盈利策略列表
        """
        try:
            # 检查是否所有策略都完成
            if not self._completed_all(profit_list):
                return
            
            # 检查余额是否充足
            if not self._sufficient_balance(profit_list):
                group.state = 3  # 余额不足，停止策略
                self.update_trailing_group(group)
                logger.warning(f"组 {group.id} 余额不足，停止策略")
                return
            
            # 检查是否连续亏损
            if self._continue_loss(group, profit_list):
                group.state = 4  # 连续亏损，停止策略
                self.update_trailing_group(group)
                logger.warning(f"组 {group.id} 连续亏损，停止策略")
            else:
                # 复制组盈利
                copy_completed = self._copy_group_profit(group, profit_list)
                if copy_completed:
                    group.state = 2  # 完成状态
                    self.update_trailing_group(group)
                    logger.info(f"组 {group.id} 策略完成")
            
        except Exception as e:
            logger.error(f"处理组和盈利策略失败: {e}")
    
    def _completed_all(self, profit_list: List[TrailingProfit]) -> bool:
        """
        检查此策略组中的所有策略是否都完成了
        
        Args:
            profit_list: 盈利策略列表
            
        Returns:
            是否全部完成
        """
        try:
            for profit in profit_list:
                # 如果是跟单策略，并且跟单策略没有启动，而其他所有策略都为最终状态，则表示此跟单策略不会被执行
                if profit.strategy_type == 1 and profit.state == 0:
                    continue
                
                # 检查是否为最终状态（止损或止盈）
                if (profit.state != OrderStateEnum.STOP_LOSS.get_code() and 
                    profit.state != OrderStateEnum.TAKE_PROFIT.get_code()):
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查策略完成状态失败: {e}")
            return False
    
    def _sufficient_balance(self, profit_list: List[TrailingProfit]) -> bool:
        """
        检查余额是否充足
        对应Java中的sufficientBalance方法

        Args:
            profit_list: 盈利策略列表

        Returns:
            是否余额充足
        """
        try:
            sufficient_all = True

            for profit in profit_list:
                if not self.binance_service:
                    continue

                # 获取账户余额
                balance_account = self.binance_service.get_balance(profit.account_name)
                balance = balance_account.futures_u_balance if balance_account else Decimal('0')

                # 获取当前价格
                cur_price = Decimal('0')
                key_price = f"BINANCE_{profit.symbol.upper()}"

                from ..global_cache import GlobalCache
                price_data = GlobalCache.PRICE.get(key_price)
                if price_data:
                    from ..enums import PositionSideEnum
                    position_side_enum = PositionSideEnum.parse_value(profit.position_side)

                    if position_side_enum == PositionSideEnum.LONG:
                        cur_price = price_data.get('selling', Decimal('0'))
                    else:
                        cur_price = price_data.get('buying', Decimal('0'))

                if cur_price <= 0:
                    continue

                # 按照Java版本的计算公式：value = amount / leverage * curPrice
                value = (profit.amount / Decimal(str(profit.leverage))) * cur_price
                value = value.quantize(Decimal('0.000001'), rounding='ROUND_DOWN')

                if value > balance:
                    sufficient_all = False
                    break

            return sufficient_all
        except Exception as e:
            logger.error(f"检查余额充足性失败: {e}")
            return False
    

    
    def _continue_loss(self, group: TrailingGroup, profit_list: List[TrailingProfit]) -> bool:
        """
        检查是否连续亏损
        对应Java中的continueLoss方法

        Args:
            group: 跟踪组
            profit_list: 盈利策略列表

        Returns:
            是否连续亏损
        """
        try:
            # 检查是否所有订单都是止损状态
            all_loss = True
            for profit in profit_list:
                from ..enums import OrderStateEnum
                if profit.state != OrderStateEnum.STOP_LOSS.get_code():
                    all_loss = False
                    break

            # 不是全部订单都止损了
            if not all_loss:
                return False  # 本次订单不是全部亏损

            # 获取父组
            parent_id = group.parent_id
            parent_group = None
            if parent_id is None or parent_id == 0:
                parent_group = group
            else:
                parent_group = self.trailing_group_dao.select_trailing_group_by_id(parent_id)

            # 设置当前组的状态
            group.profit_state = 2
            group.loss_times = 1
            self.update_trailing_group(group)

            # 统计连续亏损次数
            continue_loss_time = self.trailing_group_dao.count_loss_time(parent_group.id)
            if continue_loss_time >= 3:  # 已经连续全部账户都亏损3次
                all_loss = True
            else:
                all_loss = False

            return all_loss

        except Exception as e:
            logger.error(f"检查连续亏损失败: {e}")
            return False
    
    def _copy_group_profit(self, group: TrailingGroup, profit_list: List[TrailingProfit]) -> bool:
        """
        复制组盈利
        对应Java中的copyGroupProfit方法

        Args:
            group: 跟踪组
            profit_list: 盈利策略列表

        Returns:
            是否复制完成
        """
        try:
            # 确定父组ID
            parent_id = group.parent_id
            if parent_id is None or parent_id == 0:
                parent_id = group.id

            # 创建新的跟踪组
            new_group = TrailingGroup.builder() \
                .parent_id(parent_id) \
                .user_id(group.user_id) \
                .state(1) \
                .build()

            # 插入新组
            if not self.insert_trailing_group(new_group):
                logger.error("插入新跟踪组失败")
                return False

            # 复制每个策略
            for profit in profit_list:
                # 创建新的跟踪策略
                from ..domain.entity.trailing_profit import TrailingProfit
                copy_trailing = TrailingProfit.builder() \
                    .group_id(new_group.id) \
                    .user_id(profit.user_id) \
                    .account_name(profit.account_name) \
                    .platform(profit.platform) \
                    .symbol(profit.symbol) \
                    .stop_loss_rate(profit.stop_loss_rate) \
                    .amount(profit.amount) \
                    .leverage(profit.leverage) \
                    .position_side(profit.position_side) \
                    .order_type(profit.order_type) \
                    .trade_mode(profit.trade_mode) \
                    .state(OrderStateEnum.UNHANDLE.get_code()) \
                    .strategy_type(profit.strategy_type) \
                    .follow_content(profit.follow_content) \
                    .build()

                # 如果是跟单策略，清空持仓方向
                if profit.strategy_type == 1:
                    copy_trailing.position_side = ""

                # 获取原策略的详情列表
                if self.trailing_profit_service:
                    original_profit = self.trailing_profit_service.select_trailing_profit_by_id(profit.id)
                    if original_profit and hasattr(original_profit, 'trailing_detail_list'):
                        detail_list = original_profit.trailing_detail_list
                        new_details = []

                        for detail in detail_list:
                            from ..domain.entity.trailing_detail import TrailingDetail
                            tmp_detail = TrailingDetail.builder() \
                                .price_gain(detail.price_gain) \
                                .take_profit(detail.take_profit) \
                                .state(0) \
                                .type(detail.type) \
                                .build()
                            new_details.append(tmp_detail)

                        copy_trailing.trailing_detail_list = new_details

                # 插入新策略
                if self.trailing_profit_service:
                    self.trailing_profit_service.insert_trailing_profit(copy_trailing)

            return True

        except Exception as e:
            logger.error(f"复制组盈利失败: {e}")
            return False
    
    def select_trailing_group_by_id(self, id: int) -> Optional[TrailingGroup]:
        """
        根据ID查询跟踪组
        
        Args:
            id: 组ID
            
        Returns:
            跟踪组对象，不存在返回None
        """
        try:
            return self.trailing_group_dao.select_trailing_group_by_id(id)
        except Exception as e:
            logger.error(f"根据ID查询跟踪组失败: {e}")
            return None
    
    def select_trailing_group_list(self, trailing_group: Optional[TrailingGroup] = None) -> List[TrailingGroup]:
        """
        查询跟踪组列表
        
        Args:
            trailing_group: 查询条件对象
            
        Returns:
            跟踪组列表
        """
        try:
            return self.trailing_group_dao.select_trailing_group_list(trailing_group)
        except Exception as e:
            logger.error(f"查询跟踪组列表失败: {e}")
            return []
    
    def insert_trailing_group(self, trailing_group: TrailingGroup) -> bool:
        """
        插入跟踪组
        
        Args:
            trailing_group: 跟踪组对象
            
        Returns:
            是否插入成功
        """
        try:
            trailing_group.create_time = datetime.now()
            return self.trailing_group_dao.insert_trailing_group(trailing_group)
        except Exception as e:
            logger.error(f"插入跟踪组失败: {e}")
            return False
    
    def update_trailing_group(self, trailing_group: TrailingGroup) -> bool:
        """
        更新跟踪组
        
        Args:
            trailing_group: 跟踪组对象
            
        Returns:
            是否更新成功
        """
        try:
            trailing_group.update_time = datetime.now()
            return self.trailing_group_dao.update_trailing_group(trailing_group)
        except Exception as e:
            logger.error(f"更新跟踪组失败: {e}")
            return False
    
    def delete_trailing_group_by_ids(self, ids: str) -> int:
        """
        根据ID列表批量删除跟踪组
        
        Args:
            ids: ID字符串，逗号分隔
            
        Returns:
            删除的记录数量
        """
        try:
            id_list = self._convert_to_str_array(ids)
            if not id_list:
                return 0
            return self.trailing_group_dao.delete_trailing_group_by_ids(id_list)
        except Exception as e:
            logger.error(f"批量删除跟踪组失败: {e}")
            return 0
    
    def delete_trailing_group_by_id(self, id: int) -> bool:
        """
        根据ID删除跟踪组
        
        Args:
            id: 组ID
            
        Returns:
            是否删除成功
        """
        try:
            return self.trailing_group_dao.delete_trailing_group_by_id(id)
        except Exception as e:
            logger.error(f"根据ID删除跟踪组失败: {e}")
            return False
    
    def _convert_to_str_array(self, ids: str) -> List[str]:
        """
        将逗号分隔的字符串转换为字符串数组
        
        Args:
            ids: 逗号分隔的ID字符串
            
        Returns:
            字符串数组
        """
        if not ids or not ids.strip():
            return []
        return [id_str.strip() for id_str in ids.split(',') if id_str.strip()]
    
    def __str__(self) -> str:
        """字符串表示"""
        return "TrailingGroupServiceImpl"
    
    def __repr__(self) -> str:
        """对象表示"""
        return "TrailingGroupServiceImpl()"

"""
Binance期货服务类
迁移自: com.project.strategy.service.impl.BinanceFuturesService
"""
import logging
import time
from decimal import Decimal
from typing import Dict, Optional, Any, List

from .abstract_account_common import AbstractAccountCommon
from ..domain.entity.ex_account import ExAccount
from ..domain.entity.trailing_profit import TrailingProfit
from ..enums import InstrumentEnum, PositionSideEnum, TradeModeEnum, ExchangeEnum, AccountTypeEnum
from ..utils import OkHttpUtil
from ..utils.signature_generator import SignatureGenerator

logger = logging.getLogger(__name__)


class BinanceFuturesService(AbstractAccountCommon):
    """Binance期货服务类"""

    BASE_FTURES = "https://fapi.binance.com"  # 保持与Java版本一致的拼写错误
    TRADE = "/fapi/v1/order"
    LEVERAGE = "/fapi/v1/leverage"
    ORDERS_ALL = "/fapi/v1/allOrders"
    MARGIN_TYPE = "/fapi/v1/marginType"
    BALANCE = "/fapi/v3/balance"
    POSITION = "/fapi/v3/positionRisk"
    SYMBOL_CONFIG = "/fapi/v1/symbolConfig"
    ORDER_HISTORY = "/fapi/v1/order"
    TRADE_HISTORY = "/fapi/v1/userTrades"

    def __init__(self, db_session, account: ExAccount):
        """
        初始化Binance期货服务

        Args:
            db_session: 数据库会话
            account: 交易所账户
        """
        self.db_session = db_session
        self.account = account
        self.api_key = account.apikey
        self.secret_key = account.secret_key
        self.account_name = account.account_name
        self.platform = account.platform

        # 设置属性 - 与Java版本保持一致
        self.recv_window = 5000
        self.exchange = ExchangeEnum.BINANCE
        self.account_type_list: Optional[List[AccountTypeEnum]] = None

        logger.info(f"BinanceFuturesService initialized for account: {self.account_name}")
    
    def get_instrument(self) -> InstrumentEnum:
        """
        获取交易工具
        
        Returns:
            交易工具枚举
        """
        return InstrumentEnum.FUTURES_U
    
    def fill_position(self, trailing: TrailingProfit) -> None:
        """
        填充持仓信息
        对应Java中的fillPosition方法

        Args:
            trailing: 跟踪盈利对象
        """
        try:
            if not trailing or not trailing.symbol:
                return

            symbol = trailing.symbol
            close_order = trailing.ref_order_close

            if not close_order:
                logger.warning(f"缺少平仓订单ID: {symbol}")
                return

            # 获取平仓价格
            close_price = self.get_close_price(symbol, close_order)

            # 获取持仓历史
            pos_json = self.get_position_history(symbol, close_order)

            if not pos_json:
                logger.warning(f"无法获取持仓历史: {symbol}, {close_order}")
                return

            realized_pnl = pos_json.get("realizedPnl", Decimal('0'))
            quote_qty = pos_json.get("quoteQty", Decimal('0'))

            # 计算实际数量：quoteQty / leverage
            if trailing.leverage and trailing.leverage > 0 and quote_qty > 0:
                real_qty = quote_qty / Decimal(str(trailing.leverage))

                # 计算盈利率：realizedPnl / realQty * 100
                if real_qty > 0:
                    profit_rate = float((realized_pnl / real_qty * 100).quantize(Decimal('0.0001')))
                else:
                    profit_rate = 0.0
            else:
                profit_rate = 0.0

            # 设置结果
            trailing.close_price = close_price
            trailing.profit_rate = profit_rate
            trailing.profit_value = realized_pnl

            logger.debug(f"填充持仓信息完成: {symbol}, 平仓价格: {close_price}, 盈利率: {profit_rate}%")

        except Exception as e:
            logger.error(f"填充持仓信息失败: {e}")

    def get_position_history(self, symbol: str, close_order_id: str) -> Optional[Dict[str, Any]]:
        """
        获取持仓历史
        对应Java中的getPositionHistory方法

        Args:
            symbol: 交易对
            close_order_id: 平仓订单ID

        Returns:
            包含realizedPnl和quoteQty的字典
        """
        res_json = {}
        query_string = f"symbol={symbol}&orderId={close_order_id}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.TRADE_HISTORY}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_get(url, header_map)

        import json
        arr = json.loads(res)
        return_json = arr[0]
        realized_pnl = Decimal(str(return_json.get("realizedPnl", "0")))
        quote_qty = Decimal(str(return_json.get("quoteQty", "0")))
        res_json["realizedPnl"] = realized_pnl
        res_json["quoteQty"] = quote_qty
        return res_json

    def get_close_price(self, symbol: str, close_order_id: str) -> Optional[Decimal]:
        """
        获取平仓价格
        对应Java中的getClosePrice方法

        Args:
            symbol: 交易对
            close_order_id: 平仓订单ID

        Returns:
            平均价格
        """
        query_string = f"symbol={symbol}&orderId={close_order_id}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.ORDER_HISTORY}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_get(url, header_map)

        import json
        json_obj = json.loads(res)
        avg_price = Decimal(str(json_obj.get("avgPrice", "0")))

        return avg_price

    def get_position(self, symbol: str) -> Dict[str, Dict[str, Any]]:
        """
        获取持仓信息
        对应Java中的getPosition方法

        Args:
            symbol: 交易对

        Returns:
            持仓信息字典
        """
        position_map = {}
        query_string = f"symbol={symbol}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.POSITION}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_get(url, header_map)

        try:
            import json
            arr = json.loads(res)
            if len(arr) == 0:
                return position_map

            for order_json in arr:
                market_price = Decimal(str(order_json.get("markPrice", "0")))
                entry_price = Decimal(str(order_json.get("entryPrice", "0")))
                un_realized_profit = Decimal(str(order_json.get("unRealizedProfit", "0")))
                position_margin = Decimal(str(order_json.get("positionInitialMargin", "0")))
                position_side = order_json.get("positionSide", "")

                tmp = {
                    "marketPrice": market_price,
                    "openPrice": entry_price,
                    "unRealizedProfit": un_realized_profit,
                    "positinMargin": position_margin  # 保持Java版本的拼写错误
                }
                position_map[f"{symbol}_{position_side}"] = tmp

        except Exception as e:
            import json
            try:
                json_obj = json.loads(res)
                if json_obj.get("code") is not None:
                    logger.error(f"BinanceFuturesService getPosition error>>> {self.account_name}, {symbol}, {json_obj.get('msg')}")
                    return {}
            except:
                logger.error(f"BinanceFuturesService getPosition error>>> {self.account_name}, {symbol}, {str(e)}")
                return {}

        return position_map

    def symbol_config(self, symbol: str) -> Dict[str, Any]:
        """
        获取交易对配置
        对应Java中的symbolConfig方法

        Args:
            symbol: 交易对

        Returns:
            交易对配置信息
        """
        result = {'symbol': symbol}
        query_string = f"symbol={symbol}&recvWindow=5000&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.SYMBOL_CONFIG}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_get(url, header_map)

        try:
            import json
            arr = json.loads(res)
            result["tradeMode"] = arr[0].get("marginType")
            result["leverage"] = arr[0].get("leverage")
        except Exception as e:
            import json
            try:
                json_obj = json.loads(res)
                if json_obj.get("code") is not None:
                    logger.error(f"BinanceFuturesService symbolConfig error>>> {self.account_name}, {symbol}, {json_obj.get('msg')}")
                    result = None
            except:
                logger.error(f"BinanceFuturesService symbolConfig error>>> {self.account_name}, {symbol}, {str(e)}")
                result = None

        return result

    def prepare_before(self, symbol: str, leverage: int) -> bool:
        """
        开仓前准备检查
        对应Java中的prepareBefore方法

        Args:
            symbol: 交易对
            leverage: 杠杆倍数

        Returns:
            是否准备就绪
        """
        try:
            is_ready = False

            # 获取交易对配置
            symbol_json = self.symbol_config(symbol)

            # 检查交易模式是否为逐仓
            is_isolated = False
            current_trade_mode = symbol_json.get('tradeMode')
            if current_trade_mode == TradeModeEnum.ISOLATED_BN.get_value():
                is_isolated = True
            else:
                # 设置为逐仓模式
                trade_mode_result = self.set_trade_mode_type(symbol, TradeModeEnum.ISOLATED_BN)
                is_isolated = trade_mode_result.get('isSuccess', False)

            # 检查杠杆是否匹配
            is_leverage = False
            current_leverage = symbol_json.get('leverage')
            if current_leverage == leverage:
                is_leverage = True
            else:
                # 设置杠杆
                lever_result = self.set_leverage(symbol, leverage)
                is_leverage = lever_result.get('isSuccess', False)

            # 两个条件都满足才认为准备就绪
            if is_isolated and is_leverage:
                is_ready = True

            logger.debug(f"开仓前准备检查: {symbol}, 逐仓: {is_isolated}, 杠杆: {is_leverage}, 就绪: {is_ready}")
            return is_ready

        except Exception as e:
            logger.error(f"开仓前准备检查失败: {e}")
            return False

    def close_position(self, symbol: str, quantity: float, position_side: PositionSideEnum) -> Dict[str, Any]:
        """
        平仓
        对应Java中的closePosition方法

        Args:
            symbol: 交易对
            quantity: 数量
            position_side: 持仓方向

        Returns:
            包含isCompleted、orderId、msg的结果字典
        """
        query_string = f"symbol={symbol}"
        if position_side == PositionSideEnum.LONG:
            # 平仓-多单
            query_string += f"&side=SELL&positionSide=LONG&type=MARKET&quantity={quantity}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"
        else:
            # 平仓-空单
            query_string += f"&side=BUY&positionSide=SHORT&type=MARKET&quantity={quantity}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"

        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        # 创建空的RequestBody，与Java版本保持一致
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.TRADE}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_post(url, {}, header_map)

        import json
        order = json.loads(res)
        msg = ""
        order_id = ""
        is_completed = False

        if order.get("code") is not None:
            is_completed = False
            msg = order.get("msg", "")
        else:
            is_completed = True
            order_id = order.get("orderId", "")

        result = {
            "isCompleted": is_completed,
            "orderId": order_id,
            "msg": msg
        }
        return result
    
    def open_position(self, symbol: str, quantity: float, leverage: int, position_side: PositionSideEnum) -> dict[
                                                                                                                 str, bool | str | Any] | None:
        """
        开仓
        对应Java中的openPosition方法

        Args:
            symbol: 交易对
            quantity: 数量
            leverage: 杠杆
            position_side: 持仓方向

        Returns:
            开仓结果
        """
        is_ready = self.prepare_before(symbol, leverage)
        if not is_ready:
            return None

        query_string = f"symbol={symbol}"
        if position_side == PositionSideEnum.LONG:
            # 建仓-做多
            query_string += f"&side=BUY&positionSide=LONG&type=MARKET&quantity={quantity}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"
        else:
            # 建仓-做空
            query_string += f"&side=SELL&positionSide=SHORT&type=MARKET&quantity={quantity}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"

        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        # 创建空的RequestBody，与Java版本保持一致
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.TRADE}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_post(url, {}, header_map)

        import json
        order = json.loads(res)
        msg = ""
        order_id = ""
        is_completed = False

        if order.get("code") is not None:
            is_completed = False
            msg = order.get("msg", "")
        else:
            is_completed = True
            order_id = order.get("orderId", "")

        result = {
            "isCompleted": is_completed,
            "orderId": order_id,
            "msg": msg
        }
        return result
    
    def set_trade_mode_type(self, symbol: str, margin_type: TradeModeEnum) -> Dict[str, Any]:
        """
        设置交易模式类型
        对应Java中的setTradeModeType方法

        Args:
            symbol: 交易对
            margin_type: 保证金类型

        Returns:
            包含isSuccess和msg的结果字典
        """
        query_string = f"symbol={symbol}&marginType={margin_type.get_value()}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        # 创建空的RequestBody，与Java版本保持一致
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.MARGIN_TYPE}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_post(url, {}, header_map)

        import json
        json_obj = json.loads(res)
        is_success = False
        msg = ""

        code = json_obj.get("code")
        if code == -4046 or code == 200:
            is_success = True
        else:
            logger.error(f"BinanceFuturesService setTradeModeType error>>> {self.account_name}, {json_obj.get('msg')}")
            is_success = False
            msg = json_obj.get("msg", "")

        return_json = {
            "isSuccess": is_success,
            "msg": msg
        }
        return return_json
    
    def set_leverage(self, symbol: str, leverage_level: int) -> Dict[str, Any]:
        """
        设置杠杆
        对应Java中的setLeverage方法

        Args:
            symbol: 交易对
            leverage_level: 杠杆倍数

        Returns:
            包含isSuccess和msg的结果字典
        """
        query_string = f"symbol={symbol}&leverage={leverage_level}&recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        # 创建空的RequestBody，与Java版本保持一致
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.LEVERAGE}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_post(url, {}, header_map)

        import json
        json_obj = json.loads(res)
        is_success = False
        msg = ""

        if json_obj.get("code") is not None:
            logger.error(f"BinanceFuturesService setLeverage error>>> {self.account_name}, {json_obj.get('msg')}")
            msg = json_obj.get("msg", "")
            is_success = False
        else:
            is_success = True

        return_json = {
            "isSuccess": is_success,
            "msg": msg
        }
        return return_json
    

    def get_balance(self) -> Decimal:
        """
        获取余额
        对应Java中的getBalance方法

        Returns:
            账户余额
        """
        balance = Decimal('0')
        query_string = f"recvWindow={self.recv_window}&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        header_map = self.get_header()
        url = f"{self.BASE_FTURES}{self.BALANCE}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_get(url, header_map)

        try:
            import json
            arr = json.loads(res)
            for item in arr:
                if item.get('asset') == 'USDT':
                    balance = Decimal(str(item.get('availableBalance', '0')))
                    break
        except Exception as e:
            import json
            try:
                json_obj = json.loads(res)
                if json_obj.get("code") is not None:
                    logger.error(f"BinanceFuturesService getBalance error>>> {self.account_name}, {json_obj.get('msg')}")
            except:
                logger.error(f"BinanceFuturesService getBalance error>>> {self.account_name}, {str(e)}")

        return balance

    def is_support(self) -> bool:
        """
        是否支持
        对应Java中的isSupport方法

        Returns:
            是否支持
        """
        return True


    def get_header(self) -> Dict[str, str]:
        """
        获取HTTP请求头
        对应Java中的getHeader方法

        Returns:
            请求头字典
        """
        header_map = {
            'User-Agent': 'binance-futures-connector-java/3.0.5',
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-MBX-APIKEY': self.api_key
        }
        return header_map



    def get_exchange(self) -> ExchangeEnum:
        """
        获取交易所

        Returns:
            交易所枚举
        """
        return ExchangeEnum.BINANCE

    def get_account_name(self) -> str:
        """
        获取账户名称

        Returns:
            账户名称
        """
        return self.account_name or ""

    def get_account(self) -> str:
        """
        获取账户

        Returns:
            账户信息
        """
        return getattr(self.account, 'ex_account', '') if self.account else ""

    def __str__(self) -> str:
        """字符串表示"""
        return f"BinanceFuturesService(account={self.account_name})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"BinanceFuturesService(account_name='{self.account_name}', instrument={self.get_instrument().get_value()})"

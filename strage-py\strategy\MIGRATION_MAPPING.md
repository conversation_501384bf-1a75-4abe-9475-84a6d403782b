# 策略交易模块迁移映射表

## 命名转换规则

### Java到Python命名转换
- **类名**: PascalCase → snake_case
  - `GlobalCache` → `global_cache`
  - `AccountTypeEnum` → `account_type_enum`
  - `TrailingProfit` → `trailing_profit`

- **方法名**: camelCase → snake_case
  - `getUserId()` → `get_user_id()`
  - `setAccountName()` → `set_account_name()`

- **变量名**: camelCase → snake_case
  - `userId` → `user_id`
  - `accountName` → `account_name`

- **常量名**: UPPER_CASE保持不变
  - `MAX_RETRY_COUNT` → `MAX_RETRY_COUNT`

## 文件映射表

### 枚举类 (enums/)
| Java文件 | Python文件 | 描述 |
|---------|-----------|------|
| AccountTypeEnum.java | account_type_enum.py | 账户类型枚举 |
| ExchangeEnum.java | exchange_enum.py | 交易所枚举 |
| InstrumentEnum.java | instrument_enum.py | 交易工具枚举 |
| OrderStateEnum.java | order_state_enum.py | 订单状态枚举 |
| OrderTypeEnum.java | order_type_enum.py | 订单类型枚举 |
| PositionSideEnum.java | position_side_enum.py | 持仓方向枚举 |
| StrategyStateEnum.java | strategy_state_enum.py | 策略状态枚举 |
| TradeModeEnum.java | trade_mode_enum.py | 交易模式枚举 |
| TransferInternalEnum.java | transfer_internal_enum.py | 内部转账枚举 |

### 领域模型 (domain/)
| Java文件 | Python文件 | 描述 |
|---------|-----------|------|
| BinanceAccountInfo.java | binance_account_info.py | Binance账户信息 |
| BinanceSymbol.java | binance_symbol.py | Binance交易对 |
| OrderBookItem.java | order_book_item.py | 订单簿项目 |
| OrderBookPlatform.java | order_book_platform.py | 订单簿平台 |
| OrderItem.java | order_item.py | 订单项目 |

### 实体类 (domain/entity/)
| Java文件 | Python文件 | 描述 |
|---------|-----------|------|
| AccountBalance.java | account_balance.py | 账户余额 |
| ExAccount.java | ex_account.py | 交易所账户 |
| SubscribeSymbol.java | subscribe_symbol.py | 订阅交易对 |
| TrailingDetail.java | trailing_detail.py | 跟踪详情 |
| TrailingFollow.java | trailing_follow.py | 跟踪跟单 |
| TrailingGroup.java | trailing_group.py | 跟踪组 |
| TrailingProfit.java | trailing_profit.py | 跟踪盈利 |

### 响应对象 (domain/response/)
| Java文件 | Python文件 | 描述 |
|---------|-----------|------|
| BinanceOrderBookResp.java | binance_order_book_resp.py | Binance订单簿响应 |

### 工具类 (utils/)
| Java文件 | Python文件 | 描述 |
|---------|-----------|------|
| AESUtil.java | aes_util.py | AES加密工具 |
| GZIPUtils.java | gzip_utils.py | GZIP压缩工具 |
| OkHttpUtil.java | ok_http_util.py | HTTP请求工具 |
| OkUtils.java | ok_utils.py | 通用工具 |

### 上下文类 (context/)
| Java文件 | Python文件 | 描述 |
|---------|-----------|------|
| AccountContext.java | account_context.py | 账户上下文 |

### 任务处理类 (task/)
| Java文件 | Python文件 | 描述 |
|---------|-----------|------|
| StrategyTask.java | strategy_task.py | 策略任务 |
| WSSListenTask.java | wss_listen_task.py | WebSocket监听任务 |

### 全局缓存
| Java文件 | Python文件 | 描述 |
|---------|-----------|------|
| GlobalCache.java | global_cache.py | 全局缓存管理 |

## 依赖关系分析

### 核心依赖
1. **GlobalCache** - 被多个模块依赖的核心缓存类
2. **枚举类** - 被实体类和业务逻辑广泛使用
3. **工具类** - 提供通用功能支持

### 迁移顺序建议
1. 枚举类 (最基础，无依赖)
2. 工具类 (基础功能)
3. 领域模型和实体类
4. 上下文类
5. 任务处理类
6. 全局缓存 (集成所有模块)

## Python特定考虑

### 数据类型映射
- `Long` → `int`
- `BigDecimal` → `decimal.Decimal`
- `Date` → `datetime.datetime`
- `List<T>` → `List[T]`
- `Map<K,V>` → `Dict[K,V]`

### 注解映射
- `@Data` → `@dataclass`
- `@Builder` → 自定义构建器方法
- `@NoArgsConstructor` → `__init__`方法
- `@AllArgsConstructor` → `__init__`方法

### 包结构
```
strategy/
├── __init__.py
├── global_cache.py
├── context/
│   ├── __init__.py
│   └── account_context.py
├── domain/
│   ├── __init__.py
│   ├── binance_account_info.py
│   ├── binance_symbol.py
│   ├── order_book_item.py
│   ├── order_book_platform.py
│   ├── order_item.py
│   ├── entity/
│   │   ├── __init__.py
│   │   ├── account_balance.py
│   │   ├── ex_account.py
│   │   ├── subscribe_symbol.py
│   │   ├── trailing_detail.py
│   │   ├── trailing_follow.py
│   │   ├── trailing_group.py
│   │   └── trailing_profit.py
│   └── response/
│       ├── __init__.py
│       └── binance_order_book_resp.py
├── enums/
│   ├── __init__.py
│   ├── account_type_enum.py
│   ├── exchange_enum.py
│   ├── instrument_enum.py
│   ├── order_state_enum.py
│   ├── order_type_enum.py
│   ├── position_side_enum.py
│   ├── strategy_state_enum.py
│   ├── trade_mode_enum.py
│   └── transfer_internal_enum.py
├── task/
│   ├── __init__.py
│   ├── strategy_task.py
│   └── wss_listen_task.py
└── utils/
    ├── __init__.py
    ├── aes_util.py
    ├── gzip_utils.py
    ├── ok_http_util.py
    └── ok_utils.py
```

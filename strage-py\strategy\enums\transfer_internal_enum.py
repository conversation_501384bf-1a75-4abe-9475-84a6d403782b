"""
内部转账枚举类
迁移自: com.project.strategy.enums.TransferInternalEnum
"""
from enum import Enum
from typing import Optional


class TransferInternalEnum(Enum):
    """内部转账枚举"""
    
    BN_MAIN_UMFUTURE = (1, "MAIN_UMFUTURE", "现货钱包转向U本位合约钱包")
    BN_UMFUTURE_MAIN = (2, "UMFUTURE_MAIN", "U本位合约钱包转向现货钱包")
    UNKNOWN = (-1, "null", "未知")
    
    def __init__(self, code: int, value: str, desc: str):
        """
        初始化内部转账枚举
        
        Args:
            code: 转账代码
            value: 转账值
            desc: 转账描述
        """
        self.code = code
        self.type_value = value  # 使用type_value避免与Enum.value冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取转账代码"""
        return self.code
    
    def get_value(self) -> str:
        """获取转账值"""
        return self.type_value
    
    def get_desc(self) -> str:
        """获取转账描述"""
        return self.desc
    
    @classmethod
    def parse_value(cls, value: str) -> Optional['TransferInternalEnum']:
        """
        根据值解析内部转账枚举
        
        Args:
            value: 转账值
            
        Returns:
            对应的内部转账枚举，如果未找到则返回None
        """
        for transfer_enum in cls:
            if transfer_enum.get_value() == value:
                return transfer_enum
        return None
    
    @classmethod
    def parse_code(cls, code: int) -> Optional['TransferInternalEnum']:
        """
        根据代码解析内部转账枚举
        
        Args:
            code: 转账代码
            
        Returns:
            对应的内部转账枚举，如果未找到则返回None
        """
        for transfer_enum in cls:
            if transfer_enum.get_code() == code:
                return transfer_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_value}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"TransferInternalEnum.{self.name}"

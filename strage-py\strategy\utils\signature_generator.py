"""
签名生成器
迁移自: com.common.utils.SignatureGenerator
"""
import hmac
import hashlib


class SignatureGenerator:
    """签名生成器类"""
    
    @staticmethod
    def get_signature(query_string: str, secret_key: str) -> str:
        """
        生成签名
        对应Java中的getSignature方法
        
        Args:
            query_string: 查询字符串
            secret_key: 密钥
            
        Returns:
            签名字符串
        """
        return hmac.new(
            secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

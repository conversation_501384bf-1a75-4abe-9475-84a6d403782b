"""
Binance账户信息领域模型
迁移自: com.project.strategy.domain.BinanceAccountInfo
"""
from dataclasses import dataclass
from typing import Optional
from datetime import datetime


@dataclass
class BinanceAccountInfo:
    """Binance账户信息"""
    
    id: Optional[int] = None
    name: Optional[str] = None
    uuid: Optional[str] = None
    token: Optional[str] = None
    authorization: Optional[str] = None
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    user_agent: Optional[str] = None
    content_length: Optional[str] = None
    sec_ua: Optional[str] = None
    sec_platform: Optional[str] = None
    sec_mode: Optional[str] = None
    sec_site: Optional[str] = None
    block_interval: Optional[int] = None
    open_diff_rate: Optional[float] = None
    close_diff_rate: Optional[float] = None
    status: Optional[int] = None
    update_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        if self.id is not None and not isinstance(self.id, int):
            self.id = int(self.id) if str(self.id).isdigit() else None
            
        if self.block_interval is not None and not isinstance(self.block_interval, int):
            self.block_interval = int(self.block_interval) if str(self.block_interval).isdigit() else None
            
        if self.open_diff_rate is not None and not isinstance(self.open_diff_rate, float):
            try:
                self.open_diff_rate = float(self.open_diff_rate)
            except (ValueError, TypeError):
                self.open_diff_rate = None
                
        if self.close_diff_rate is not None and not isinstance(self.close_diff_rate, float):
            try:
                self.close_diff_rate = float(self.close_diff_rate)
            except (ValueError, TypeError):
                self.close_diff_rate = None
                
        if self.status is not None and not isinstance(self.status, int):
            self.status = int(self.status) if str(self.status).isdigit() else None
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'BinanceAccountInfo':
        """从字典创建实例"""
        # 处理datetime字段
        if 'update_time' in data and data['update_time']:
            if isinstance(data['update_time'], str):
                try:
                    data['update_time'] = datetime.fromisoformat(data['update_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['update_time'] = None
        
        return cls(**data)
    
    def is_valid(self) -> bool:
        """验证账户信息是否有效"""
        return (
            self.api_key is not None and 
            self.api_secret is not None and 
            len(self.api_key.strip()) > 0 and 
            len(self.api_secret.strip()) > 0
        )
    
    def get_headers(self) -> dict:
        """获取HTTP请求头"""
        headers = {}
        
        if self.authorization:
            headers['Authorization'] = self.authorization
        if self.user_agent:
            headers['User-Agent'] = self.user_agent
        if self.content_length:
            headers['Content-Length'] = self.content_length
        if self.sec_ua:
            headers['sec-ch-ua'] = self.sec_ua
        if self.sec_platform:
            headers['sec-ch-ua-platform'] = self.sec_platform
        if self.sec_mode:
            headers['sec-fetch-mode'] = self.sec_mode
        if self.sec_site:
            headers['sec-fetch-site'] = self.sec_site
            
        return headers
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"BinanceAccountInfo(id={self.id}, name='{self.name}', status={self.status})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"BinanceAccountInfo(id={self.id}, name='{self.name}', "
                f"api_key='***', status={self.status})")

"""
订阅交易对服务接口
迁移自: com.project.strategy.service.ISubscribeSymbolService
"""
from abc import ABC, abstractmethod
from typing import List, Optional

from ..domain.entity.subscribe_symbol import SubscribeSymbol


class ISubscribeSymbolService(ABC):
    """订阅交易对服务接口"""
    
    @abstractmethod
    def select_subscribe_symbol_by_id(self, id: int) -> Optional[SubscribeSymbol]:
        """
        根据ID查询订阅交易对
        
        Args:
            id: 订阅ID
            
        Returns:
            订阅交易对对象，不存在返回None
        """
        pass
    
    @abstractmethod
    def select_subscribe_symbol_list(self, subscribe_symbol: SubscribeSymbol) -> List[SubscribeSymbol]:
        """
        查询订阅交易对列表

        Args:
            subscribe_symbol: 查询条件对象

        Returns:
            订阅交易对列表
        """
        pass
    
    @abstractmethod
    def insert_subscribe_symbol(self, subscribe_symbol: SubscribeSymbol) -> int:
        """
        插入订阅交易对

        Args:
            subscribe_symbol: 订阅交易对对象

        Returns:
            插入的记录数量
        """
        pass

    @abstractmethod
    def update_subscribe_symbol(self, subscribe_symbol: SubscribeSymbol) -> int:
        """
        更新订阅交易对

        Args:
            subscribe_symbol: 订阅交易对对象

        Returns:
            更新的记录数量
        """
        pass
    
    @abstractmethod
    def delete_subscribe_symbol_by_ids(self, ids: str) -> int:
        """
        根据ID列表批量删除订阅交易对
        
        Args:
            ids: ID字符串，逗号分隔
            
        Returns:
            删除的记录数量
        """
        pass
    
    @abstractmethod
    def delete_subscribe_symbol_by_id(self, id: int) -> int:
        """
        根据ID删除订阅交易对

        Args:
            id: 订阅ID

        Returns:
            删除的记录数量
        """
        pass

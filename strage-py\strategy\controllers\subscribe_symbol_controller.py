"""
订阅交易对控制器
迁移自: com.project.strategy.controller.SubscribeSymbolController
"""
import logging
from flask import Blueprint, render_template, request, jsonify
from services.auth_service import login_required
from strategy.service.subscribe_symbol_service_impl import SubscribeSymbolServiceImpl
from strategy.domain.entity.subscribe_symbol import SubscribeSymbol
from strategy.global_cache import GlobalCache
from utils.log_decorator import log_operation, BusinessType
from datetime import datetime

logger = logging.getLogger(__name__)

# 创建蓝图
subscribe_symbol_bp = Blueprint('subscribe_symbol', __name__, url_prefix='/strategy/symbol')

def get_subscribe_symbol_service():
    """获取订阅交易对服务实例"""
    return SubscribeSymbolServiceImpl()

def get_page_params():
    """获取分页参数"""
    page_num = int(request.form.get('pageNum', 1))
    page_size = int(request.form.get('pageSize', 10))
    return page_num, page_size

def get_symbol_price(platform: str, symbol: str) -> str:
    """
    从GlobalCache获取交易对价格

    Args:
        platform: 平台名称
        symbol: 交易对名称

    Returns:
        价格字符串，格式化为两位小数
    """
    try:
        # 构建缓存键：平台名_交易对名
        cache_key = f"{platform.upper()}_{symbol.upper()}"

        # 从GlobalCache获取价格数据
        price_data = GlobalCache.get_price(cache_key)

        if price_data:
            # 获取卖一价（selling）作为当前价格
            selling_price = price_data.get('selling')
            if selling_price:
                # 格式化为两位小数的字符串
                return f"{float(selling_price):.2f}"

        # 如果没有价格数据，返回默认值
        return "0.00"

    except Exception as e:
        logger.error(f"获取价格失败 {platform}_{symbol}: {e}")
        return "0.00"

def to_ajax_result(result):
    """
    转换为Ajax结果格式
    对应Java中的toAjax方法
    """
    if result:
        return {"code": 0, "msg": "操作成功"}
    else:
        return {"code": 1, "msg": "操作失败"}

def get_data_table(data_list, total=None):
    """
    获取数据表格信息
    对应Java中的getDataTable方法
    """
    if total is None:
        total = len(data_list)

    return {
        "code": 0,
        "msg": "查询成功",
        "rows": data_list,
        "total": total
    }

@subscribe_symbol_bp.route('/')
@login_required
def symbol():
    """
    订阅交易对列表页面
    对应: @GetMapping()
    """
    return render_template('strategy/symbol/list.html')

@subscribe_symbol_bp.route('/list', methods=['POST'])
@login_required
@log_operation("订阅交易对查询", BusinessType.OTHER)
def list_symbols():
    """
    查询订阅交易对列表
    对应: @PostMapping("/list")
    """
    try:
        # 获取查询参数 - 支持JSON和Form两种格式
        if request.is_json:
            # JSON格式
            data = request.get_json() or {}
            platform = data.get('platform', '').strip()
            symbol = data.get('symbol', '').strip()
            state = data.get('state', '').strip()
            message = data.get('message', '').strip()
            page_num = int(data.get('pageNum', 1))
            page_size = int(data.get('pageSize', 10))
        else:
            # Form格式
            platform = request.form.get('platform', '').strip()
            symbol = request.form.get('symbol', '').strip()
            state = request.form.get('state', '').strip()
            message = request.form.get('message', '').strip()
            page_num = int(request.form.get('pageNum', 1))
            page_size = int(request.form.get('pageSize', 10))

        logger.info(f"查询参数: platform={platform}, symbol={symbol}, state={state}, message={message}")
        # 构建查询条件
        subscribe_symbol = SubscribeSymbol()
        if platform:
            subscribe_symbol.platform = platform
        if symbol:
            subscribe_symbol.symbol = symbol
        if state:
            subscribe_symbol.state = int(state)
        if message:
            subscribe_symbol.message = message


        # 查询数据
        service = get_subscribe_symbol_service()
        symbol_list = service.select_subscribe_symbol_list(subscribe_symbol)

        # 转换为字典格式
        data_list = []
        for item in symbol_list:
            data_dict = item.to_dict()
            data_list.append(data_dict)

        # 返回分页数据
        return jsonify(get_data_table(data_list))

    except Exception as e:
        logger.error(f"查询订阅交易对列表失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '查询失败'
        })

@subscribe_symbol_bp.route('/getOptions', methods=['GET'])
@login_required
def get_options():
    """
    获取订阅交易对选项（带价格信息）
    对应: @GetMapping("/getOptions")
    """
    try:
        # 获取查询参数
        platform = request.args.get('platform', '').strip()
        symbol = request.args.get('symbol', '').strip()
        state = request.args.get('state', '').strip()

        # 构建查询条件
        subscribe_symbol = SubscribeSymbol()
        if platform:
            subscribe_symbol.platform = platform
        if symbol:
            subscribe_symbol.symbol = symbol
        if state:
            subscribe_symbol.state = int(state)

        # 查询数据
        service = get_subscribe_symbol_service()
        symbol_list = service.select_subscribe_symbol_list(subscribe_symbol)

        # 转换为字典格式并添加价格信息
        data_list = []
        for item in symbol_list:
            data_dict = item.to_dict()

            # 从GlobalCache获取价格信息
            price = get_symbol_price(item.platform, item.symbol)
            data_dict['price'] = price

            data_list.append(data_dict)

        return jsonify({
            'code': 0,
            'msg': '获取成功',
            'data': data_list
        })

    except Exception as e:
        logger.error(f"获取订阅交易对选项失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '获取选项失败'
        })

@subscribe_symbol_bp.route('/add', methods=['GET'])
@login_required
def add():
    """
    新增订阅交易对页面
    对应: @GetMapping("/add")
    """
    return render_template('strategy/symbol/add.html')

@subscribe_symbol_bp.route('/add', methods=['POST'])
@login_required
@log_operation("订阅交易对新增", BusinessType.INSERT)
def add_save():
    """
    新增保存订阅交易对
    对应: @PostMapping("/add")
    """
    try:
        # 获取表单数据
        platform = request.form.get('platform', '').strip()
        symbol = request.form.get('symbol', '').strip()
        state = request.form.get('state', '1').strip()
        message = request.form.get('message', '').strip()

        # 参数验证
        if not platform:
            return jsonify({
                'code': 1,
                'msg': '平台不能为空'
            })
        if not symbol:
            return jsonify({
                'code': 1,
                'msg': '交易对不能为空'
            })

        # 构建实体对象
        subscribe_symbol = SubscribeSymbol(
            platform=platform,
            symbol=symbol,
            state=int(state),
            message=message,
            create_time=datetime.now(),
            update_time=datetime.now()
        )

        # 保存数据
        service = get_subscribe_symbol_service()
        result = service.insert_subscribe_symbol(subscribe_symbol)

        return jsonify(to_ajax_result(result))

    except Exception as e:
        logger.error(f"新增订阅交易对失败: {e}")
        return jsonify({
            'code': 1,
            'msg': f'新增失败: {str(e)}'
        })

@subscribe_symbol_bp.route('/edit/<int:id>', methods=['GET'])
@login_required
def edit(id):
    """
    修改订阅交易对页面
    对应: @GetMapping("/edit/{id}")
    """
    try:
        service = get_subscribe_symbol_service()
        subscribe_symbol = service.select_subscribe_symbol_by_id(id)

        if not subscribe_symbol:
            return render_template('error/404.html'), 404

        return render_template('strategy/symbol/edit.html',
                             subscribe_symbol=subscribe_symbol)

    except Exception as e:
        logger.error(f"获取编辑页面失败: {e}")
        return render_template('error/500.html'), 500

@subscribe_symbol_bp.route('/edit', methods=['POST'])
@login_required
@log_operation("订阅交易对修改", BusinessType.UPDATE)
def edit_save():
    """
    修改保存订阅交易对
    对应: @PostMapping("/edit")
    """
    try:
        # 获取表单数据
        id = request.form.get('id', '').strip()
        platform = request.form.get('platform', '').strip()
        symbol = request.form.get('symbol', '').strip()
        state = request.form.get('state', '1').strip()
        message = request.form.get('message', '').strip()

        # 参数验证
        if not id:
            return jsonify({
                'code': 1,
                'msg': 'ID不能为空'
            })
        if not platform:
            return jsonify({
                'code': 1,
                'msg': '平台不能为空'
            })
        if not symbol:
            return jsonify({
                'code': 1,
                'msg': '交易对不能为空'
            })

        # 构建实体对象
        subscribe_symbol = SubscribeSymbol(
            id=int(id),
            platform=platform,
            symbol=symbol,
            state=int(state),
            message=message,
            update_time=datetime.now()
        )

        # 更新数据
        service = get_subscribe_symbol_service()
        result = service.update_subscribe_symbol(subscribe_symbol)

        return jsonify(to_ajax_result(result))

    except Exception as e:
        logger.error(f"修改订阅交易对失败: {e}")
        return jsonify({
            'code': 1,
            'msg': f'修改失败: {str(e)}'
        })

@subscribe_symbol_bp.route('/remove', methods=['POST'])
@login_required
@log_operation("订阅交易对删除", BusinessType.DELETE)
def remove():
    """
    删除订阅交易对
    对应: @PostMapping("/remove")
    """
    try:
        ids = request.form.get('ids', '').strip()

        if not ids:
            return jsonify({
                'code': 1,
                'msg': '请选择要删除的数据'
            })

        # 删除数据
        service = get_subscribe_symbol_service()
        result = service.delete_subscribe_symbol_by_ids(ids)

        return jsonify(to_ajax_result(result > 0))

    except Exception as e:
        logger.error(f"删除订阅交易对失败: {e}")
        return jsonify({
            'code': 1,
            'msg': f'删除失败: {str(e)}'
        })
"""
订单项目领域模型
迁移自: com.project.strategy.domain.OrderItem
"""
from dataclasses import dataclass
from typing import Optional
from decimal import Decimal


@dataclass
class OrderItem:
    """订单项目"""
    
    client_oid: Optional[str] = None  # 客户端订单ID
    account_id: Optional[int] = None  # 账户ID
    symbol: Optional[str] = None  # 交易对
    side: Optional[int] = None  # 开仓方向 1开多 2开空
    open_price: Optional[Decimal] = None  # 开仓价格
    timestamp: Optional[int] = None  # 上次开仓时间
    open_amount: Optional[str] = None  # 开仓数量
    leverage: Optional[int] = None  # 杠杆
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        if self.account_id is not None and not isinstance(self.account_id, int):
            self.account_id = int(self.account_id) if str(self.account_id).isdigit() else None
            
        if self.side is not None and not isinstance(self.side, int):
            self.side = int(self.side) if str(self.side).isdigit() else None
            
        if self.open_price is not None and not isinstance(self.open_price, Decimal):
            try:
                self.open_price = Decimal(str(self.open_price))
            except (ValueError, TypeError):
                self.open_price = None
                
        if self.timestamp is not None and not isinstance(self.timestamp, int):
            self.timestamp = int(self.timestamp) if str(self.timestamp).isdigit() else None
            
        if self.leverage is not None and not isinstance(self.leverage, int):
            self.leverage = int(self.leverage) if str(self.leverage).isdigit() else None
    
    def is_long(self) -> bool:
        """是否为多单"""
        return self.side == 1
    
    def is_short(self) -> bool:
        """是否为空单"""
        return self.side == 2
    
    def get_side_name(self) -> str:
        """获取方向名称"""
        if self.side == 1:
            return "多单"
        elif self.side == 2:
            return "空单"
        else:
            return "未知"
    
    def get_open_amount_decimal(self) -> Optional[Decimal]:
        """获取开仓数量的Decimal值"""
        if self.open_amount is None:
            return None
        try:
            return Decimal(self.open_amount)
        except (ValueError, TypeError):
            return None
    
    def get_open_value(self) -> Optional[Decimal]:
        """获取开仓价值（价格 * 数量）"""
        if self.open_price is None:
            return None
            
        open_amount_decimal = self.get_open_amount_decimal()
        if open_amount_decimal is None:
            return None
            
        return self.open_price * open_amount_decimal
    
    def get_leveraged_value(self) -> Optional[Decimal]:
        """获取杠杆后的价值"""
        open_value = self.get_open_value()
        if open_value is None or self.leverage is None:
            return None
            
        return open_value * Decimal(self.leverage)
    
    def is_valid(self) -> bool:
        """检查订单是否有效"""
        return (
            self.client_oid is not None and
            self.account_id is not None and
            self.symbol is not None and
            self.side in [1, 2] and
            self.open_price is not None and
            self.open_amount is not None and
            len(self.client_oid.strip()) > 0 and
            len(self.symbol.strip()) > 0 and
            len(self.open_amount.strip()) > 0
        )
    
    @classmethod
    def builder(cls) -> 'OrderItemBuilder':
        """创建构建器"""
        return OrderItemBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, Decimal):
                    result[key] = str(value)
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'OrderItem':
        """从字典创建实例"""
        # 处理Decimal字段
        if 'open_price' in data and data['open_price']:
            if isinstance(data['open_price'], str):
                try:
                    data['open_price'] = Decimal(data['open_price'])
                except (ValueError, TypeError):
                    data['open_price'] = None
        
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"OrderItem(client_oid='{self.client_oid}', symbol='{self.symbol}', "
                f"side={self.get_side_name()}, price={self.open_price})")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"OrderItem(client_oid='{self.client_oid}', account_id={self.account_id}, "
                f"symbol='{self.symbol}', side={self.side}, open_price={self.open_price}, "
                f"open_amount='{self.open_amount}', leverage={self.leverage})")


class OrderItemBuilder:
    """OrderItem构建器"""
    
    def __init__(self):
        self._data = {}
    
    def client_oid(self, client_oid: str) -> 'OrderItemBuilder':
        self._data['client_oid'] = client_oid
        return self
    
    def account_id(self, account_id: int) -> 'OrderItemBuilder':
        self._data['account_id'] = account_id
        return self
    
    def symbol(self, symbol: str) -> 'OrderItemBuilder':
        self._data['symbol'] = symbol
        return self
    
    def side(self, side: int) -> 'OrderItemBuilder':
        self._data['side'] = side
        return self
    
    def open_price(self, open_price: Decimal) -> 'OrderItemBuilder':
        self._data['open_price'] = open_price
        return self
    
    def timestamp(self, timestamp: int) -> 'OrderItemBuilder':
        self._data['timestamp'] = timestamp
        return self
    
    def open_amount(self, open_amount: str) -> 'OrderItemBuilder':
        self._data['open_amount'] = open_amount
        return self
    
    def leverage(self, leverage: int) -> 'OrderItemBuilder':
        self._data['leverage'] = leverage
        return self
    
    def build(self) -> OrderItem:
        """构建OrderItem实例"""
        return OrderItem(**self._data)

"""
交易模式枚举类
迁移自: com.project.strategy.enums.TradeModeEnum
"""
from enum import Enum
from typing import Optional


class TradeModeEnum(Enum):
    """交易模式枚举"""
    
    CROSSED_BN = (1, "CROSSED", "全仓")
    ISOLATED_BN = (2, "ISOLATED", "逐仓")
    UNKNOWN = (-1, "null", "未知")
    
    def __init__(self, code: int, value: str, desc: str):
        """
        初始化交易模式枚举
        
        Args:
            code: 模式代码
            value: 模式值
            desc: 模式描述
        """
        self.code = code
        self.type_value = value  # 使用type_value避免与Enum.value冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取模式代码"""
        return self.code
    
    def get_value(self) -> str:
        """获取模式值"""
        return self.type_value
    
    def get_desc(self) -> str:
        """获取模式描述"""
        return self.desc
    
    @classmethod
    def parse_code(cls, code: int) -> Optional['TradeModeEnum']:
        """
        根据代码解析交易模式枚举
        
        Args:
            code: 模式代码
            
        Returns:
            对应的交易模式枚举，如果未找到则返回None
        """
        for trade_mode_enum in cls:
            if trade_mode_enum.get_code() == code:
                return trade_mode_enum
        return None
    
    @classmethod
    def parse_value(cls, value: str) -> Optional['TradeModeEnum']:
        """
        根据值解析交易模式枚举
        
        Args:
            value: 模式值
            
        Returns:
            对应的交易模式枚举，如果未找到则返回None
        """
        for trade_mode_enum in cls:
            if trade_mode_enum.get_value() == value:
                return trade_mode_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_value}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"TradeModeEnum.{self.name}"

"""
用户数据访问层
处理用户相关的数据库操作
"""
import logging
from datetime import datetime
from dao.base_dao import BaseDAO
from models.user import User

logger = logging.getLogger(__name__)

class UserDAO(BaseDAO):
    """用户数据访问对象"""
    
    @staticmethod
    def select_user_list(page=1, size=10, search_params=None):
        """分页查询用户列表"""
        try:
            # 构建查询条件
            conditions = {"del_flag": "0"}
            
            if search_params:
                if search_params.get('login_name'):
                    conditions['login_name_like'] = search_params['login_name']
                if search_params.get('user_name'):
                    conditions['user_name_like'] = search_params['user_name']
                if search_params.get('phonenumber'):
                    conditions['phonenumber_like'] = search_params['phonenumber']
                if search_params.get('status'):
                    conditions['status'] = search_params['status']
                if search_params.get('begin_time'):
                    conditions['create_time_gte'] = search_params['begin_time']
                if search_params.get('end_time'):
                    conditions['create_time_lte'] = search_params['end_time']
            
            where_clause, where_params = BaseDAO.build_where_clause(conditions, [])
            
            # 查询总数
            total = BaseDAO.get_total_count('sys_user', where_clause, where_params)
            
            # 查询数据
            base_sql = """
                SELECT user_id, login_name, user_name, email, phonenumber, sex, 
                       avatar, status, del_flag, create_time, remark
                FROM sys_user
            """
            if where_clause:
                base_sql += f" WHERE {where_clause}"
            base_sql += " ORDER BY create_time DESC"
            
            paginated_sql, pagination_params = BaseDAO.build_pagination_sql(base_sql, page, size)
            all_params = where_params + pagination_params
            
            rows = BaseDAO.execute_query(paginated_sql, all_params)
            
            # 转换为User对象
            users = []
            for row in rows:
                user = User()
                user.user_id = row['user_id']
                user.login_name = row['login_name']
                user.user_name = row['user_name']
                user.email = row['email']
                user.phonenumber = row['phonenumber']
                user.sex = row['sex']
                user.avatar = row['avatar']
                user.status = row['status']
                user.del_flag = row['del_flag']
                user.create_time = row['create_time']
                user.remark = row['remark']
                users.append(user)
            
            return {
                'total': total,
                'rows': users
            }
            
        except Exception as e:
            logger.error(f"查询用户列表失败: {e}")
            raise
    
    @staticmethod
    def select_user_by_id(user_id):
        """根据用户ID查询用户"""
        try:
            sql = """
                SELECT user_id, login_name, user_name, email, phonenumber, sex, 
                       avatar, password, salt, status, del_flag, create_time, 
                       create_by, update_time, update_by, remark
                FROM sys_user 
                WHERE user_id = %s AND del_flag = '0'
            """
            rows = BaseDAO.execute_query(sql, [user_id])
            
            if rows:
                row = rows[0]
                user = User()
                user.user_id = row['user_id']
                user.login_name = row['login_name']
                user.user_name = row['user_name']
                user.email = row['email']
                user.phonenumber = row['phonenumber']
                user.sex = row['sex']
                user.avatar = row['avatar']
                user.password = row['password']
                user.salt = row['salt']
                user.status = row['status']
                user.del_flag = row['del_flag']
                user.create_time = row['create_time']
                user.create_by = row['create_by']
                user.update_time = row['update_time']
                user.update_by = row['update_by']
                user.remark = row['remark']
                return user
            
            return None
            
        except Exception as e:
            logger.error(f"根据ID查询用户失败: {e}")
            raise
    
    @staticmethod
    def select_user_by_login_name(login_name):
        """根据登录名查询用户"""
        try:
            sql = """
                SELECT user_id, login_name, user_name, email, phonenumber, sex, 
                       avatar, password, salt, status, del_flag, create_time, 
                       create_by, update_time, update_by, remark
                FROM sys_user 
                WHERE login_name = %s AND del_flag = '0'
            """
            rows = BaseDAO.execute_query(sql, [login_name])
            
            if rows:
                row = rows[0]
                user = User()
                user.user_id = row['user_id']
                user.login_name = row['login_name']
                user.user_name = row['user_name']
                user.email = row['email']
                user.phonenumber = row['phonenumber']
                user.sex = row['sex']
                user.avatar = row['avatar']
                user.password = row['password']
                user.salt = row['salt']
                user.status = row['status']
                user.del_flag = row['del_flag']
                user.create_time = row['create_time']
                user.create_by = row['create_by']
                user.update_time = row['update_time']
                user.update_by = row['update_by']
                user.remark = row['remark']
                return user
            
            return None
            
        except Exception as e:
            logger.error(f"根据登录名查询用户失败: {e}")
            raise
    
    @staticmethod
    def insert_user(user):
        """插入用户"""
        try:
            sql = """
                INSERT INTO sys_user (login_name, user_name, email, phonenumber, sex, 
                                    avatar, password, salt, status, del_flag, create_time, 
                                    create_by, remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            create_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            params = [
                user.login_name, user.user_name, user.email, user.phonenumber,
                user.sex, user.avatar, user.password, user.salt, user.status,
                user.del_flag, create_time, user.create_by, user.remark
            ]
            
            return BaseDAO.execute_insert(sql, params)
            
        except Exception as e:
            logger.error(f"插入用户失败: {e}")
            raise
    
    @staticmethod
    def update_user(user):
        """更新用户信息"""
        try:
            sql = """
                UPDATE sys_user 
                SET user_name = %s, email = %s, phonenumber = %s, sex = %s, 
                    status = %s, update_time = %s, update_by = %s, remark = %s
                WHERE user_id = %s AND del_flag = '0'
            """
            update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            params = [
                user.user_name, user.email, user.phonenumber, user.sex,
                user.status, update_time, user.update_by, user.remark,
                user.user_id
            ]
            
            return BaseDAO.execute_update(sql, params) > 0
            
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            raise
    
    @staticmethod
    def update_user_password(user_id, password, salt):
        """更新用户密码"""
        try:
            sql = """
                UPDATE sys_user
                SET password = %s, salt = %s, update_time = %s
                WHERE user_id = %s AND del_flag = '0'
            """
            update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            rows_affected = BaseDAO.execute_update(sql, [password, salt, update_time, user_id])
            return rows_affected > 0
        except Exception as e:
            logger.error(f"更新用户密码失败: {e}")
            raise
    
    @staticmethod
    def update_user_avatar(user_id, avatar_url):
        """更新用户头像"""
        try:
            sql = """
                UPDATE sys_user
                SET avatar = %s, update_time = %s
                WHERE user_id = %s AND del_flag = '0'
            """
            update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            rows_affected = BaseDAO.execute_update(sql, [avatar_url, update_time, user_id])
            return rows_affected > 0
        except Exception as e:
            logger.error(f"更新用户头像失败: {e}")
            raise
    
    @staticmethod
    def update_login_info(user_id, login_ip):
        """更新用户登录信息"""
        try:
            sql = """
                UPDATE sys_user 
                SET login_ip = %s, login_date = %s 
                WHERE user_id = %s AND del_flag = '0'
            """
            login_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            rows_affected = BaseDAO.execute_update(sql, [login_ip, login_date, user_id])
            return rows_affected > 0
        except Exception as e:
            logger.error(f"更新用户登录信息失败: {e}")
            raise
    
    @staticmethod
    def delete_user_by_ids(user_ids):
        """批量删除用户（逻辑删除）"""
        try:
            if not user_ids:
                return 0
            
            placeholders = ','.join(['%s'] * len(user_ids))
            sql = f"""
                UPDATE sys_user 
                SET del_flag = '2', update_time = %s 
                WHERE user_id IN ({placeholders}) AND del_flag = '0'
            """
            update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            params = [update_time] + list(user_ids)
            
            return BaseDAO.execute_update(sql, params)
            
        except Exception as e:
            logger.error(f"批量删除用户失败: {e}")
            raise
    
    @staticmethod
    def check_login_name_unique(login_name, user_id=None):
        """检查登录名是否唯一"""
        try:
            sql = "SELECT COUNT(*) as count FROM sys_user WHERE login_name = %s AND del_flag = '0'"
            params = [login_name]
            
            if user_id:
                sql += " AND user_id != %s"
                params.append(user_id)
            
            result = BaseDAO.execute_query(sql, params)
            return result[0]['count'] == 0
            
        except Exception as e:
            logger.error(f"检查登录名唯一性失败: {e}")
            raise
    
    @staticmethod
    def check_email_unique(email, user_id=None):
        """检查邮箱是否唯一"""
        try:
            sql = "SELECT COUNT(*) as count FROM sys_user WHERE email = %s AND del_flag = '0'"
            params = [email]
            
            if user_id:
                sql += " AND user_id != %s"
                params.append(user_id)
            
            result = BaseDAO.execute_query(sql, params)
            return result[0]['count'] == 0
            
        except Exception as e:
            logger.error(f"检查邮箱唯一性失败: {e}")
            raise
    
    @staticmethod
    def check_phone_unique(phonenumber, user_id=None):
        """检查手机号是否唯一"""
        try:
            sql = "SELECT COUNT(*) as count FROM sys_user WHERE phonenumber = %s AND del_flag = '0'"
            params = [phonenumber]
            
            if user_id:
                sql += " AND user_id != %s"
                params.append(user_id)
            
            result = BaseDAO.execute_query(sql, params)
            return result[0]['count'] == 0
            
        except Exception as e:
            logger.error(f"检查手机号唯一性失败: {e}")
            raise

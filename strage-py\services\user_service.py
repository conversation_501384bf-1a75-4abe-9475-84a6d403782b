"""
用户服务类
基于Java UserServiceImpl迁移
处理用户相关的业务逻辑
"""
import logging
import secrets
from datetime import datetime
from models.user import User
from dao.user_dao import UserDAO

logger = logging.getLogger(__name__)

class UserService:
    """用户服务类，处理用户相关的业务逻辑"""
    
    @staticmethod
    def select_user_list(page_num=1, page_size=10, search_params=None):
        """分页查询用户列表"""
        try:
            return UserDAO.select_user_list(page_num, page_size, search_params)
        except Exception as e:
            logger.error(f"查询用户列表失败: {e}")
            return {
                'total': 0,
                'rows': []
            }
    
    @staticmethod
    def select_user_by_id(user_id):
        """根据用户ID查询用户"""
        try:
            return UserDAO.select_user_by_id(user_id)
        except Exception as e:
            logger.error(f"根据ID查询用户失败: {e}")
            return None
    
    @staticmethod
    def select_user_by_login_name(login_name):
        """根据登录名查询用户"""
        try:
            return UserDAO.select_user_by_login_name(login_name)
        except Exception as e:
            logger.error(f"根据登录名查询用户失败: {e}")
            return None
    
    @staticmethod
    def insert_user(user):
        """新增用户"""
        try:
            # 业务逻辑验证
            if not user.login_name or not user.user_name:
                raise ValueError("登录账号和用户名称不能为空")
            
            # 检查登录名唯一性
            if not UserService.check_login_name_unique(user.login_name):
                raise ValueError("登录账号已存在")
            
            # 检查邮箱唯一性
            if user.email and not UserService.check_email_unique(user.email):
                raise ValueError("邮箱已存在")
            
            # 检查手机号唯一性
            if user.phonenumber and not UserService.check_phone_unique(user.phonenumber):
                raise ValueError("手机号已存在")
            
            return UserDAO.insert_user(user)
            
        except Exception as e:
            logger.error(f"新增用户失败: {e}")
            raise
    
    @staticmethod
    def update_user(user):
        """更新用户信息"""
        try:
            # 业务逻辑验证
            if not user.user_id or not user.user_name:
                raise ValueError("用户ID和用户名称不能为空")
            
            # 检查邮箱唯一性
            if user.email and not UserService.check_email_unique(user.email, user.user_id):
                raise ValueError("邮箱已存在")
            
            # 检查手机号唯一性
            if user.phonenumber and not UserService.check_phone_unique(user.phonenumber, user.user_id):
                raise ValueError("手机号已存在")
            
            return UserDAO.update_user(user)
            
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            raise
    
    @staticmethod
    def delete_user_by_ids(user_ids):
        """批量删除用户"""
        try:
            if not user_ids:
                return 0
            
            # 这里可以添加业务逻辑，比如检查用户是否可以删除
            # 例如：管理员用户不能删除等
            
            return UserDAO.delete_user_by_ids(user_ids)
            
        except Exception as e:
            logger.error(f"批量删除用户失败: {e}")
            raise
    
    @staticmethod
    def update_user_password(user_id, old_password, new_password):
        """更新用户密码"""
        try:
            # 获取用户信息
            user = UserDAO.select_user_by_id(user_id)
            if not user:
                raise ValueError("用户不存在")
            
            # 验证旧密码
            if not user.check_password(old_password):
                raise ValueError("原密码错误")
            
            # 生成新密码
            salt = secrets.token_hex(3)
            encrypted_password = User.encrypt_password(user.login_name, new_password, salt)
            
            return UserDAO.update_user_password(user_id, encrypted_password, salt)
            
        except Exception as e:
            logger.error(f"更新用户密码失败: {e}")
            raise
    
    @staticmethod
    def reset_user_password(user_id, new_password):
        """重置用户密码（管理员操作）"""
        try:
            # 获取用户信息
            user = UserDAO.select_user_by_id(user_id)
            if not user:
                raise ValueError("用户不存在")
            
            # 生成新密码
            salt = secrets.token_hex(3)
            encrypted_password = User.encrypt_password(user.login_name, new_password, salt)
            
            return UserDAO.update_user_password(user_id, encrypted_password, salt)
            
        except Exception as e:
            logger.error(f"重置用户密码失败: {e}")
            raise
    
    @staticmethod
    def update_user_avatar(user_id, avatar_url):
        """更新用户头像"""
        try:
            return UserDAO.update_user_avatar(user_id, avatar_url)
        except Exception as e:
            logger.error(f"更新用户头像失败: {e}")
            raise

    @staticmethod
    def change_status(user_id, status):
        """修改用户状态"""
        try:
            from datetime import datetime

            # 获取当前用户信息
            user = UserService.select_user_by_id(user_id)
            if not user:
                raise ValueError("用户不存在")

            # 更新状态
            user.status = status
            user.update_time = datetime.now()

            # 保存到数据库
            return UserDAO.update_user(user)

        except Exception as e:
            logger.error(f"修改用户状态失败: {e}")
            raise
    
    @staticmethod
    def update_login_info(user_id, login_ip):
        """更新用户登录信息"""
        try:
            return UserDAO.update_login_info(user_id, login_ip)
        except Exception as e:
            logger.error(f"更新用户登录信息失败: {e}")
            raise
    
    @staticmethod
    def check_login_name_unique(login_name, user_id=None):
        """检查登录名是否唯一"""
        try:
            return UserDAO.check_login_name_unique(login_name, user_id)
        except Exception as e:
            logger.error(f"检查登录名唯一性失败: {e}")
            return False
    
    @staticmethod
    def check_email_unique(email, user_id=None):
        """检查邮箱是否唯一"""
        try:
            return UserDAO.check_email_unique(email, user_id)
        except Exception as e:
            logger.error(f"检查邮箱唯一性失败: {e}")
            return False
    
    @staticmethod
    def check_phone_unique(phonenumber, user_id=None):
        """检查手机号是否唯一"""
        try:
            return UserDAO.check_phone_unique(phonenumber, user_id)
        except Exception as e:
            logger.error(f"检查手机号唯一性失败: {e}")
            return False
    
    @staticmethod
    def validate_user_data(user_data, is_edit=False):
        """验证用户数据"""
        errors = []

        # 必填字段验证 - 使用驼峰命名与前端保持一致
        # 编辑模式下不验证loginName，因为该字段为只读
        if not is_edit and not user_data.get('loginName', '').strip():
            errors.append("登录账号不能为空")

        if not user_data.get('userName', '').strip():
            errors.append("用户名称不能为空")

        # 邮箱格式验证
        email = user_data.get('email', '').strip()
        if email:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                errors.append("邮箱格式不正确")

        # 手机号格式验证
        phonenumber = user_data.get('phonenumber', '').strip()
        if phonenumber:
            if not phonenumber.isdigit() or len(phonenumber) != 11:
                errors.append("手机号格式不正确")

        return errors
    
    @staticmethod
    def create_user_from_form_data(form_data, create_by=None):
        """从表单数据创建用户对象"""
        user = User()
        user.login_name = form_data.get('loginName', '').strip()
        user.user_name = form_data.get('userName', '').strip()
        user.email = form_data.get('email', '').strip() or None
        user.phonenumber = form_data.get('phonenumber', '').strip() or None
        user.sex = form_data.get('sex', '0')
        user.status = form_data.get('status', '0')
        user.remark = form_data.get('remark', '').strip() or None
        user.create_by = create_by or 'admin'
        user.del_flag = '0'
        
        # 生成默认密码
        password = form_data.get('password', '123456')
        salt = secrets.token_hex(3)
        user.salt = salt
        user.password = User.encrypt_password(user.login_name, password, salt)
        
        return user
    
    @staticmethod
    def update_user_from_form_data(user, form_data, update_by=None):
        """从表单数据更新用户对象"""
        user.user_name = form_data.get('userName', '').strip()
        user.email = form_data.get('email', '').strip() or None
        user.phonenumber = form_data.get('phonenumber', '').strip() or None
        user.sex = form_data.get('sex', '0')
        user.status = form_data.get('status', '0')
        user.remark = form_data.get('remark', '').strip() or None
        user.update_by = update_by or 'admin'
        
        return user

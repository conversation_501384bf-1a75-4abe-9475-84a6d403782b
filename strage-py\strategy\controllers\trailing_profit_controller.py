"""
跟踪止盈控制器
迁移自: com.project.strategy.controller.TrailingProfitController
"""
import json
import logging
from flask import Blueprint, request, render_template, jsonify, session
from typing import Dict, Any

from utils.log_decorator import log_operation

class BusinessType:
    """业务类型枚举"""
    OTHER = 0
    INSERT = 1
    UPDATE = 2
    DELETE = 3
    GRANT = 4
    EXPORT = 5
    IMPORT = 6
    FORCE = 7
    GENCODE = 8
    CLEAN = 9
from ..domain.entity.trailing_profit import TrailingProfit
from ..domain.entity.trailing_follow import TrailingFollow
from ..enums.order_state_enum import OrderStateEnum
from ..service.service_manager import ServiceManager

logger = logging.getLogger(__name__)

# 创建蓝图
trailing_profit_bp = Blueprint('trailing_profit', __name__, url_prefix='/strategy/trailing')


class TrailingProfitController:
    """跟踪止盈控制器"""
    
    def __init__(self):
        self.prefix = "strategy/trailing"
        self.service_manager = ServiceManager()
    
    def get_user_id(self) -> int:
        """获取当前用户ID"""
        return session.get('user_id', 1)  # 默认用户ID为1，实际应从session获取


@trailing_profit_bp.route('/')
def trailing():
    """
    跟踪止盈主页面
    对应: @GetMapping()
    """
    return render_template('strategy/trailing/list.html')


@trailing_profit_bp.route('/list', methods=['POST'])
def list_trailing_profit():
    """
    查询跟踪止盈列表
    对应: @PostMapping("/list")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        # 获取查询参数
        user_name = request.form.get('userName', '').strip()
        platform = request.form.get('platform', '').strip()
        symbol = request.form.get('symbol', '').strip()
        state = request.form.get('state', '').strip()
        
        # 构建查询条件
        trailing_profit = TrailingProfit()
        trailing_profit.user_id = controller.get_user_id()
        
        if user_name:
            trailing_profit.user_name = user_name
        if platform:
            trailing_profit.platform = platform
        if symbol:
            trailing_profit.symbol = symbol
        if state:
            trailing_profit.state = int(state)
        
        # 获取分页参数
        page = int(request.form.get('page', 1))
        limit = int(request.form.get('limit', 10))
        
        # 查询数据
        data_list = service.select_trailing_profit_list(trailing_profit)
        
        # 简单分页处理
        start = (page - 1) * limit
        end = start + limit
        page_data = data_list[start:end]
        
        # 转换为字典格式
        result_data = []
        for item in page_data:
            item_dict = {
                'id': item.id,
                'group_id': item.group_id,
                'account_name': item.account_name,
                'platform': item.platform,
                'symbol': item.symbol,
                'open_price': str(item.open_price) if item.open_price else '',
                'market_price': str(item.market_price) if item.market_price else '',
                'stop_loss_price': str(item.stop_loss_price) if item.stop_loss_price else '',
                'amount': str(item.amount) if item.amount else '',
                'profit_value': str(item.profit_value) if item.profit_value else '',
                'profit_rate': item.profit_rate if item.profit_rate else 0,
                'leverage': item.leverage,
                'position_side': item.position_side,
                'state': item.state,
                'strategy_type': item.strategy_type
            }
            result_data.append(item_dict)
        
        return jsonify({
            'code': 0,
            'msg': '查询成功',
            'count': len(data_list),
            'data': result_data
        })
        
    except Exception as e:
        logger.error(f"查询跟踪止盈列表失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'查询失败: {str(e)}',
            'count': 0,
            'data': []
        })


@trailing_profit_bp.route('/add', methods=['GET'])
def add():
    """
    新增跟踪止盈页面
    对应: @GetMapping("/add")
    """
    return render_template('strategy/trailing/add.html')


@trailing_profit_bp.route('/follow/<int:id>')
def follow(id: int):
    """
    跟单策略配置页面
    对应: @GetMapping("/follow/{id}")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        trailing_profit = service.select_trailing_profit_by_id(id)
        if not trailing_profit:
            return render_template('error/404.html'), 404
            
        return render_template('strategy/trailing/follow.html', 
                             trailing_profit=trailing_profit)
        
    except Exception as e:
        logger.error(f"获取跟单策略配置失败: {e}")
        return render_template('error/500.html'), 500


@trailing_profit_bp.route('/add', methods=['POST'])
# @log_operation("跟踪止盈", BusinessType.INSERT)
def add_save():
    """
    新增保存跟踪止盈
    对应: @PostMapping("/add")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        # 构建跟踪止盈对象
        trailing_profit = TrailingProfit()
        trailing_profit.account_name = request.form.get('accountName', '').strip()
        trailing_profit.platform = request.form.get('platform', '').strip()
        trailing_profit.symbol = request.form.get('symbol', '').strip()
        trailing_profit.stop_loss_rate = float(request.form.get('stopLossRate', 0))
        trailing_profit.leverage = int(request.form.get('leverage', 1))
        trailing_profit.amount = float(request.form.get('amount', 0))
        trailing_profit.position_side = request.form.get('positionSide', '').strip()
        trailing_profit.strategy_type = int(request.form.get('strategyType', 0))
        
        # 设置默认值
        trailing_profit.state = OrderStateEnum.UNAVAILABLE.get_code()
        trailing_profit.user_id = controller.get_user_id()
        
        # 处理跟单策略
        if trailing_profit.strategy_type == 1:
            follow = TrailingFollow()
            follow.follow_type = int(request.form.get('followType', 1))
            follow.rise_open = float(request.form.get('riseOpen', 0))
            follow.decline_trigger = float(request.form.get('declineTrigger', 0))
            follow.decline_call = float(request.form.get('declineCall', 0))
            
            trailing_profit.follow_content = json.dumps(follow.__dict__)
        
        # 保存数据
        result = service.insert_trailing_profit(trailing_profit)
        
        if result:
            return jsonify({'code': 0, 'msg': '新增成功'})
        else:
            return jsonify({'code': 500, 'msg': '新增失败'})
            
    except Exception as e:
        logger.error(f"新增跟踪止盈失败: {e}")
        return jsonify({'code': 500, 'msg': f'新增失败: {str(e)}'})


@trailing_profit_bp.route('/edit/<int:id>')
def edit(id: int):
    """
    修改跟踪止盈页面
    对应: @GetMapping("/edit/{id}")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        trailing_profit = service.select_trailing_profit_by_id(id)
        if not trailing_profit:
            return render_template('error/404.html'), 404
            
        return render_template('strategy/trailing/edit.html', 
                             trailing_profit=trailing_profit)
        
    except Exception as e:
        logger.error(f"获取跟踪止盈编辑页面失败: {e}")
        return render_template('error/500.html'), 500


@trailing_profit_bp.route('/edit', methods=['POST'])
# @log_operation("跟踪止盈", BusinessType.UPDATE)
def edit_save():
    """
    修改保存跟踪止盈
    对应: @PostMapping("/edit")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        # 构建跟踪止盈对象
        trailing_profit = TrailingProfit()
        trailing_profit.id = int(request.form.get('id'))
        # 这里可以添加其他需要更新的字段
        
        result = service.update_trailing_profit(trailing_profit)
        
        if result:
            return jsonify({'code': 0, 'msg': '修改成功'})
        else:
            return jsonify({'code': 500, 'msg': '修改失败'})
            
    except Exception as e:
        logger.error(f"修改跟踪止盈失败: {e}")
        return jsonify({'code': 500, 'msg': f'修改失败: {str(e)}'})


@trailing_profit_bp.route('/addGroup', methods=['POST'])
# @log_operation("创建策略组", BusinessType.UPDATE)
def add_group():
    """
    创建策略组
    对应: @PostMapping("/addGroup")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        ids = request.form.get('ids', '').strip()
        result = service.add_strategy_group(controller.get_user_id(), ids)
        
        if result > 0:
            return jsonify({'code': 0, 'msg': '创建策略组成功'})
        else:
            return jsonify({'code': 500, 'msg': '创建策略组失败'})
            
    except Exception as e:
        logger.error(f"创建策略组失败: {e}")
        return jsonify({'code': 500, 'msg': f'创建策略组失败: {str(e)}'})


@trailing_profit_bp.route('/startGroup', methods=['POST'])
# @log_operation("启动Group", BusinessType.UPDATE)
def start_group():
    """
    启动策略组
    对应: @PostMapping("/startGroup")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        id = int(request.form.get('id'))
        result = service.start_strategy_group(controller.get_user_id(), id)
        
        if result > 0:
            return jsonify({'code': 0, 'msg': '启动策略组成功'})
        else:
            return jsonify({'code': 500, 'msg': '启动策略组失败'})
            
    except Exception as e:
        logger.error(f"启动策略组失败: {e}")
        return jsonify({'code': 500, 'msg': f'启动策略组失败: {str(e)}'})


@trailing_profit_bp.route('/stopGroup', methods=['POST'])
# @log_operation("停止Group", BusinessType.UPDATE)
def stop_group():
    """
    停止策略组
    对应: @PostMapping("/stopGroup")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        id = int(request.form.get('id'))
        result = service.stop_strategy_group(controller.get_user_id(), id)
        
        if result > 0:
            return jsonify({'code': 0, 'msg': '停止策略组成功'})
        else:
            return jsonify({'code': 500, 'msg': '停止策略组失败'})
            
    except Exception as e:
        logger.error(f"停止策略组失败: {e}")
        return jsonify({'code': 500, 'msg': f'停止策略组失败: {str(e)}'})


@trailing_profit_bp.route('/remove', methods=['POST'])
# @log_operation("跟踪止盈", BusinessType.DELETE)
def remove():
    """
    删除跟踪止盈
    对应: @PostMapping("/remove")
    """
    try:
        controller = TrailingProfitController()
        service = controller.service_manager.get_trailing_profit_service()
        
        ids = request.form.get('ids', '').strip()
        result = service.delete_trailing_profit_by_ids(ids)
        
        if result > 0:
            return jsonify({'code': 0, 'msg': '删除成功'})
        else:
            return jsonify({'code': 500, 'msg': '删除失败'})
            
    except Exception as e:
        logger.error(f"删除跟踪止盈失败: {e}")
        return jsonify({'code': 500, 'msg': f'删除失败: {str(e)}'})

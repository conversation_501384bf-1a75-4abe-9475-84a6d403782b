<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增用户</title>
    <!-- CSS文件 - 使用CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet" />

    <style>
        body {
            background-color: #f8f8f9;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }

        .main-content {
            padding: 20px;
            background-color: #fff;
            margin: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .form-header {
            color: #676a6c;
            border-bottom: 1px solid #e7eaec;
            padding-bottom: 10px;
            margin-bottom: 20px;
            margin-top: 30px;
        }

        .form-header:first-child {
            margin-top: 0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .control-label {
            font-weight: 600;
            color: #676a6c;
            text-align: right;
            padding-top: 7px;
        }

        .is-required:before {
            content: "*";
            color: #ed5565;
            margin-right: 4px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-switch span {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-switch span:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .toggle-switch input:checked+span {
            background-color: #1ab394;
        }

        .toggle-switch input:checked+span:before {
            transform: translateX(26px);
        }

        .btn-group-actions {
            margin-top: 30px;
            text-align: center;
        }

        .error {
            color: #ed5565;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="main-content">
        <form id="form-user-add" class="form-horizontal">
            <h4 class="form-header h4">基本信息</h4>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">用户名称：</label>
                        <div class="col-sm-8">
                            <input name="userName" placeholder="请输入用户名称" class="form-control" type="text" maxlength="30"
                                required>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">登录账号：</label>
                        <div class="col-sm-8">
                            <input id="loginName" name="loginName" placeholder="请输入登录账号" class="form-control"
                                type="text" maxlength="30" required>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">手机号码：</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input id="phonenumber" name="phonenumber" placeholder="请输入手机号码" class="form-control"
                                    type="text" maxlength="11">
                                <span class="input-group-addon"><i class="fa fa-mobile"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">邮箱：</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input id="email" name="email" class="form-control" type="text" maxlength="50"
                                    placeholder="请输入邮箱">
                                <span class="input-group-addon"><i class="fa fa-envelope"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
             
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">登录密码：</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input id="password" name="password" placeholder="请输入登录密码" class="form-control"
                                    type="password" value="123456" required>
                                <span class="input-group-addon" title="登录密码,鼠标按下显示密码"
                                    onmousedown="$('#password').attr('type','text')"
                                    onmouseup="$('#password').attr('type','password')"><i class="fa fa-key"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                 <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">用户性别：</label>
                        <div class="col-sm-8">
                            <select name="sex" class="form-control">
                                <option value="0">男</option>
                                <option value="1">女</option>
                                <option value="2">未知</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
               
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">用户状态：</label>
                        <div class="col-sm-8">
                            <label class="toggle-switch">
                                <input type="checkbox" id="status" checked>
                                <span></span>
                            </label>
                            <span style="margin-left: 10px;">正常</span>
                        </div>
                    </div>
                </div>
            </div>


            <h4 class="form-header h4">其他信息</h4>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-xs-2 control-label">备注：</label>
                        <div class="col-xs-10">
                            <textarea name="remark" maxlength="500" class="form-control" rows="3"
                                placeholder="请输入备注信息"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="btn-group-actions">
        <button type="button" class="btn btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i> 保存</button>
        <button type="button" class="btn btn-default" onclick="closeItem()" style="margin-left: 10px;"><i
                class="fa fa-reply-all"></i> 关闭</button>
    </div>

    <!-- JS文件 - 使用CDN -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    <!-- jQuery Validate -->
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/localization/messages_zh.min.js"></script>

    <script>
        var prefix = "/system/user";

        // 表单验证
        $("#form-user-add").validate({
            onkeyup: false,
            rules: {
                loginName: {
                    required: true,
                    minlength: 2,
                    maxlength: 20,
                    remote: {
                        url: prefix + "/checkLoginNameUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "loginName": function () {
                                return $("#loginName").val().trim();
                            }
                        }
                    }
                },
                userName: {
                    required: true,
                    maxlength: 30
                },
                password: {
                    required: true,
                    minlength: 5,
                    maxlength: 20
                },
                email: {
                    email: true,
                    remote: {
                        url: prefix + "/checkEmailUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "email": function () {
                                return $("#email").val().trim();
                            }
                        }
                    }
                },
                phonenumber: {
                    minlength: 11,
                    maxlength: 11,
                    digits: true,
                    remote: {
                        url: prefix + "/checkPhoneUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "phonenumber": function () {
                                return $("#phonenumber").val().trim();
                            }
                        }
                    }
                }
            },
            messages: {
                loginName: {
                    required: "请输入登录账号",
                    minlength: "登录账号长度不能小于2个字符",
                    maxlength: "登录账号长度不能超过20个字符",
                    remote: "用户已经存在"
                },
                userName: {
                    required: "请输入用户名称",
                    maxlength: "用户名称长度不能超过30个字符"
                },
                password: {
                    required: "请输入登录密码",
                    minlength: "密码长度不能小于5个字符",
                    maxlength: "密码长度不能超过20个字符"
                },
                email: {
                    email: "请输入正确的邮箱格式",
                    remote: "邮箱已经存在"
                },
                phonenumber: {
                    minlength: "手机号码必须为11位数字",
                    maxlength: "手机号码必须为11位数字",
                    digits: "手机号码只能包含数字",
                    remote: "手机号码已经存在"
                }
            },
            focusCleanup: true
        });

        // 提交处理
        function submitHandler() {
            if ($("#form-user-add").valid()) {
                var formData = $("#form-user-add").serializeArray();
                var status = $("#status").is(':checked') ? '0' : '1';
                formData.push({ "name": "status", "value": status });

                $.ajax({
                    url: prefix + "/add",
                    type: 'post',
                    data: formData,
                    success: function (result) {
                        if (result.code == 0) {
                            alert("新增成功");
                            closeItem();
                        } else {
                            alert("新增失败：" + result.msg);
                        }
                    },
                    error: function () {
                        alert("新增失败，请检查网络连接");
                    }
                });
            }
        }

        // 关闭页面
        function closeItem() {
            if (typeof parent.closeTab === 'function') {
                parent.closeTab();
            } else if (typeof closeTab === 'function') {
                closeTab();
            } else {
                window.close();
            }
        }

        // 状态切换显示
        $("#status").change(function () {
            var statusText = $(this).is(':checked') ? '正常' : '停用';
            $(this).parent().next('span').text(statusText);
        });
    </script>
</body>

</html>
"""
账户服务接口
迁移自: com.project.strategy.service.IAccountService
"""
from abc import ABC, abstractmethod
from decimal import Decimal
from typing import Dict, Any

from ..domain.entity.trailing_profit import TrailingProfit
from ..enums import ExchangeEnum, InstrumentEnum, PositionSideEnum, TradeModeEnum, TransferInternalEnum


class IAccountService(ABC):
    """账户服务接口"""
    
    @abstractmethod
    def fill_position(self, trailing: TrailingProfit) -> None:
        """
        填充持仓信息
        
        Args:
            trailing: 跟踪盈利对象
        """
        pass
    
    @abstractmethod
    def get_position(self, symbol: str) -> Dict[str, Dict[str, Any]]:
        """
        获取持仓信息
        
        Args:
            symbol: 交易对
            
        Returns:
            持仓信息字典
        """
        pass
    
    @abstractmethod
    def close_position(self, symbol: str, quantity: float, position_side: PositionSideEnum) -> Dict[str, Any]:
        """
        平仓
        
        Args:
            symbol: 交易对
            quantity: 数量
            position_side: 持仓方向
            
        Returns:
            平仓结果
        """
        pass
    
    @abstractmethod
    def open_position(self, symbol: str, quantity: float, leverage: int, position_side: PositionSideEnum) -> Dict[str, Any]:
        """
        开仓
        
        Args:
            symbol: 交易对
            quantity: 数量
            leverage: 杠杆
            position_side: 持仓方向
            
        Returns:
            开仓结果
        """
        pass
    
    @abstractmethod
    def set_trade_mode_type(self, symbol: str, margin_type: TradeModeEnum) -> Dict[str, Any]:
        """
        设置交易模式类型
        
        Args:
            symbol: 交易对
            margin_type: 保证金类型
            
        Returns:
            设置结果
        """
        pass
    
    @abstractmethod
    def set_leverage(self, symbol: str, leverage_level: int) -> Dict[str, Any]:
        """
        设置杠杆
        
        Args:
            symbol: 交易对
            leverage_level: 杠杆倍数
            
        Returns:
            设置结果
        """
        pass
    
    @abstractmethod
    def transfer_asset(self, account_name: str, amount: Decimal, transfer_internal_enum: TransferInternalEnum) -> bool:
        """
        资产转账
        
        Args:
            account_name: 账户名称
            amount: 转账金额
            transfer_internal_enum: 转账类型
            
        Returns:
            是否转账成功
        """
        pass
    
    @abstractmethod
    def get_balance(self) -> Decimal:
        """
        获取余额
        
        Returns:
            账户余额
        """
        pass
    
    @abstractmethod
    def is_support(self) -> bool:
        """
        是否支持
        
        Returns:
            是否支持此服务
        """
        pass
    
    @abstractmethod
    def get_exchange(self) -> ExchangeEnum:
        """
        获取交易所
        
        Returns:
            交易所枚举
        """
        pass
    
    @abstractmethod
    def get_instrument(self) -> InstrumentEnum:
        """
        获取交易工具
        
        Returns:
            交易工具枚举
        """
        pass
    
    @abstractmethod
    def get_account_name(self) -> str:
        """
        获取账户名称
        
        Returns:
            账户名称
        """
        pass
    
    @abstractmethod
    def get_account(self) -> str:
        """
        获取账户
        
        Returns:
            账户信息
        """
        pass

"""
Binance订单簿响应对象
迁移自: com.project.strategy.domain.response.BinanceOrderBookResp
"""
from dataclasses import dataclass
from typing import List, Optional
import json


@dataclass
class BinanceOrderBookResp:
    """Binance订单簿响应"""
    
    a: Optional[List[List[str]]] = None  # asks (卖单)
    b: Optional[List[List[str]]] = None  # bids (买单)
    s: Optional[str] = None  # symbol (交易对)
    T: Optional[int] = None  # transaction time (交易时间)
    e: Optional[str] = None  # event type (事件类型)
    E: Optional[int] = None  # event time (事件时间)
    U: Optional[int] = None  # first update id (首次更新ID)
    u: Optional[int] = None  # final update id (最终更新ID)
    pu: Optional[int] = None  # previous update id (上次更新ID)
    
    def get_a(self) -> Optional[List[List[str]]]:
        """获取卖单列表"""
        return self.a
    
    def get_b(self) -> Optional[List[List[str]]]:
        """获取买单列表"""
        return self.b
    
    def get_s(self) -> Optional[str]:
        """获取交易对"""
        return self.s
    
    def get_t(self) -> Optional[int]:
        """获取交易时间"""
        return self.T
    
    def get_e(self) -> Optional[str]:
        """获取事件类型"""
        return self.e
    
    def get_u(self) -> Optional[int]:
        """获取最终更新ID"""
        return self.u
    
    def get_pu(self) -> Optional[int]:
        """获取上次更新ID"""
        return self.pu
    
    def get_best_ask(self) -> Optional[List[str]]:
        """获取最佳卖价"""
        if self.a and len(self.a) > 0:
            return self.a[0]
        return None
    
    def get_best_bid(self) -> Optional[List[str]]:
        """获取最佳买价"""
        if self.b and len(self.b) > 0:
            return self.b[0]
        return None
    
    def get_best_ask_price(self) -> Optional[float]:
        """获取最佳卖价价格"""
        best_ask = self.get_best_ask()
        if best_ask and len(best_ask) > 0:
            try:
                return float(best_ask[0])
            except (ValueError, TypeError):
                return None
        return None
    
    def get_best_bid_price(self) -> Optional[float]:
        """获取最佳买价价格"""
        best_bid = self.get_best_bid()
        if best_bid and len(best_bid) > 0:
            try:
                return float(best_bid[0])
            except (ValueError, TypeError):
                return None
        return None
    
    def get_best_ask_quantity(self) -> Optional[float]:
        """获取最佳卖价数量"""
        best_ask = self.get_best_ask()
        if best_ask and len(best_ask) > 1:
            try:
                return float(best_ask[1])
            except (ValueError, TypeError):
                return None
        return None
    
    def get_best_bid_quantity(self) -> Optional[float]:
        """获取最佳买价数量"""
        best_bid = self.get_best_bid()
        if best_bid and len(best_bid) > 1:
            try:
                return float(best_bid[1])
            except (ValueError, TypeError):
                return None
        return None
    
    def get_spread(self) -> Optional[float]:
        """获取买卖价差"""
        ask_price = self.get_best_ask_price()
        bid_price = self.get_best_bid_price()
        
        if ask_price is not None and bid_price is not None:
            return ask_price - bid_price
        return None
    
    def is_valid(self) -> bool:
        """检查响应数据是否有效"""
        return (
            self.s is not None and
            self.a is not None and
            self.b is not None and
            len(self.a) > 0 and
            len(self.b) > 0
        )
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'a': self.a,
            'b': self.b,
            's': self.s,
            'T': self.T,
            'e': self.e,
            'E': self.E,
            'U': self.U,
            'u': self.u,
            'pu': self.pu
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'BinanceOrderBookResp':
        """从字典创建实例"""
        return cls(
            a=data.get('a'),
            b=data.get('b'),
            s=data.get('s'),
            T=data.get('T'),
            e=data.get('e'),
            E=data.get('E'),
            U=data.get('U'),
            u=data.get('u'),
            pu=data.get('pu')
        )
    
    @classmethod
    def from_json(cls, json_str: str) -> Optional['BinanceOrderBookResp']:
        """从JSON字符串创建实例"""
        try:
            data = json.loads(json_str)
            return cls.from_dict(data)
        except (json.JSONDecodeError, TypeError, ValueError):
            return None
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"BinanceOrderBookResp(s='{self.s}', asks={len(self.a) if self.a else 0}, bids={len(self.b) if self.b else 0})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"BinanceOrderBookResp(s='{self.s}', T={self.T}, "
                f"asks_count={len(self.a) if self.a else 0}, "
                f"bids_count={len(self.b) if self.b else 0})")

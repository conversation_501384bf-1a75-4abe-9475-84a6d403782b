/**
 * 若依UI通用方法
 */
(function($) {
    'use strict';

    // 全局变量
    window.$ = window.jQuery = $;

    // 通用方法封装
    var common = {
        // 判断字符串是否为空
        isEmpty: function(value) {
            return value == null || this.trim(value) == "";
        },
        // 判断字符串是否不为空
        isNotEmpty: function(value) {
            return !this.isEmpty(value);
        },
        // 空格截取
        trim: function(value) {
            if (value == null) return "";
            return value.toString().replace(/(^\s*)|(\s*$)|\r|\n/g, "");
        },
        // 比较两个字符串（大小写敏感）
        equals: function(str, that) {
            return str == that;
        },
        // 比较两个字符串（大小写不敏感）
        equalsIgnoreCase: function(str, that) {
            return String(str).toUpperCase() === String(that).toUpperCase();
        }
    };

    // 模态框封装
    var modal = {
        // 显示图标
        icon: function(type) {
            var icon = "";
            if (type == 'warning') {
                icon = 0;
            } else if (type == 'success') {
                icon = 1;
            } else if (type == 'error') {
                icon = 2;
            } else {
                icon = 3;
            }
            return icon;
        },
        // 消息提示
        msg: function(content, type) {
            if (typeof layer !== 'undefined') {
                layer.msg(content, {icon: modal.icon(type), time: 1000, shift: 5});
            } else {
                alert(content);
            }
        },
        // 错误消息
        msgError: function(content) {
            modal.msg(content, 'error');
        },
        // 成功消息
        msgSuccess: function(content) {
            modal.msg(content, 'success');
        },
        // 警告消息
        msgWarning: function(content) {
            modal.msg(content, 'warning');
        },
        // 弹出提示
        alert: function(content, type) {
            if (typeof layer !== 'undefined') {
                layer.alert(content, {
                    icon: modal.icon(type),
                    title: "系统提示",
                    btn: ['确认'],
                    btnclass: ['btn btn-primary']
                });
            } else {
                alert(content);
            }
        },
        // 消息确认
        confirm: function(content, callBack) {
            if (typeof layer !== 'undefined') {
                layer.confirm(content, {
                    icon: 3,
                    title: "系统提示",
                    btn: ['确认', '取消']
                }, function(index) {
                    layer.close(index);
                    callBack(true);
                }, function(index) {
                    layer.close(index);
                    callBack(false);
                });
            } else {
                if (confirm(content)) {
                    callBack(true);
                } else {
                    callBack(false);
                }
            }
        },
        // 弹出层
        open: function(title, url, width, height, callback) {
            if (typeof layer !== 'undefined') {
                //如果是移动端，就使用自适应大小弹窗
                if (navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)) {
                    width = 'auto';
                    height = 'auto';
                }
                if (common.isEmpty(title)) {
                    title = false;
                }
                if (common.isEmpty(url)) {
                    url = "/404.html";
                }
                if (common.isEmpty(width)) {
                    width = 800;
                }
                if (common.isEmpty(height)) {
                    height = ($(window).height() - 50);
                }
                var options = {
                    type: 2,
                    area: [width + 'px', height + 'px'],
                    fix: false,
                    maxmin: true,
                    shade: 0.3,
                    title: title,
                    content: url,
                    btn: ['确认', '关闭'],
                    yes: function(index, layero) {
                        var iframeWin = layero.find('iframe')[0];
                        if (iframeWin.contentWindow.submitHandler) {
                            iframeWin.contentWindow.submitHandler(index, layero);
                        } else {
                            layer.close(index);
                        }
                    },
                    cancel: function(index) {
                        return true;
                    }
                };
                if (typeof callback == 'function') {
                    options.success = callback;
                }
                return layer.open(options);
            } else {
                window.open(url, '_blank');
            }
        },
        // 弹出层指定宽度
        openOptions: function(options) {
            if (typeof layer !== 'undefined') {
                var index = layer.open(options);
                return index;
            }
        },
        // 关闭弹出层
        close: function() {
            if (typeof layer !== 'undefined') {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            }
        }
    };

    // 通用工具封装
    var table = {
        // 刷新表格
        refresh: function() {
            var currentId = $('.layui-laypage .layui-laypage-curr em').eq(1).text();
            if (typeof bootstrap_table_refresh === 'function') {
                bootstrap_table_refresh();
            }
        },
        // 查询表格
        search: function() {
            var currentId = $('.layui-laypage .layui-laypage-curr em').eq(1).text();
            if (typeof bootstrap_table_refresh === 'function') {
                bootstrap_table_refresh();
            }
        }
    };

    // 通用方法封装处理
    var form = {
        // 表单重置
        reset: function(formId) {
            if (common.isEmpty(formId)) {
                $('form').each(function() {
                    $(this)[0].reset();
                });
            } else {
                $("#" + formId)[0].reset();
            }
        }
    };

    // 将方法暴露到全局
    $.common = common;
    $.modal = modal;
    $.table = table;
    $.form = form;

})(jQuery);

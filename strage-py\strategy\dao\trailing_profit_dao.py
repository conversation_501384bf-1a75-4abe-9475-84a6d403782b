"""
跟踪盈利DAO类
迁移自: com.project.strategy.mapper.TrailingProfitMapper
"""
import logging
from typing import List, Optional
from decimal import Decimal
from datetime import datetime
from dao.base_dao import BaseDAO
from ..domain.entity.trailing_profit import TrailingProfit
from ..domain.entity.trailing_detail import TrailingDetail

logger = logging.getLogger(__name__)


class TrailingProfitDAO(BaseDAO):
    """跟踪盈利DAO类"""

    def __init__(self):
        """初始化跟踪盈利DAO"""
        super().__init__()
        logger.info("TrailingProfitDAO initialized")

    def _row_to_entity(self, row: dict) -> TrailingProfit:
        """
        将数据库行转换为实体对象

        Args:
            row: 数据库行字典

        Returns:
            跟踪盈利实体对象
        """
        return TrailingProfit(
            id=row.get('id'),
            group_id=row.get('group_id'),
            user_id=row.get('user_id'),
            user_name=row.get('user_name'),
            account_name=row.get('account_name'),
            platform=row.get('platform'),
            symbol=row.get('symbol'),
            open_price=row.get('open_price'),
            market_price=row.get('market_price'),
            close_price=row.get('close_price'),
            stop_loss_price=row.get('stop_loss_price'),
            stop_loss_rate=row.get('stop_loss_rate'),
            highest_price=row.get('highest_price'),
            lowest_price=row.get('lowest_price'),
            amount=row.get('amount'),
            profit_value=row.get('profit_value'),
            profit_rate=row.get('profit_rate'),
            inst_type=row.get('inst_type'),
            leverage=row.get('leverage'),
            position_side=row.get('position_side'),
            order_type=row.get('order_type'),
            trade_mode=row.get('trade_mode'),
            fee=row.get('fee'),
            ref_order_open=row.get('ref_order_open'),
            ref_order_close=row.get('ref_order_close'),
            state=row.get('state'),
            strategy_type=row.get('strategy_type'),
            follow_content=row.get('follow_content'),
            fresh=row.get('fresh'),
            create_time=row.get('create_time'),
            update_time=row.get('update_time'),
            message=row.get('message')
        )

    def _detail_row_to_entity(self, row: dict) -> TrailingDetail:
        """
        将数据库行转换为跟踪详情实体对象

        Args:
            row: 数据库行字典

        Returns:
            跟踪详情实体对象
        """
        return TrailingDetail(
            id=row.get('id'),
            trailing_profit_id=row.get('trailing_profit_id'),
            price_gain=row.get('price_gain'),
            trigger_price=row.get('trigger_price'),
            take_profit=row.get('take_profit'),
            state=row.get('state'),
            type=row.get('type'),
            create_time=row.get('create_time'),
            update_time=row.get('update_time'),
            message=row.get('message')
        )

    def select_trailing_profit_by_id(self, id: int) -> Optional[TrailingProfit]:
        """
        根据ID查询跟踪盈利（包含详情列表）
        对应: selectTrailingProfitById

        Args:
            id: 跟踪盈利ID

        Returns:
            跟踪盈利对象，不存在返回None
        """
        try:
            sql = """
                SELECT id, group_id, user_id, user_name, account_name, platform, symbol,
                       open_price, market_price, close_price, stop_loss_price, stop_loss_rate,
                       highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
                       leverage, position_side, order_type, trade_mode, fee, ref_order_open,
                       ref_order_close, state, strategy_type, follow_content, fresh,
                       create_time, update_time, message
                FROM trailing_profit
                WHERE id = %s
            """
            results = self.execute_query(sql, (id,))

            if results:
                row = results[0]
                trailing = self._row_to_entity(row)
                # 加载详情列表
                trailing.trailing_detail_list = self.select_trailing_detail_list(trailing.id)
                return trailing
            return None
        except Exception as e:
            logger.error(f"根据ID查询跟踪盈利失败: {e}")
            return None

    def select_trailing_follow_by_group(self, group_id: int) -> Optional[TrailingProfit]:
        """
        根据组ID查询跟踪跟单（包含详情列表）
        对应: selectTrailingFollowByGroup

        Args:
            group_id: 组ID

        Returns:
            跟踪盈利对象，不存在返回None
        """
        try:
            sql = """
                SELECT id, group_id, user_id, user_name, account_name, platform, symbol,
                       open_price, market_price, close_price, stop_loss_price, stop_loss_rate,
                       highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
                       leverage, position_side, order_type, trade_mode, fee, ref_order_open,
                       ref_order_close, state, strategy_type, follow_content, fresh,
                       create_time, update_time, message
                FROM trailing_profit
                WHERE group_id = %s AND strategy_type = 1 AND state = 0
                LIMIT 1
            """
            results = self.execute_query(sql, (group_id,))

            if results:
                row = results[0]
                trailing = self._row_to_entity(row)
                # 加载详情列表
                trailing.trailing_detail_list = self.select_trailing_detail_list(trailing.id)
                return trailing
            return None
        except Exception as e:
            logger.error(f"根据组ID查询跟踪跟单失败: {e}")
            return None

    def select_trailing_profit_list(self, trailing_profit: Optional[TrailingProfit] = None) -> List[TrailingProfit]:
        """
        查询跟踪盈利列表
        对应: selectTrailingProfitList

        Args:
            trailing_profit: 查询条件对象

        Returns:
            跟踪盈利列表
        """
        try:
            # 基础SQL
            sql = """
                SELECT id, group_id, user_id, user_name, account_name, platform, symbol,
                       open_price, market_price, close_price, stop_loss_price, stop_loss_rate,
                       highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
                       leverage, position_side, order_type, trade_mode, fee, ref_order_open,
                       ref_order_close, state, strategy_type, follow_content, fresh,
                       create_time, update_time, message
                FROM trailing_profit
            """

            conditions = []
            params = []

            # 动态构建WHERE条件
            if trailing_profit:
                if trailing_profit.group_id is not None:
                    conditions.append("group_id = %s")
                    params.append(trailing_profit.group_id)
                if trailing_profit.user_id is not None:
                    conditions.append("user_id = %s")
                    params.append(trailing_profit.user_id)
                if trailing_profit.user_name:
                    conditions.append("user_name LIKE %s")
                    params.append(f"%{trailing_profit.user_name}%")
                if trailing_profit.account_name:
                    conditions.append("account_name LIKE %s")
                    params.append(f"%{trailing_profit.account_name}%")
                if trailing_profit.platform:
                    conditions.append("platform = %s")
                    params.append(trailing_profit.platform)
                if trailing_profit.symbol:
                    conditions.append("symbol = %s")
                    params.append(trailing_profit.symbol)
                if trailing_profit.state is not None:
                    conditions.append("state = %s")
                    params.append(trailing_profit.state)
                if trailing_profit.strategy_type is not None:
                    conditions.append("strategy_type = %s")
                    params.append(trailing_profit.strategy_type)
                if trailing_profit.fresh is not None:
                    conditions.append("fresh = %s")
                    params.append(trailing_profit.fresh)

            if conditions:
                sql += " WHERE " + " AND ".join(conditions)

            sql += " ORDER BY id DESC"

            results = self.execute_query(sql, params)

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))
            return entities
        except Exception as e:
            logger.error(f"查询跟踪盈利列表失败: {e}")
            return []

    def select_trailing_detail_list(self, trailing_profit_id: int) -> List[TrailingDetail]:
        """
        根据跟踪盈利ID查询详情列表
        对应: selectTrailingDetailList

        Args:
            trailing_profit_id: 跟踪盈利ID

        Returns:
            跟踪详情列表
        """
        try:
            sql = """
                SELECT id, trailing_profit_id, price_gain, trigger_price, take_profit,
                       state, type, create_time, update_time, message
                FROM trailing_detail
                WHERE trailing_profit_id = %s
            """
            results = self.execute_query(sql, (trailing_profit_id,))

            entities = []
            for row in results:
                entities.append(self._detail_row_to_entity(row))
            return entities
        except Exception as e:
            logger.error(f"根据跟踪盈利ID查询详情列表失败: {e}")
            return []

    def select_by_ids(self, ids: List[str]) -> List[TrailingProfit]:
        """
        根据ID列表查询跟踪盈利列表
        对应: selectByIds

        Args:
            ids: ID列表

        Returns:
            跟踪盈利列表
        """
        try:
            if not ids:
                return []

            placeholders = ','.join(['%s'] * len(ids))
            sql = f"""
                SELECT id, group_id, user_id, user_name, account_name, platform, symbol,
                       open_price, market_price, close_price, stop_loss_price, stop_loss_rate,
                       highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
                       leverage, position_side, order_type, trade_mode, fee, ref_order_open,
                       ref_order_close, state, strategy_type, follow_content, fresh,
                       create_time, update_time, message
                FROM trailing_profit
                WHERE id IN ({placeholders})
            """
            results = self.execute_query(sql, ids)

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))
            return entities
        except Exception as e:
            logger.error(f"根据ID列表查询跟踪盈利列表失败: {e}")
            return []

    def select_by_refresh(self, state_list: List[int]) -> List[TrailingProfit]:
        """
        根据状态列表查询需要刷新的跟踪盈利列表
        对应: selectByRefresh

        Args:
            state_list: 状态列表

        Returns:
            跟踪盈利列表
        """
        try:
            if not state_list:
                return []

            placeholders = ','.join(['%s'] * len(state_list))
            sql = f"""
                SELECT id, group_id, user_id, user_name, account_name, platform, symbol,
                       open_price, market_price, close_price, stop_loss_price, stop_loss_rate,
                       highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
                       leverage, position_side, order_type, trade_mode, fee, ref_order_open,
                       ref_order_close, state, strategy_type, follow_content, fresh,
                       create_time, update_time, message
                FROM trailing_profit
                WHERE fresh = 0 AND state IN ({placeholders})
            """
            results = self.execute_query(sql, state_list)

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))
            return entities
        except Exception as e:
            logger.error(f"根据状态列表查询需要刷新的跟踪盈利列表失败: {e}")
            return []

    def select_by_group(self, user_id: int, group_id: int) -> List[TrailingProfit]:
        """
        根据用户ID和组ID查询跟踪盈利列表
        对应: selectByGroup

        Args:
            user_id: 用户ID
            group_id: 组ID

        Returns:
            跟踪盈利列表
        """
        try:
            sql = """
                SELECT id, group_id, user_id, user_name, account_name, platform, symbol,
                       open_price, market_price, close_price, stop_loss_price, stop_loss_rate,
                       highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
                       leverage, position_side, order_type, trade_mode, fee, ref_order_open,
                       ref_order_close, state, strategy_type, follow_content, fresh,
                       create_time, update_time, message
                FROM trailing_profit
                WHERE user_id = %s AND group_id = %s
            """
            results = self.execute_query(sql, (user_id, group_id))

            entities = []
            for row in results:
                entities.append(self._row_to_entity(row))
            return entities
        except Exception as e:
            logger.error(f"根据用户ID和组ID查询跟踪盈利列表失败: {e}")
            return []

    def select_lastest_group_id(self, user_id: int) -> Optional[int]:
        """
        查询用户最新的组ID
        对应: selectLastestGroupId

        Args:
            user_id: 用户ID

        Returns:
            最新的组ID，不存在返回None
        """
        try:
            sql = """
                SELECT group_id FROM trailing_profit
                WHERE user_id = %s
                ORDER BY group_id DESC
                LIMIT 1
            """
            results = self.execute_query(sql, (user_id,))

            if results:
                return results[0].get('group_id')
            return None
        except Exception as e:
            logger.error(f"查询用户最新的组ID失败: {e}")
            return None

    def insert_trailing_profit(self, trailing_profit: TrailingProfit) -> bool:
        """
        插入跟踪盈利
        对应: insertTrailingProfit

        Args:
            trailing_profit: 跟踪盈利对象

        Returns:
            是否插入成功
        """
        try:
            # 构建动态插入SQL
            fields = []
            values = []
            params = []

            if trailing_profit.group_id is not None:
                fields.append("group_id")
                values.append("%s")
                params.append(trailing_profit.group_id)
            if trailing_profit.user_id is not None:
                fields.append("user_id")
                values.append("%s")
                params.append(trailing_profit.user_id)
            if trailing_profit.user_name:
                fields.append("user_name")
                values.append("%s")
                params.append(trailing_profit.user_name)
            if trailing_profit.account_name:
                fields.append("account_name")
                values.append("%s")
                params.append(trailing_profit.account_name)
            if trailing_profit.platform:
                fields.append("platform")
                values.append("%s")
                params.append(trailing_profit.platform)
            if trailing_profit.symbol:
                fields.append("symbol")
                values.append("%s")
                params.append(trailing_profit.symbol)
            if trailing_profit.open_price is not None:
                fields.append("open_price")
                values.append("%s")
                params.append(trailing_profit.open_price)
            if trailing_profit.market_price is not None:
                fields.append("market_price")
                values.append("%s")
                params.append(trailing_profit.market_price)
            if trailing_profit.close_price is not None:
                fields.append("close_price")
                values.append("%s")
                params.append(trailing_profit.close_price)
            if trailing_profit.stop_loss_price is not None:
                fields.append("stop_loss_price")
                values.append("%s")
                params.append(trailing_profit.stop_loss_price)
            if trailing_profit.stop_loss_rate is not None:
                fields.append("stop_loss_rate")
                values.append("%s")
                params.append(trailing_profit.stop_loss_rate)
            if trailing_profit.highest_price is not None:
                fields.append("highest_price")
                values.append("%s")
                params.append(trailing_profit.highest_price)
            if trailing_profit.lowest_price is not None:
                fields.append("lowest_price")
                values.append("%s")
                params.append(trailing_profit.lowest_price)
            if trailing_profit.amount is not None:
                fields.append("amount")
                values.append("%s")
                params.append(trailing_profit.amount)
            if trailing_profit.profit_value is not None:
                fields.append("profit_value")
                values.append("%s")
                params.append(trailing_profit.profit_value)
            if trailing_profit.profit_rate is not None:
                fields.append("profit_rate")
                values.append("%s")
                params.append(trailing_profit.profit_rate)
            if trailing_profit.inst_type:
                fields.append("inst_type")
                values.append("%s")
                params.append(trailing_profit.inst_type)
            if trailing_profit.leverage is not None:
                fields.append("leverage")
                values.append("%s")
                params.append(trailing_profit.leverage)
            if trailing_profit.position_side:
                fields.append("position_side")
                values.append("%s")
                params.append(trailing_profit.position_side)
            if trailing_profit.order_type:
                fields.append("order_type")
                values.append("%s")
                params.append(trailing_profit.order_type)
            if trailing_profit.trade_mode:
                fields.append("trade_mode")
                values.append("%s")
                params.append(trailing_profit.trade_mode)
            if trailing_profit.fee is not None:
                fields.append("fee")
                values.append("%s")
                params.append(trailing_profit.fee)
            if trailing_profit.ref_order_open:
                fields.append("ref_order_open")
                values.append("%s")
                params.append(trailing_profit.ref_order_open)
            if trailing_profit.ref_order_close:
                fields.append("ref_order_close")
                values.append("%s")
                params.append(trailing_profit.ref_order_close)
            if trailing_profit.state is not None:
                fields.append("state")
                values.append("%s")
                params.append(trailing_profit.state)
            if trailing_profit.strategy_type is not None:
                fields.append("strategy_type")
                values.append("%s")
                params.append(trailing_profit.strategy_type)
            if trailing_profit.follow_content:
                fields.append("follow_content")
                values.append("%s")
                params.append(trailing_profit.follow_content)
            if trailing_profit.fresh is not None:
                fields.append("fresh")
                values.append("%s")
                params.append(trailing_profit.fresh)
            if trailing_profit.create_time is not None:
                fields.append("create_time")
                values.append("%s")
                params.append(trailing_profit.create_time)
            if trailing_profit.update_time is not None:
                fields.append("update_time")
                values.append("%s")
                params.append(trailing_profit.update_time)
            if trailing_profit.message:
                fields.append("message")
                values.append("%s")
                params.append(trailing_profit.message)

            if not fields:
                return False

            sql = f"""
                INSERT INTO trailing_profit ({', '.join(fields)})
                VALUES ({', '.join(values)})
            """

            affected_rows = self.execute_update(sql, params)

            # 获取插入的ID
            if affected_rows > 0:
                trailing_profit.id = self.get_last_insert_id()
                return True
            return False
        except Exception as e:
            logger.error(f"插入跟踪盈利失败: {e}")
            return False

    def update_trailing_profit(self, trailing_profit: TrailingProfit) -> bool:
        """
        更新跟踪盈利
        对应: updateTrailingProfit

        Args:
            trailing_profit: 跟踪盈利对象

        Returns:
            是否更新成功
        """
        try:
            # 构建动态更新SQL
            set_clauses = []
            params = []

            if trailing_profit.group_id is not None:
                set_clauses.append("group_id = %s")
                params.append(trailing_profit.group_id)
            if trailing_profit.user_id is not None:
                set_clauses.append("user_id = %s")
                params.append(trailing_profit.user_id)
            if trailing_profit.user_name is not None:
                set_clauses.append("user_name = %s")
                params.append(trailing_profit.user_name)
            if trailing_profit.account_name is not None:
                set_clauses.append("account_name = %s")
                params.append(trailing_profit.account_name)
            if trailing_profit.platform is not None:
                set_clauses.append("platform = %s")
                params.append(trailing_profit.platform)
            if trailing_profit.symbol is not None:
                set_clauses.append("symbol = %s")
                params.append(trailing_profit.symbol)
            if trailing_profit.open_price is not None:
                set_clauses.append("open_price = %s")
                params.append(trailing_profit.open_price)
            if trailing_profit.market_price is not None:
                set_clauses.append("market_price = %s")
                params.append(trailing_profit.market_price)
            if trailing_profit.close_price is not None:
                set_clauses.append("close_price = %s")
                params.append(trailing_profit.close_price)
            if trailing_profit.stop_loss_price is not None:
                set_clauses.append("stop_loss_price = %s")
                params.append(trailing_profit.stop_loss_price)
            if trailing_profit.stop_loss_rate is not None:
                set_clauses.append("stop_loss_rate = %s")
                params.append(trailing_profit.stop_loss_rate)
            if trailing_profit.highest_price is not None:
                set_clauses.append("highest_price = %s")
                params.append(trailing_profit.highest_price)
            if trailing_profit.lowest_price is not None:
                set_clauses.append("lowest_price = %s")
                params.append(trailing_profit.lowest_price)
            if trailing_profit.amount is not None:
                set_clauses.append("amount = %s")
                params.append(trailing_profit.amount)
            if trailing_profit.profit_value is not None:
                set_clauses.append("profit_value = %s")
                params.append(trailing_profit.profit_value)
            if trailing_profit.profit_rate is not None:
                set_clauses.append("profit_rate = %s")
                params.append(trailing_profit.profit_rate)
            if trailing_profit.inst_type is not None:
                set_clauses.append("inst_type = %s")
                params.append(trailing_profit.inst_type)
            if trailing_profit.leverage is not None:
                set_clauses.append("leverage = %s")
                params.append(trailing_profit.leverage)
            if trailing_profit.position_side is not None:
                set_clauses.append("position_side = %s")
                params.append(trailing_profit.position_side)
            if trailing_profit.order_type is not None:
                set_clauses.append("order_type = %s")
                params.append(trailing_profit.order_type)
            if trailing_profit.trade_mode is not None:
                set_clauses.append("trade_mode = %s")
                params.append(trailing_profit.trade_mode)
            if trailing_profit.fee is not None:
                set_clauses.append("fee = %s")
                params.append(trailing_profit.fee)
            if trailing_profit.ref_order_open is not None:
                set_clauses.append("ref_order_open = %s")
                params.append(trailing_profit.ref_order_open)
            if trailing_profit.ref_order_close is not None:
                set_clauses.append("ref_order_close = %s")
                params.append(trailing_profit.ref_order_close)
            if trailing_profit.state is not None:
                set_clauses.append("state = %s")
                params.append(trailing_profit.state)
            if trailing_profit.strategy_type is not None:
                set_clauses.append("strategy_type = %s")
                params.append(trailing_profit.strategy_type)
            if trailing_profit.follow_content is not None:
                set_clauses.append("follow_content = %s")
                params.append(trailing_profit.follow_content)
            if trailing_profit.fresh is not None:
                set_clauses.append("fresh = %s")
                params.append(trailing_profit.fresh)
            if trailing_profit.create_time is not None:
                set_clauses.append("create_time = %s")
                params.append(trailing_profit.create_time)
            if trailing_profit.update_time is not None:
                set_clauses.append("update_time = %s")
                params.append(trailing_profit.update_time)
            if trailing_profit.message is not None:
                set_clauses.append("message = %s")
                params.append(trailing_profit.message)

            if not set_clauses:
                return False

            params.append(trailing_profit.id)
            sql = f"""
                UPDATE trailing_profit
                SET {', '.join(set_clauses)}
                WHERE id = %s
            """

            affected_rows = self.execute_update(sql, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"更新跟踪盈利失败: {e}")
            return False

    def delete_trailing_profit_by_id(self, id: int) -> bool:
        """
        根据ID删除跟踪盈利
        对应: deleteTrailingProfitById

        Args:
            id: 跟踪盈利ID

        Returns:
            是否删除成功
        """
        try:
            sql = "DELETE FROM trailing_profit WHERE id = %s"
            affected_rows = self.execute_update(sql, (id,))
            return affected_rows > 0
        except Exception as e:
            logger.error(f"根据ID删除跟踪盈利失败: {e}")
            return False

    def delete_trailing_profit_by_ids(self, ids: List[str]) -> int:
        """
        根据ID列表删除跟踪盈利
        对应: deleteTrailingProfitByIds

        Args:
            ids: ID列表

        Returns:
            影响的行数
        """
        try:
            if not ids:
                return 0

            placeholders = ','.join(['%s'] * len(ids))
            sql = f"DELETE FROM trailing_profit WHERE id IN ({placeholders})"
            affected_rows = self.execute_update(sql, ids)
            return affected_rows
        except Exception as e:
            logger.error(f"根据ID列表删除跟踪盈利失败: {e}")
            return 0

    def add_trailing_group(self, group_id: int, user_id: int, ids: List[str]) -> int:
        """
        添加跟踪组
        对应: addTrailingGroup

        Args:
            group_id: 组ID
            user_id: 用户ID
            ids: ID列表

        Returns:
            影响的行数
        """
        try:
            if not ids:
                return 0

            placeholders = ','.join(['%s'] * len(ids))
            sql = f"""
                UPDATE trailing_profit
                SET group_id = %s
                WHERE user_id = %s AND state = -1 AND id IN ({placeholders})
            """
            params = [group_id, user_id] + ids
            affected_rows = self.execute_update(sql, params)
            return affected_rows
        except Exception as e:
            logger.error(f"添加跟踪组失败: {e}")
            return 0

    def start_group(self, user_id: int, group_id: int) -> bool:
        """
        启动组
        对应: startGroup

        Args:
            user_id: 用户ID
            group_id: 组ID

        Returns:
            是否更新成功
        """
        try:
            sql = """
                UPDATE trailing_profit
                SET state = 0
                WHERE user_id = %s AND group_id = %s AND state = -1
            """
            params = (user_id, group_id)

            affected_rows = self.execute_update(sql, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"启动组失败: {e}")
            return False

    def update_trailing_detail(self, detail: TrailingDetail) -> bool:
        """
        更新跟踪详情
        对应: updateTrailingDetail

        Args:
            detail: 跟踪详情对象

        Returns:
            是否更新成功
        """
        try:
            set_clauses = []
            params = []

            if detail.price_gain is not None:
                set_clauses.append("price_gain = %s")
                params.append(detail.price_gain)
            if detail.trigger_price is not None:
                set_clauses.append("trigger_price = %s")
                params.append(detail.trigger_price)
            if detail.take_profit is not None:
                set_clauses.append("take_profit = %s")
                params.append(detail.take_profit)
            if detail.state is not None:
                set_clauses.append("state = %s")
                params.append(detail.state)
            if detail.type is not None:
                set_clauses.append("type = %s")
                params.append(detail.type)
            if detail.update_time is not None:
                set_clauses.append("update_time = %s")
                params.append(detail.update_time)
            else:
                set_clauses.append("update_time = NOW()")

            if not set_clauses:
                return False

            params.append(detail.id)
            sql = f"""
                UPDATE trailing_detail
                SET {', '.join(set_clauses)}
                WHERE id = %s
            """

            affected_rows = self.execute_update(sql, params)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"更新跟踪详情失败: {e}")
            return False

    def delete_trailing_detail_by_trailing_profit_ids(self, ids: List[str]) -> int:
        """
        根据跟踪盈利ID列表删除跟踪详情
        对应: deleteTrailingDetailByTrailingProfitIds

        Args:
            ids: 跟踪盈利ID列表

        Returns:
            影响的行数
        """
        try:
            if not ids:
                return 0

            placeholders = ','.join(['%s'] * len(ids))
            sql = f"""
                DELETE FROM trailing_detail
                WHERE trailing_profit_id IN ({placeholders})
            """
            affected_rows = self.execute_update(sql, ids)
            return affected_rows
        except Exception as e:
            logger.error(f"根据跟踪盈利ID列表删除跟踪详情失败: {e}")
            return 0

    def batch_trailing_detail(self, trailing_detail_list: List[TrailingDetail]) -> bool:
        """
        批量插入跟踪详情
        对应: batchTrailingDetail

        Args:
            trailing_detail_list: 跟踪详情列表

        Returns:
            是否插入成功
        """
        try:
            if not trailing_detail_list:
                return True

            sql = """
                INSERT INTO trailing_detail (id, trailing_profit_id, price_gain, trigger_price,
                                           take_profit, state, type, create_time, update_time, message)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            params_list = []
            for detail in trailing_detail_list:
                params_list.append((
                    detail.id, detail.trailing_profit_id, detail.price_gain, detail.trigger_price,
                    detail.take_profit, detail.state, detail.type, detail.create_time,
                    detail.update_time, detail.message
                ))

            affected_rows = self.execute_batch_update(sql, params_list)
            return affected_rows > 0
        except Exception as e:
            logger.error(f"批量插入跟踪详情失败: {e}")
            return False

    def delete_trailing_detail_by_trailing_profit_id(self, trailing_profit_id: int) -> bool:
        """
        根据跟踪盈利ID删除跟踪详情
        对应: deleteTrailingDetailByTrailingProfitId

        Args:
            trailing_profit_id: 跟踪盈利ID

        Returns:
            是否删除成功
        """
        try:
            sql = "DELETE FROM trailing_detail WHERE trailing_profit_id = %s"
            affected_rows = self.execute_update(sql, (trailing_profit_id,))
            return affected_rows > 0
        except Exception as e:
            logger.error(f"根据跟踪盈利ID删除跟踪详情失败: {e}")
            return False
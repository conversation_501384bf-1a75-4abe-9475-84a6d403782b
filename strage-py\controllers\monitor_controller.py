"""
监控管理控制器
处理日志监控相关的HTTP请求
"""
import logging
import csv
import io
from datetime import datetime
from flask import Blueprint, render_template, request, jsonify, make_response
from services.auth_service import login_required
from services.log_service import OperLogService, LoginLogService
from utils.log_decorator import log_operation, log_delete, BusinessType

logger = logging.getLogger(__name__)

monitor_bp = Blueprint('monitor', __name__, url_prefix='/monitor')

@monitor_bp.route('/operlog')
@login_required
def oper_log_list():
    """操作日志页面"""
    return render_template('monitor/operlog.html')

@monitor_bp.route('/operlog/list', methods=['POST'])
@login_required
def oper_log_list_api():
    """操作日志列表API"""
    try:
        # 获取分页参数 (Bootstrap Table使用offset和limit)
        offset = int(request.form.get('offset', 0))
        limit = int(request.form.get('limit', 10))
        page = (offset // limit) + 1
        size = limit

        # 获取搜索条件
        search_params = {
            'title': request.form.get('title', '').strip() or None,
            'oper_name': request.form.get('oper_name', '').strip() or None,
            'oper_ip': request.form.get('oper_ip', '').strip() or None,
            'status': request.form.get('status', '').strip() or None,
            'business_types': request.form.get('business_types', '').strip() or None,
            'begin_time': request.form.get('params[begin_time]', '').strip() or None,
            'end_time': request.form.get('params[end_time]', '').strip() or None
        }

        # 处理业务类型
        if search_params['business_types']:
            try:
                business_types = [int(x.strip()) for x in search_params['business_types'].split(',') if x.strip().isdigit()]
                search_params['business_types'] = business_types if business_types else None
            except:
                search_params['business_types'] = None

        # 处理状态
        if search_params['status']:
            try:
                search_params['status'] = int(search_params['status'])
            except:
                search_params['status'] = None

        # 查询数据
        result = OperLogService.select_oper_log_list(
            page=page,
            size=size,
            **{k: v for k, v in search_params.items() if v is not None}
        )

        # 转换为字典格式
        rows = [log.to_dict() for log in result['rows']]

        return jsonify({
            'code': 0,
            'msg': '查询成功',
            'total': result['total'],
            'rows': rows
        })
    except Exception as e:
        logger.error(f"查询操作日志失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'查询失败: {str(e)}',
            'total': 0,
            'rows': []
        })

@monitor_bp.route('/operlog/export', methods=['POST'])
@login_required
@log_operation("操作日志", BusinessType.EXPORT)
def oper_log_export():
    """导出操作日志"""
    try:
        # 获取搜索条件
        search_params = {
            'title': request.form.get('title', '').strip() or None,
            'oper_name': request.form.get('oper_name', '').strip() or None,
            'oper_ip': request.form.get('oper_ip', '').strip() or None,
            'status': request.form.get('status', '').strip() or None,
            'business_types': request.form.get('business_types', '').strip() or None,
            'begin_time': request.form.get('params[begin_time]', '').strip() or None,
            'end_time': request.form.get('params[end_time]', '').strip() or None
        }

        # 处理业务类型
        if search_params['business_types']:
            try:
                business_types = [int(x.strip()) for x in search_params['business_types'].split(',') if x.strip().isdigit()]
                search_params['business_types'] = business_types if business_types else None
            except:
                search_params['business_types'] = None

        # 处理状态
        if search_params['status']:
            try:
                search_params['status'] = int(search_params['status'])
            except:
                search_params['status'] = None

        # 查询数据
        logs = OperLogService.export_oper_log(
            **{k: v for k, v in search_params.items() if v is not None}
        )

        # 创建CSV文件
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        writer.writerow(['日志编号', '系统模块', '操作类型', '操作人员', '操作地址',
                        '操作状态', '操作时间', '消耗时间'])

        # 操作类型映射
        business_type_map = {
            0: '其它', 1: '新增', 2: '修改', 3: '删除', 4: '授权',
            5: '导出', 6: '导入', 7: '强退', 8: '生成代码', 9: '清空数据'
        }

        # 写入数据
        for log in logs:
            business_type_name = business_type_map.get(log.business_type, '其它')
            status_name = '成功' if log.status == 0 else '失败'
            cost_time = f"{log.cost_time}毫秒" if log.cost_time else ""
            
            writer.writerow([
                log.oper_id,
                log.title,
                business_type_name,
                log.oper_name,
                log.oper_ip,
                status_name,
                log.oper_time.strftime('%Y-%m-%d %H:%M:%S') if log.oper_time else '',
                cost_time
            ])

        # 创建响应
        output.seek(0)
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8-sig'
        response.headers['Content-Disposition'] = f'attachment; filename=operlog_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        return response

    except Exception as e:
        logger.error(f"导出操作日志失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '导出失败'
        })

@monitor_bp.route('/operlog/detail/<int:oper_id>')
@login_required
def oper_log_detail(oper_id):
    """操作日志详情"""
    try:
        from services.log_service import OperLogService

        oper_log = OperLogService.select_oper_log_by_id(oper_id)
        if not oper_log:
            return render_template('error/404.html'), 404

        # 业务类型映射
        business_type_map = {
            0: '其它', 1: '新增', 2: '修改', 3: '删除', 4: '授权',
            5: '导出', 6: '导入', 7: '强退', 8: '生成代码', 9: '清空数据'
        }

        return render_template('monitor/operlog_detail.html',
                             oper_log=oper_log,
                             business_type_map=business_type_map)

    except Exception as e:
        logger.error(f"查看操作日志详情失败: {e}")
        return render_template('error/500.html'), 500

@monitor_bp.route('/logininfor')
@login_required
def login_log_list():
    """登录日志页面"""
    return render_template('monitor/logininfor.html')

@monitor_bp.route('/logininfor/list', methods=['POST'])
@login_required
def login_log_list_api():
    """登录日志列表API"""
    try:
        # 获取分页参数
        page = int(request.form.get('pageNum', 1))
        size = int(request.form.get('pageSize', 10))
        
        # 获取搜索条件
        search_params = {
            'login_name': request.form.get('loginName', '').strip() or None,
            'status': request.form.get('status', '').strip() or None,
            'begin_time': request.form.get('params[begin_time]', '').strip() or None,
            'end_time': request.form.get('params[end_time]', '').strip() or None
        }

        # 处理状态
        if search_params['status']:
            try:
                search_params['status'] = search_params['status']
            except:
                search_params['status'] = None

        # 查询数据
        result = LoginLogService.select_login_log_list(
            page=page,
            size=size,
            **{k: v for k, v in search_params.items() if v is not None}
        )

        # 转换为字典格式
        rows = [log.to_dict() for log in result['rows']]

        return jsonify({
            'code': 0,
            'msg': '查询成功',
            'rows': rows,
            'total': result['total']
        })
    except Exception as e:
        logger.error(f"查询登录日志失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '查询失败'
        })

@monitor_bp.route('/operlog/remove', methods=['POST'])
@login_required
@log_operation("操作日志", BusinessType.DELETE)
def oper_log_remove():
    """删除操作日志"""
    try:
        ids = request.form.get('ids', '').strip()
        if not ids:
            return jsonify({
                'code': 1,
                'msg': '请选择要删除的日志'
            })
        
        oper_ids = [int(id.strip()) for id in ids.split(',') if id.strip().isdigit()]
        if not oper_ids:
            return jsonify({
                'code': 1,
                'msg': '无效的日志ID'
            })
        
        # 删除日志
        deleted_count = OperLogService.delete_oper_log_by_ids(oper_ids)
        
        if deleted_count > 0:
            return jsonify({
                'code': 0,
                'msg': f'成功删除{deleted_count}条日志'
            })
        else:
            return jsonify({
                'code': 1,
                'msg': '删除失败'
            })
    
    except Exception as e:
        logger.error(f"删除操作日志失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '删除失败'
        })

@monitor_bp.route('/operlog/clean', methods=['POST'])
@login_required
@log_operation("操作日志", BusinessType.CLEAN)
def oper_log_clean():
    """清空操作日志"""
    try:
        deleted_count = OperLogService.clean_oper_log()
        
        return jsonify({
            'code': 0,
            'msg': f'成功清空{deleted_count}条日志'
        })
    
    except Exception as e:
        logger.error(f"清空操作日志失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '清空失败'
        })

@monitor_bp.route('/logininfor/remove', methods=['POST'])
@login_required
@log_operation("登录日志", BusinessType.DELETE)
def login_log_remove():
    """删除登录日志"""
    try:
        ids = request.form.get('ids', '').strip()
        if not ids:
            return jsonify({
                'code': 1,
                'msg': '请选择要删除的日志'
            })
        
        info_ids = [int(id.strip()) for id in ids.split(',') if id.strip().isdigit()]
        if not info_ids:
            return jsonify({
                'code': 1,
                'msg': '无效的日志ID'
            })
        
        # 删除日志
        deleted_count = LoginLogService.delete_login_log_by_ids(info_ids)
        
        if deleted_count > 0:
            return jsonify({
                'code': 0,
                'msg': f'成功删除{deleted_count}条日志'
            })
        else:
            return jsonify({
                'code': 1,
                'msg': '删除失败'
            })
    
    except Exception as e:
        logger.error(f"删除登录日志失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '删除失败'
        })

@monitor_bp.route('/logininfor/clean', methods=['POST'])
@login_required
@log_operation("登录日志", BusinessType.CLEAN)
def login_log_clean():
    """清空登录日志"""
    try:
        deleted_count = LoginLogService.clean_login_log()
        
        return jsonify({
            'code': 0,
            'msg': f'成功清空{deleted_count}条日志'
        })
    
    except Exception as e:
        logger.error(f"清空登录日志失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '清空失败'
        })

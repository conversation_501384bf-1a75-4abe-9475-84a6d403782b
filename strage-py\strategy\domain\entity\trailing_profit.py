"""
跟踪盈利实体类
迁移自: com.project.strategy.domain.entity.TrailingProfit
"""
from dataclasses import dataclass, field
from typing import Optional, List
from decimal import Decimal
from datetime import datetime
import threading


@dataclass
class TrailingProfit:
    """跟踪盈利实体"""
    
    # 基础信息
    id: Optional[int] = None
    group_id: Optional[int] = None
    user_id: Optional[int] = None  # 用户id
    user_name: Optional[str] = None  # 用户名称
    account_name: Optional[str] = None
    platform: Optional[str] = None  # 平台
    symbol: Optional[str] = None  # 交易所对应币对名
    
    # 价格信息
    open_price: Optional[Decimal] = None
    market_price: Optional[Decimal] = None
    close_price: Optional[Decimal] = None
    stop_loss_price: Optional[Decimal] = None
    stop_loss_rate: Optional[float] = None
    highest_price: Optional[Decimal] = None
    lowest_price: Optional[Decimal] = None
    
    # 交易信息
    amount: Optional[Decimal] = None  # 交易数量
    profit_value: Optional[Decimal] = None
    profit_rate: Optional[float] = None
    inst_type: Optional[str] = None  # 金融类型：futures, spot
    leverage: Optional[int] = None  # 杠杆位数
    position_side: Optional[str] = None  # 订单方向： long, short
    order_type: Optional[str] = None  # 订单类型： MARKET, LIMIT
    trade_mode: Optional[str] = None  # 交易模式： 全仓-CROSSED, 逐仓-ISOLATED
    fee: Optional[Decimal] = None  # fee
    
    # 订单信息
    ref_order_open: Optional[str] = None  # 来源订单id
    ref_order_close: Optional[str] = None
    state: Optional[int] = None  # 0: 不启用， 1：进行中，2：完成
    
    # 策略信息
    strategy_type: Optional[int] = None  # 策略类型,0: 普通策略 1: 跟单策略
    follow_content: Optional[str] = None
    
    # 跟单策略临时属性
    follow_type: Optional[int] = None  # 跟单策略类型：1-止盈正开，2-止损正开，3-止盈反开
    rise_open: Optional[float] = None  # 开仓条件：上涨多少
    decline_trigger: Optional[float] = None  # 下跌触发开仓的条件
    decline_call: Optional[float] = None  # 下跌触发后，回调多少开仓
    
    fresh: Optional[int] = None
    message: Optional[str] = None  # 备注
    
    # 时间信息
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    # 分阶段止盈信息
    trailing_detail_list: List = field(default_factory=list)  # List[TrailingDetail]
    
    # 锁定状态
    is_locked: bool = False
    
    # 线程锁
    _lock: threading.RLock = field(default_factory=threading.RLock, init=False, repr=False)
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        self._ensure_numeric_types()
        
        # 初始化锁
        if not hasattr(self, '_lock'):
            self._lock = threading.RLock()
    
    def _ensure_numeric_types(self):
        """确保数值类型正确"""
        # 处理整数字段
        int_fields = ['id', 'group_id', 'user_id', 'leverage', 'state', 'strategy_type', 'follow_type', 'fresh']
        for field_name in int_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, int):
                try:
                    setattr(self, field_name, int(value))
                except (ValueError, TypeError):
                    setattr(self, field_name, None)
        
        # 处理Decimal字段
        decimal_fields = ['open_price', 'market_price', 'close_price', 'stop_loss_price', 
                         'highest_price', 'lowest_price', 'amount', 'profit_value', 'fee']
        for field_name in decimal_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, Decimal):
                try:
                    setattr(self, field_name, Decimal(str(value)))
                except (ValueError, TypeError):
                    setattr(self, field_name, None)
        
        # 处理浮点数字段
        float_fields = ['stop_loss_rate', 'profit_rate', 'rise_open', 'decline_trigger', 'decline_call']
        for field_name in float_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, float):
                try:
                    setattr(self, field_name, float(value))
                except (ValueError, TypeError):
                    setattr(self, field_name, None)
    
    def get_trailing_detail_list(self, detail_type: int) -> List:
        """
        获取指定类型的跟踪详情列表
        
        Args:
            detail_type: 详情类型
            
        Returns:
            指定类型的跟踪详情列表
        """
        result = []
        for detail in self.trailing_detail_list:
            if detail.type == detail_type:
                result.append(detail)
        return result
    
    def is_active(self) -> bool:
        """检查是否处于活跃状态"""
        return self.state == 1
    
    def is_completed(self) -> bool:
        """检查是否已完成"""
        return self.state == 2
    
    def is_disabled(self) -> bool:
        """检查是否已禁用"""
        return self.state == 0
    
    def is_follow_strategy(self) -> bool:
        """检查是否为跟单策略"""
        return self.strategy_type == 1
    
    def is_normal_strategy(self) -> bool:
        """检查是否为普通策略"""
        return self.strategy_type == 0
    
    def is_long_position(self) -> bool:
        """检查是否为多头持仓"""
        return self.position_side == "long"
    
    def is_short_position(self) -> bool:
        """检查是否为空头持仓"""
        return self.position_side == "short"
    
    def is_futures(self) -> bool:
        """检查是否为期货交易"""
        return self.inst_type == "futures"
    
    def is_spot(self) -> bool:
        """检查是否为现货交易"""
        return self.inst_type == "spot"
    
    def lock(self):
        """加锁"""
        with self._lock:
            self.is_locked = True
    
    def unlock(self):
        """解锁"""
        with self._lock:
            self.is_locked = False
    
    def calculate_profit_rate(self) -> Optional[float]:
        """计算盈利率"""
        if (self.open_price is None or self.close_price is None or 
            self.open_price == 0):
            return None
            
        if self.is_long_position():
            return float((self.close_price - self.open_price) / self.open_price * 100)
        elif self.is_short_position():
            return float((self.open_price - self.close_price) / self.open_price * 100)
        else:
            return None
    
    def calculate_profit_value(self) -> Optional[Decimal]:
        """计算盈利价值"""
        if (self.open_price is None or self.close_price is None or 
            self.amount is None):
            return None
            
        if self.is_long_position():
            return (self.close_price - self.open_price) * self.amount
        elif self.is_short_position():
            return (self.open_price - self.close_price) * self.amount
        else:
            return None
    
    @classmethod
    def builder(cls) -> 'TrailingProfitBuilder':
        """创建构建器"""
        return TrailingProfitBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if key.startswith('_'):  # 跳过私有属性
                continue
            if value is not None:
                if isinstance(value, Decimal):
                    result[key] = str(value)
                elif isinstance(value, datetime):
                    result[key] = value.isoformat()
                elif isinstance(value, list):
                    result[key] = [item.to_dict() if hasattr(item, 'to_dict') else item for item in value]
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TrailingProfit':
        """从字典创建实例"""
        # 处理特殊字段
        if 'create_time' in data and data['create_time']:
            if isinstance(data['create_time'], str):
                try:
                    data['create_time'] = datetime.fromisoformat(data['create_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['create_time'] = None
        
        if 'update_time' in data and data['update_time']:
            if isinstance(data['update_time'], str):
                try:
                    data['update_time'] = datetime.fromisoformat(data['update_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['update_time'] = None
        
        # 处理trailing_detail_list
        if 'trailing_detail_list' in data and data['trailing_detail_list']:
            from .trailing_detail import TrailingDetail
            detail_list = []
            for item in data['trailing_detail_list']:
                if isinstance(item, dict):
                    detail_list.append(TrailingDetail.from_dict(item))
                else:
                    detail_list.append(item)
            data['trailing_detail_list'] = detail_list
        
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"TrailingProfit(id={self.id}, symbol='{self.symbol}', "
                f"position_side='{self.position_side}', state={self.state})")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"TrailingProfit(id={self.id}, user_id={self.user_id}, "
                f"symbol='{self.symbol}', amount={self.amount}, state={self.state})")


class TrailingProfitBuilder:
    """TrailingProfit构建器"""
    
    def __init__(self):
        self._data = {}
    
    def id(self, id: int) -> 'TrailingProfitBuilder':
        self._data['id'] = id
        return self
    
    def group_id(self, group_id: int) -> 'TrailingProfitBuilder':
        self._data['group_id'] = group_id
        return self

    def user_id(self, user_id: int) -> 'TrailingProfitBuilder':
        self._data['user_id'] = user_id
        return self

    def user_name(self, user_name: str) -> 'TrailingProfitBuilder':
        self._data['user_name'] = user_name
        return self

    def account_name(self, account_name: str) -> 'TrailingProfitBuilder':
        self._data['account_name'] = account_name
        return self

    def platform(self, platform: str) -> 'TrailingProfitBuilder':
        self._data['platform'] = platform
        return self

    def symbol(self, symbol: str) -> 'TrailingProfitBuilder':
        self._data['symbol'] = symbol
        return self

    def open_price(self, open_price: Decimal) -> 'TrailingProfitBuilder':
        self._data['open_price'] = open_price
        return self

    def market_price(self, market_price: Decimal) -> 'TrailingProfitBuilder':
        self._data['market_price'] = market_price
        return self

    def close_price(self, close_price: Decimal) -> 'TrailingProfitBuilder':
        self._data['close_price'] = close_price
        return self

    def stop_loss_price(self, stop_loss_price: Decimal) -> 'TrailingProfitBuilder':
        self._data['stop_loss_price'] = stop_loss_price
        return self

    def stop_loss_rate(self, stop_loss_rate: float) -> 'TrailingProfitBuilder':
        self._data['stop_loss_rate'] = stop_loss_rate
        return self

    def highest_price(self, highest_price: Decimal) -> 'TrailingProfitBuilder':
        self._data['highest_price'] = highest_price
        return self

    def lowest_price(self, lowest_price: Decimal) -> 'TrailingProfitBuilder':
        self._data['lowest_price'] = lowest_price
        return self

    def amount(self, amount: Decimal) -> 'TrailingProfitBuilder':
        self._data['amount'] = amount
        return self

    def profit_value(self, profit_value: Decimal) -> 'TrailingProfitBuilder':
        self._data['profit_value'] = profit_value
        return self

    def profit_rate(self, profit_rate: float) -> 'TrailingProfitBuilder':
        self._data['profit_rate'] = profit_rate
        return self

    def inst_type(self, inst_type: str) -> 'TrailingProfitBuilder':
        self._data['inst_type'] = inst_type
        return self

    def leverage(self, leverage: int) -> 'TrailingProfitBuilder':
        self._data['leverage'] = leverage
        return self

    def position_side(self, position_side: str) -> 'TrailingProfitBuilder':
        self._data['position_side'] = position_side
        return self

    def order_type(self, order_type: str) -> 'TrailingProfitBuilder':
        self._data['order_type'] = order_type
        return self

    def trade_mode(self, trade_mode: str) -> 'TrailingProfitBuilder':
        self._data['trade_mode'] = trade_mode
        return self

    def fee(self, fee: Decimal) -> 'TrailingProfitBuilder':
        self._data['fee'] = fee
        return self

    def ref_order_open(self, ref_order_open: str) -> 'TrailingProfitBuilder':
        self._data['ref_order_open'] = ref_order_open
        return self

    def ref_order_close(self, ref_order_close: str) -> 'TrailingProfitBuilder':
        self._data['ref_order_close'] = ref_order_close
        return self

    def state(self, state: int) -> 'TrailingProfitBuilder':
        self._data['state'] = state
        return self

    def strategy_type(self, strategy_type: int) -> 'TrailingProfitBuilder':
        self._data['strategy_type'] = strategy_type
        return self

    def follow_content(self, follow_content: str) -> 'TrailingProfitBuilder':
        self._data['follow_content'] = follow_content
        return self

    def follow_type(self, follow_type: int) -> 'TrailingProfitBuilder':
        self._data['follow_type'] = follow_type
        return self

    def rise_open(self, rise_open: float) -> 'TrailingProfitBuilder':
        self._data['rise_open'] = rise_open
        return self

    def decline_trigger(self, decline_trigger: float) -> 'TrailingProfitBuilder':
        self._data['decline_trigger'] = decline_trigger
        return self

    def decline_call(self, decline_call: float) -> 'TrailingProfitBuilder':
        self._data['decline_call'] = decline_call
        return self

    def fresh(self, fresh: int) -> 'TrailingProfitBuilder':
        self._data['fresh'] = fresh
        return self

    def message(self, message: str) -> 'TrailingProfitBuilder':
        self._data['message'] = message
        return self

    def create_time(self, create_time: datetime) -> 'TrailingProfitBuilder':
        self._data['create_time'] = create_time
        return self

    def update_time(self, update_time: datetime) -> 'TrailingProfitBuilder':
        self._data['update_time'] = update_time
        return self

    def trailing_detail_list(self, trailing_detail_list: List) -> 'TrailingProfitBuilder':
        self._data['trailing_detail_list'] = trailing_detail_list
        return self
    
    def build(self) -> TrailingProfit:
        """构建TrailingProfit实例"""
        return TrailingProfit(**self._data)

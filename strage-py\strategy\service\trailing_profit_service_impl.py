"""
跟踪盈利服务实现类
迁移自: com.project.strategy.service.impl.TrailingProfitServiceImpl
"""
import logging
from decimal import Decimal, ROUND_DOWN
from typing import List, Optional

from .i_account_service import IAccountService
from .i_trailing_group_service import ITrailingGroupService
from .i_trailing_profit_service import ITrailingProfitService
from ..context.account_context import AccountContext
from ..dao.trailing_profit_dao import TrailingProfitDAO
from ..domain.entity.trailing_detail import TrailingDetail
from ..domain.entity.trailing_follow import TrailingFollow
from ..domain.entity.trailing_group import TrailingGroup
from ..domain.entity.trailing_profit import TrailingProfit
from ..enums import (
    OrderStateEnum, ExchangeEnum, InstrumentEnum, PositionSideEnum,
    OrderTypeEnum
)
from ..global_cache import GlobalCache
from ..utils.convert_utils import Convert
from ..utils.date_utils import DateUtils
from ..utils.string_utils import StringUtils

logger = logging.getLogger(__name__)


class TrailingProfitServiceImpl(ITrailingProfitService):
    """跟踪盈利服务实现类"""

    def __init__(self, db_session, account_context: AccountContext,
                 trailing_group_service: ITrailingGroupService):
        """
        初始化跟踪盈利服务
        
        Args:
            db_session: 数据库会话
            account_context: 账户上下文
            trailing_group_service: 跟踪组服务
        """
        self.db_session = db_session
        self.trailing_profit_dao = TrailingProfitDAO()
        self.account_context = account_context
        self.trailing_group_service = trailing_group_service

        # 初始化完成状态列表
        self.completed_list = [
            OrderStateEnum.STOP_LOSS.get_code(),
            OrderStateEnum.STOP_MANUAL.get_code(),
            OrderStateEnum.TAKE_PROFIT.get_code()
        ]

        logger.info("TrailingProfitServiceImpl initialized")

    def position_update(self) -> None:
        """
        持仓更新
        对应Java中的positionUpdate方法
        """
        try:
            search = TrailingProfit()
            search.state = OrderStateEnum.HOLDING.get_code()

            trailing_list = self.select_trailing_profit_list(search)
            if not trailing_list:
                return

            for trailing in trailing_list:
                self._update_position(trailing)

        except Exception as e:
            logger.error(f"持仓更新失败: {e}")

    def start_follow(self) -> None:
        """
        开始跟单
        对应Java中的startFollow方法
        """
        try:
            search = TrailingProfit()
            search.state = OrderStateEnum.FOLLOW_READY.get_code()

            trailing_list = self.select_trailing_profit_list(search)
            if not trailing_list:
                return

            for trailing in trailing_list:
                try:
                    # 获取价格缓存键
                    key_price = f"{ExchangeEnum.BINANCE.get_name()}_{trailing.symbol}"

                    # 获取当前价格
                    price_data = GlobalCache.PRICE.get(key_price)
                    if not price_data:
                        logger.error(f"GlobalCache.PRICE is null>>> {key_price}")
                        continue

                    cur_price = price_data.get("buying")
                    if not cur_price:
                        continue

                    # 处理跟单策略逻辑
                    follow = TrailingFollow.from_json(trailing.follow_content)
                    if not follow.close_price or follow.close_price == Decimal('0'):
                        last_trailing = self.select_trailing_profit_by_id(follow.trailing_id)
                        if (last_trailing and last_trailing.close_price and
                                last_trailing.close_price > Decimal('0')):
                            follow.close_price = last_trailing.close_price
                            trailing.lowest_price = last_trailing.close_price
                            trailing.follow_content = follow.to_json_string()
                            self.update_by_id(trailing)
                        else:
                            continue

                    if self._is_open_follow(trailing, follow, cur_price):
                        self._open_position(trailing)

                except Exception as e:
                    logger.error(f"处理跟单策略失败: {e}")

        except Exception as e:
            logger.error(f"开始跟单失败: {e}")

    def _is_open_follow(self, trailing: TrailingProfit, follow: TrailingFollow, cur_price: Decimal) -> bool:
        """
        判断是否开启跟单
        对应Java中的isOpenFollow方法
        """
        try:
            is_open = False
            position_side_enum = PositionSideEnum.parse_value(trailing.position_side)
            close_price = follow.close_price
            lowest_price = trailing.lowest_price

            if position_side_enum == PositionSideEnum.LONG:
                # 判断是否上涨达到开仓条件，直接开仓
                rise_rate = ((cur_price - close_price) / close_price * Decimal('100')).quantize(
                    Decimal('0.0001'), rounding=ROUND_DOWN
                )

                if rise_rate >= Decimal(str(follow.rise_open or 0)):
                    is_open = True

                # 判断下跌是否超过触发条件
                decline_rate = ((close_price - lowest_price) / close_price * Decimal('100')).quantize(
                    Decimal('0.0001'), rounding=ROUND_DOWN
                )

                if decline_rate >= Decimal(str(follow.decline_trigger or 0)):
                    # 跟最低点回调了多少
                    decline_call = ((cur_price - lowest_price) / lowest_price * Decimal('100')).quantize(
                        Decimal('0.0001'), rounding=ROUND_DOWN
                    )

                    if decline_call >= Decimal(str(follow.decline_call or 0)):
                        is_open = True

            else:  # SHORT
                # 判断是否下跌达到开仓条件，直接开仓
                rise_rate = ((close_price - cur_price) / close_price * Decimal('100')).quantize(
                    Decimal('0.0001'), rounding=ROUND_DOWN
                )

                if rise_rate >= Decimal(str(follow.rise_open or 0)):
                    is_open = True

                # 判断上涨是否超过触发条件
                decline_rate = ((lowest_price - close_price) / close_price * Decimal('100')).quantize(
                    Decimal('0.0001'), rounding=ROUND_DOWN
                )

                if decline_rate >= Decimal(str(follow.decline_trigger or 0)):
                    # 跟最高点回调了多少
                    decline_call = ((lowest_price - cur_price) / lowest_price * Decimal('100')).quantize(
                        Decimal('0.0001'), rounding=ROUND_DOWN
                    )

                    if decline_call >= Decimal(str(follow.decline_call or 0)):
                        is_open = True

            return is_open

        except Exception as e:
            logger.error(f"判断是否开启跟单失败: {e}")
            return False

    def start_trailing(self) -> None:
        """
        开始跟踪
        对应Java中的startTraling方法
        """
        try:
            search = TrailingProfit()
            search.state = OrderStateEnum.UNHANDLE.get_code()

            trailing_list = self.select_trailing_profit_list(search)
            if not trailing_list:
                return

            for trailing in trailing_list:
                try:
                    # 跟单策略跳过
                    if trailing.strategy_type == 1:
                        continue

                    symbol = trailing.symbol
                    key_price = f"{ExchangeEnum.BINANCE.get_name()}_{symbol}"
                    price_json = GlobalCache.PRICE.get(key_price)
                    if not price_json:
                        logger.error(f"*** GlobalCache.PRICE is null>>> {key_price}")
                        continue

                    self._open_position(trailing)

                except Exception as e:
                    logger.error(f"处理跟踪策略失败: {e}")

        except Exception as e:
            logger.error(f"开始跟踪失败: {e}")

    def _update_position(self, trailing: TrailingProfit) -> None:
        """
        更新持仓信息
        对应Java中的updatePosition方法
        """
        try:
            # 检查开仓订单ID
            if not trailing.ref_order_open:
                trailing.state = OrderStateEnum.UNHANDLE.get_code()
                self.update_by_id(trailing)
                return

            symbol = trailing.symbol
            account_client: Optional[IAccountService] = self.account_context.get_account_client(
                ExchangeEnum.BINANCE, InstrumentEnum.FUTURES_U, trailing.account_name
            )

            if not account_client:
                logger.error(f"accountName return null client: id: {trailing.id}, {trailing.account_name}")
                return

            # 获取持仓信息
            position_map = account_client.get_position(trailing.symbol)
            order_json = position_map.get(f"{symbol}_{trailing.position_side}")

            if order_json:
                market_price = Decimal(str(order_json.get("marketPrice", "0")))
                open_price = Decimal(str(order_json.get("openPrice", "0")))

                # 初始化开仓价格和止损价格
                if not trailing.open_price or trailing.open_price == Decimal('0'):
                    stop_rate_value = self._truncate_decimal(
                        open_price * Decimal(str(trailing.stop_loss_rate / 100))
                    )

                    if PositionSideEnum.parse_value(trailing.position_side) == PositionSideEnum.LONG:
                        stop_loss_price = open_price - stop_rate_value
                    else:
                        stop_loss_price = open_price + stop_rate_value

                    trailing.stop_loss_price = stop_loss_price
                    trailing.open_price = open_price
                    trailing.highest_price = open_price
                    trailing.lowest_price = open_price

                trailing.market_price = market_price
                trailing.profit_value = Decimal(str(order_json.get("unRealizedProfit", "0")))

                # 计算盈利率
                profit_rate = 0.0
                if PositionSideEnum.parse_value(trailing.position_side) == PositionSideEnum.LONG:
                    if open_price > 0:
                        profit_rate = ((market_price - open_price) / open_price * Decimal('100') *
                                       Decimal(str(trailing.leverage))).quantize(Decimal('0.0001'), rounding=ROUND_DOWN)
                        profit_rate = float(profit_rate)
                else:
                    if open_price > 0:
                        profit_rate = ((open_price - market_price) / open_price * Decimal('100') *
                                       Decimal(str(trailing.leverage))).quantize(Decimal('0.0001'), rounding=ROUND_DOWN)
                        profit_rate = float(profit_rate)

                trailing.profit_rate = profit_rate
                self.update_by_id(trailing)

                # 处理TrailingDetail列表
                detail_list = self.trailing_profit_dao.select_trailing_detail_list(trailing.id)
                for detail in detail_list:
                    trigger_price = detail.trigger_price
                    if not trigger_price or trigger_price == Decimal('0'):
                        if trailing.position_side == PositionSideEnum.LONG.get_value():
                            if detail.type == 0:  # 分阶段止盈
                                trigger_price = trailing.open_price * Decimal(str(detail.price_gain / 100 + 1))
                            else:  # type == 1, 分阶段止损
                                trigger_price = trailing.open_price * Decimal(str(1 - detail.price_gain / 100))
                        else:
                            if detail.type == 0:  # 分阶段止盈
                                trigger_price = trailing.open_price * Decimal(str(1 - detail.price_gain / 100))
                            else:
                                trigger_price = trailing.open_price * Decimal(str(detail.price_gain / 100 + 1))

                        detail.trigger_price = trigger_price
                        self.trailing_profit_dao.update_trailing_detail(detail)

                trailing.trailing_detail_list = detail_list

                # 更新GlobalCache
                strategy_map = GlobalCache.STRATEGY_TRAILING.get(symbol)
                if not strategy_map:
                    strategy_map = {}
                    strategy_map[trailing.id] = trailing
                    GlobalCache.STRATEGY_TRAILING[symbol] = strategy_map
                else:
                    # 如果内存中的openPrice=0，说明对象是openPosition时放进去的，此时需要将数据同步进去
                    cached_trailing = strategy_map.get(trailing.id)
                    if not cached_trailing or not cached_trailing.open_price or cached_trailing.open_price == Decimal(
                            '0'):
                        strategy_map[trailing.id] = trailing
            else:
                logger.info(f"return updatePosition null>>> {trailing.id}")

        except Exception as e:
            logger.error(f"更新持仓信息失败: {e}")

    def _close_position(self, trailing: TrailingProfit) -> None:
        """
        平仓
        对应Java中的closePosition方法
        """
        try:
            account_client: Optional[IAccountService] = self.account_context.get_account_client(
                ExchangeEnum.BINANCE, InstrumentEnum.FUTURES_U, trailing.account_name
            )

            if not account_client:
                logger.error(f"accountClient is null>>> {trailing.account_name}")
                return

            position_side_enum = PositionSideEnum.parse_value(trailing.position_side)
            close_pos_json = account_client.close_position(
                trailing.symbol, float(trailing.amount), position_side_enum
            )

            if not close_pos_json:
                logger.error(f"closePosition error>>> trailingId: {trailing.id} return null")
                return

            # 检查平仓结果
            is_completed = close_pos_json.get("isCompleted", False)
            if is_completed:
                order_id = close_pos_json.get("orderId", "")
                trailing.ref_order_close = order_id
                trailing.state = OrderStateEnum.STOP_LOSS.get_code()
                self.update_by_id(trailing)

                # 从全局缓存中移除
                GlobalCache.remove_strategy_trailing(trailing.symbol, trailing.id)
            else:
                msg = close_pos_json.get("msg", "")
                logger.error(f"closePosition failed>>> trailingId: {trailing.id}, msg: {msg}")

        except Exception as e:
            logger.error(f"平仓失败: {e}")

    def _open_position(self, trailing: TrailingProfit) -> None:
        """
        开仓
        对应Java中的openPosition方法
        """
        try:
            position_side_enum = PositionSideEnum.parse_value(trailing.position_side)
            account_client: Optional[IAccountService] = self.account_context.get_account_client(
                ExchangeEnum.BINANCE, InstrumentEnum.FUTURES_U, trailing.account_name
            )

            if not account_client:
                logger.error(f"accountClient is null>>> {trailing.account_name}")
                return

            res_json = account_client.open_position(
                trailing.symbol, float(trailing.amount), trailing.leverage, position_side_enum
            )

            if not res_json:
                logger.error(f"OpenPositon error>>> trailingId: {trailing.id}, return null")
                return

            logger.info(f"openPosition>>> {trailing.id}, {res_json}")

            # 检查开仓结果
            is_completed = res_json.get("isCompleted", False)
            if is_completed:
                order_id = res_json.get("orderId", "")
                trailing.ref_order_open = order_id
                trailing.state = OrderStateEnum.HOLDING.get_code()
            else:
                trailing.message = res_json.get("msg", "")

            # 设置状态为持仓中（Java版本有重复设置，这里保持一致）
            trailing.state = OrderStateEnum.HOLDING.get_code()
            self.update_by_id(trailing)

            # 加载TrailingDetail列表
            if not trailing.trailing_detail_list:
                detail_list = self.select_detail_by_trailing(trailing.id)
                trailing.trailing_detail_list = detail_list

            # 添加到全局缓存
            strategy_map = GlobalCache.STRATEGY_TRAILING.get(trailing.symbol)
            if not strategy_map:
                strategy_map = {}
                strategy_map[trailing.id] = trailing
                GlobalCache.STRATEGY_TRAILING[trailing.symbol] = strategy_map
            else:
                strategy_map[trailing.id] = trailing

        except Exception as e:
            logger.error(f"开仓失败: {e}")

    def refresh_order(self) -> None:
        """
        刷新订单
        对应Java中的refreshOrder方法
        """
        try:
            trailing_list = self.trailing_profit_dao.select_by_refresh(self.completed_list)

            for trailing in trailing_list:
                try:
                    if not trailing.ref_order_close:
                        trailing.fresh = 1
                        self.update_trailing_profit(trailing)
                        continue

                    account_client: Optional[IAccountService] = self.account_context.get_account_client(
                        ExchangeEnum.BINANCE, InstrumentEnum.FUTURES_U, trailing.account_name
                    )

                    if account_client:
                        account_client.fill_position(trailing)
                        trailing.fresh = 1
                        self.update_trailing_profit(trailing)

                except Exception as e:
                    logger.error(f"刷新订单失败: {e}")

        except Exception as e:
            logger.error(f"刷新订单失败: {e}")

    def select_trailing_follow_by_group(self, group_id: int) -> Optional[TrailingProfit]:
        """
        根据组ID查询跟踪跟单
        """
        try:
            return self.trailing_profit_dao.select_trailing_follow_by_group(group_id)
        except Exception as e:
            logger.error(f"根据组ID查询跟踪跟单失败: {e}")
            return None

    def select_trailing_profit_by_id(self, id: int) -> Optional[TrailingProfit]:
        """
        根据ID查询跟踪盈利
        """
        try:
            return self.trailing_profit_dao.select_trailing_profit_by_id(id)
        except Exception as e:
            logger.error(f"根据ID查询跟踪盈利失败: {e}")
            return None

    def select_trailing_profit_list(self, trailing_profit: Optional[TrailingProfit] = None) -> List[TrailingProfit]:
        """
        查询跟踪盈利列表
        """
        try:
            return self.trailing_profit_dao.select_trailing_profit_list(trailing_profit)
        except Exception as e:
            logger.error(f"查询跟踪盈利列表失败: {e}")
            return []

    def select_detail_by_trailing(self, id: int) -> List[TrailingDetail]:
        """
        根据跟踪ID查询详情列表
        对应Java中的selectDetailByTrailing方法
        """
        try:
            return self.trailing_profit_dao.select_trailing_detail_list(id)
        except Exception as e:
            logger.error(f"根据跟踪ID查询详情列表失败: {e}")
            return []

    def insert_trailing_profit(self, trailing_profit: TrailingProfit) -> bool:
        """
        插入跟踪盈利
        对应Java中的insertTrailingProfit方法
        """
        try:
            trailing_profit.create_time = DateUtils.get_now_date()

            # 处理跟踪详情列表
            detail_list = trailing_profit.trailing_detail_list
            for detail in detail_list:
                detail.state = 0
                detail.create_time = DateUtils.get_now_date()

            # 设置订单类型
            trailing_profit.order_type = OrderTypeEnum.BN_MARKET.get_value()

            # 插入主记录
            success = self.trailing_profit_dao.insert_trailing_profit(trailing_profit)
            if success:
                # 插入详情记录
                self._insert_trailing_detail(trailing_profit)

            return success
        except Exception as e:
            logger.error(f"插入跟踪盈利失败: {e}")
            return False

    def update_by_id(self, trailing_profit: TrailingProfit) -> bool:
        """
        根据ID更新跟踪盈利
        """
        try:
            trailing_profit.update_time = DateUtils.get_now_date()
            return self.trailing_profit_dao.update_trailing_profit(trailing_profit)
        except Exception as e:
            logger.error(f"根据ID更新跟踪盈利失败: {e}")
            return False

    def update_detail_by_id(self, detail: TrailingDetail) -> bool:
        """
        根据ID更新跟踪详情
        """
        try:
            detail.update_time = DateUtils.get_now_date()
            return self.trailing_profit_dao.update_trailing_detail(detail)
        except Exception as e:
            logger.error(f"根据ID更新跟踪详情失败: {e}")
            return False

    def update_trailing_profit(self, trailing_profit: TrailingProfit) -> bool:
        """
        更新跟踪盈利
        """
        try:
            trailing_profit.update_time = DateUtils.get_now_date()
            return self.trailing_profit_dao.update_trailing_profit(trailing_profit)
        except Exception as e:
            logger.error(f"更新跟踪盈利失败: {e}")
            return False

    def delete_trailing_profit_by_ids(self, ids: str) -> int:
        """
        根据ID列表删除跟踪盈利
        """
        try:
            id_array = Convert.to_str_array(ids)
            # 先删除详情
            self.trailing_profit_dao.delete_trailing_detail_by_trailing_profit_ids(id_array)
            # 再删除主记录
            return self.trailing_profit_dao.delete_trailing_profit_by_ids(id_array)
        except Exception as e:
            logger.error(f"根据ID列表删除跟踪盈利失败: {e}")
            return 0

    def stop_strategy_group(self, user_id: int, id: int) -> int:
        """
        停止策略组
        对应Java中的stopStragegyGroup方法
        """
        try:
            profit = self.trailing_profit_dao.select_trailing_profit_by_id(id)
            if not profit:
                return 0

            group_id = profit.group_id
            group = self.trailing_group_service.select_trailing_group_by_id(group_id)

            if group.state != 1:
                raise RuntimeError("停止异常，策略组并非运行中。")

            # 获取组内所有策略
            profit_list = self.trailing_profit_dao.select_by_group(user_id, group_id)

            for trailing in profit_list:
                if trailing.state == 1:  # 持仓中
                    self._close_position(trailing)
                    trailing.state = 6  # 手动停止
                    self.trailing_profit_dao.update_trailing_profit(trailing)

            # 更新组状态
            group.state = 5  # 手动停止
            return self.trailing_group_service.update_trailing_group(group)

        except Exception as e:
            logger.error(f"停止策略组失败: {e}")
            return 0

    def start_strategy_group(self, user_id: int, id: int) -> int:
        """
        启动策略组
        对应Java中的startStragegyGroup方法
        """
        try:
            profit = self.trailing_profit_dao.select_trailing_profit_by_id(id)
            if not profit:
                return 0

            group_id = profit.group_id
            group = self.trailing_group_service.select_trailing_group_by_id(group_id)

            if group.state != 0:
                raise RuntimeError("启动异常，策略组已经启动。")

            # 获取组内所有策略
            profit_list = self.trailing_profit_dao.select_by_group(user_id, group_id)

            # 检查是否都是未启用状态
            is_start = True
            for trailing in profit_list:
                if trailing.state != OrderStateEnum.UNAVAILABLE.get_code():
                    is_start = False
                    break

            if not is_start:
                raise RuntimeError("启动异常，请检查要启动的策略组数据。")

            if not profit_list:
                return 0

            # 更新组状态
            group.state = 1  # 运行中
            self.trailing_group_service.update_trailing_group(group)

            # 使用数据库的startGroup方法
            return self.trailing_profit_dao.start_group(user_id, group_id)

        except Exception as e:
            logger.error(f"启动策略组失败: {e}")
            return 0

    def add_strategy_group(self, user_id: int, ids: str) -> int:
        """
        添加策略组
        对应Java中的addStragegyGroup方法
        """
        try:
            id_array = Convert.to_str_array(ids)
            profit_list = self.trailing_profit_dao.select_by_ids(id_array)

            # 判断此策略是否已经归属过策略组
            for profit in profit_list:
                if profit.group_id and profit.group_id != 0:
                    return 0

            # 创建新的策略组
            group = TrailingGroup()
            group.user_id = user_id
            group.state = 0

            success = self.trailing_group_service.insert_trailing_group(group)
            if not success:
                return 0

            # 更新策略的组ID
            return self.trailing_profit_dao.add_trailing_group(group.id, user_id, id_array)

        except Exception as e:
            logger.error(f"添加策略组失败: {e}")
            return 0

    def delete_trailing_profit_by_id(self, id: int) -> int:
        """
        根据ID删除跟踪盈利
        """
        try:
            # 先删除详情
            self.trailing_profit_dao.delete_trailing_detail_by_trailing_profit_id(id)
            # 再删除主记录
            return self.trailing_profit_dao.delete_trailing_profit_by_id(id)
        except Exception as e:
            logger.error(f"根据ID删除跟踪盈利失败: {e}")
            return 0

    def _insert_trailing_detail(self, trailing_profit: TrailingProfit) -> None:
        """
        插入跟踪详情
        对应Java中的insertTrailingDetail方法
        """
        try:
            trailing_detail_list = trailing_profit.trailing_detail_list
            trailing_id = trailing_profit.id

            if StringUtils.is_not_null(trailing_detail_list):
                detail_list = []
                for trailing_detail in trailing_detail_list:
                    trailing_detail.trailing_profit_id = trailing_id
                    detail_list.append(trailing_detail)

                if detail_list:
                    self.trailing_profit_dao.batch_trailing_detail(detail_list)

        except Exception as e:
            logger.error(f"插入跟踪详情失败: {e}")

    @staticmethod
    def _truncate_decimal(value: Decimal) -> Decimal:
        """
        截断小数
        对应Java中的truncateDecimal方法
        """
        try:
            # 去除末尾0并转换为普通字符串格式
            str_value = str(value).rstrip('0').rstrip('.')

            if '.' not in str_value:
                return value  # 没有小数部分，直接返回

            parts = str_value.split('.')
            integer_part = parts[0]
            decimal_part = parts[1]

            # 情况1：整数部分为0
            if integer_part == "0":
                # 找到第一个非零小数位
                first_non_zero_index = -1
                for i, char in enumerate(decimal_part):
                    if char != '0':
                        first_non_zero_index = i
                        break

                # 如果小数部分全是0，则返回0
                if first_non_zero_index == -1:
                    return Decimal('0')

                # 计算要保留的长度
                end_index = min(first_non_zero_index + 4, len(decimal_part))

                # 构造新的小数部分
                new_decimal_part = decimal_part[:end_index]
                return Decimal(f"0.{new_decimal_part}")

            # 情况2：整数部分不为0，只保留前4位小数
            decimal_length = min(4, len(decimal_part))
            new_decimal_part = decimal_part[:decimal_length]

            # 如果新小数部分全是0，则返回整数部分
            if new_decimal_part.replace("0", "") == "":
                return Decimal(integer_part)

            return Decimal(f"{integer_part}.{new_decimal_part}")

        except Exception as e:
            logger.error(f"截断小数失败: {e}")
            return Decimal('0')

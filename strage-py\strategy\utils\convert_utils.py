"""
转换工具类
迁移自: com.common.utils.text.Convert
"""
from typing import List, Any, Optional


class Convert:
    """转换工具类"""
    
    @staticmethod
    def to_str_array(ids: str) -> List[str]:
        """
        将逗号分隔的字符串转换为字符串数组
        
        Args:
            ids: 逗号分隔的字符串
            
        Returns:
            字符串数组
        """
        if not ids:
            return []
        
        return [item.strip() for item in ids.split(',') if item.strip()]
    
    @staticmethod
    def to_int_array(ids: str) -> List[int]:
        """
        将逗号分隔的字符串转换为整数数组
        
        Args:
            ids: 逗号分隔的字符串
            
        Returns:
            整数数组
        """
        str_array = Convert.to_str_array(ids)
        int_array = []
        
        for item in str_array:
            try:
                int_array.append(int(item))
            except (ValueError, TypeError):
                continue
        
        return int_array
    
    @staticmethod
    def to_long_array(ids: str) -> List[int]:
        """
        将逗号分隔的字符串转换为长整数数组
        
        Args:
            ids: 逗号分隔的字符串
            
        Returns:
            长整数数组
        """
        return Convert.to_int_array(ids)
    
    @staticmethod
    def to_str(value: Any, default_value: str = "") -> str:
        """
        将值转换为字符串
        
        Args:
            value: 要转换的值
            default_value: 默认值
            
        Returns:
            字符串值
        """
        if value is None:
            return default_value
        
        return str(value)
    
    @staticmethod
    def to_int(value: Any, default_value: int = 0) -> int:
        """
        将值转换为整数
        
        Args:
            value: 要转换的值
            default_value: 默认值
            
        Returns:
            整数值
        """
        if value is None:
            return default_value
        
        try:
            return int(value)
        except (ValueError, TypeError):
            return default_value
    
    @staticmethod
    def to_float(value: Any, default_value: float = 0.0) -> float:
        """
        将值转换为浮点数
        
        Args:
            value: 要转换的值
            default_value: 默认值
            
        Returns:
            浮点数值
        """
        if value is None:
            return default_value
        
        try:
            return float(value)
        except (ValueError, TypeError):
            return default_value
    
    @staticmethod
    def to_bool(value: Any, default_value: bool = False) -> bool:
        """
        将值转换为布尔值
        
        Args:
            value: 要转换的值
            default_value: 默认值
            
        Returns:
            布尔值
        """
        if value is None:
            return default_value
        
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        
        try:
            return bool(value)
        except (ValueError, TypeError):
            return default_value

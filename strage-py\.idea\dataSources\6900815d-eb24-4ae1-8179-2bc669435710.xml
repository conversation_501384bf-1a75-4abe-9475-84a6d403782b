<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root|localhost|ALTER|G
|root||root|localhost|ALTER ROUTINE|G
|root||root|localhost|APPLICATION_PASSWORD_ADMIN|G
|root||mysql.infoschema|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.session|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.sys|localhost|AUDIT_ABORT_EXEMPT|G
|root||root|localhost|AUDIT_ABORT_EXEMPT|G
|root||root|localhost|AUDIT_ADMIN|G
|root||mysql.session|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||root|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||mysql.session|localhost|BACKUP_ADMIN|G
|root||root|localhost|BACKUP_ADMIN|G
|root||root|localhost|BINLOG_ADMIN|G
|root||root|localhost|BINLOG_ENCRYPTION_ADMIN|G
|root||mysql.session|localhost|CLONE_ADMIN|G
|root||root|localhost|CLONE_ADMIN|G
|root||mysql.session|localhost|CONNECTION_ADMIN|G
|root||root|localhost|CONNECTION_ADMIN|G
|root||root|localhost|CREATE|G
|root||root|localhost|CREATE ROLE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE USER|G
|root||root|localhost|CREATE VIEW|G
|root||root|localhost|DELETE|G
|root||root|localhost|DROP|G
|root||root|localhost|DROP ROLE|G
|root||root|localhost|ENCRYPTION_KEY_ADMIN|G
|root||root|localhost|EVENT|G
|root||root|localhost|EXECUTE|G
|root||root|localhost|FILE|G
|root||mysql.infoschema|localhost|FIREWALL_EXEMPT|G
|root||mysql.session|localhost|FIREWALL_EXEMPT|G
|root||mysql.sys|localhost|FIREWALL_EXEMPT|G
|root||root|localhost|FIREWALL_EXEMPT|G
|root||root|localhost|FLUSH_OPTIMIZER_COSTS|G
|root||root|localhost|FLUSH_STATUS|G
|root||root|localhost|FLUSH_TABLES|G
|root||root|localhost|FLUSH_USER_RESOURCES|G
|root||root|localhost|GROUP_REPLICATION_ADMIN|G
|root||root|localhost|GROUP_REPLICATION_STREAM|G
|root||root|localhost|INDEX|G
|root||root|localhost|INNODB_REDO_LOG_ARCHIVE|G
|root||root|localhost|INNODB_REDO_LOG_ENABLE|G
|root||root|localhost|INSERT|G
|root||root|localhost|LOCK TABLES|G
|root||root|localhost|PASSWORDLESS_USER_ADMIN|G
|root||mysql.session|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root|localhost|PROCESS|G
|root||root|localhost|REFERENCES|G
|root||root|localhost|RELOAD|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root|localhost|REPLICATION_APPLIER|G
|root||root|localhost|REPLICATION_SLAVE_ADMIN|G
|root||root|localhost|RESOURCE_GROUP_ADMIN|G
|root||root|localhost|RESOURCE_GROUP_USER|G
|root||root|localhost|ROLE_ADMIN|G
|root||mysql.infoschema|localhost|SELECT|G
|root||root|localhost|SELECT|G
|root||root|localhost|SENSITIVE_VARIABLES_OBSERVER|G
|root||root|localhost|SERVICE_CONNECTION_ADMIN|G
|root||mysql.session|localhost|SESSION_VARIABLES_ADMIN|G
|root||root|localhost|SESSION_VARIABLES_ADMIN|G
|root||root|localhost|SET_USER_ID|G
|root||root|localhost|SHOW DATABASES|G
|root||root|localhost|SHOW VIEW|G
|root||root|localhost|SHOW_ROUTINE|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||root|localhost|SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root|localhost|SUPER|G
|root||mysql.infoschema|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_USER|G
|root||mysql.sys|localhost|SYSTEM_USER|G
|root||root|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root|localhost|TABLE_ENCRYPTION_ADMIN|G
|root||root|localhost|TELEMETRY_LOG_ADMIN|G
|root||root|localhost|TRIGGER|G
|root||root|localhost|UPDATE|G
|root||root|localhost|XA_RECOVER_ADMIN|G
|root||root|localhost|grant option|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G</Grants>
      <ServerVersion>8.0.42</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="demo">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-07-29.15:30:18</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="meal_system">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="mysql">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="second_hand_trading">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="294" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="295" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="296" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="297" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="298" parent="1" name="root">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <table id="299" parent="288" name="ex_account">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb3_bin</CollationName>
    </table>
    <table id="300" parent="288" name="gen_table">
      <Comment>代码生成业务表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="301" parent="288" name="gen_table_column">
      <Comment>代码生成业务表字段</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="302" parent="288" name="subscribe_symbol">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb3_bin</CollationName>
    </table>
    <table id="303" parent="288" name="sys_config">
      <Comment>参数配置表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="304" parent="288" name="sys_dept">
      <Comment>部门表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="305" parent="288" name="sys_dict_data">
      <Comment>字典数据表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="306" parent="288" name="sys_dict_type">
      <Comment>字典类型表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="307" parent="288" name="sys_job">
      <Comment>定时任务调度表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="308" parent="288" name="sys_job_log">
      <Comment>定时任务调度日志表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="309" parent="288" name="sys_logininfor">
      <Comment>系统访问记录</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="310" parent="288" name="sys_menu">
      <Comment>菜单权限表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="311" parent="288" name="sys_notice">
      <Comment>通知公告表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="312" parent="288" name="sys_oper_log">
      <Comment>操作日志记录</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="313" parent="288" name="sys_post">
      <Comment>岗位信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="314" parent="288" name="sys_role">
      <Comment>角色信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="315" parent="288" name="sys_role_dept">
      <Comment>角色和部门关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="316" parent="288" name="sys_role_menu">
      <Comment>角色和菜单关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="317" parent="288" name="sys_user">
      <Comment>用户信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="318" parent="288" name="sys_user_online">
      <Comment>在线用户记录</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="319" parent="288" name="sys_user_post">
      <Comment>用户与岗位关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="320" parent="288" name="sys_user_role">
      <Comment>用户和角色关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="321" parent="288" name="trailing_detail">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="322" parent="288" name="trailing_group">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="323" parent="288" name="trailing_group1">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="324" parent="288" name="trailing_profit">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <column id="325" parent="299" name="id">
      <AutoIncrement>4</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="326" parent="299" name="ex_account">
      <Comment>交易所账户</Comment>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="327" parent="299" name="account_name">
      <Comment>api账户</Comment>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="328" parent="299" name="user_id">
      <Comment>系统userId</Comment>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="329" parent="299" name="apikey">
      <Comment>apikey</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="330" parent="299" name="secret_key">
      <Comment>密钥</Comment>
      <Position>6</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="331" parent="299" name="password">
      <Comment>密码</Comment>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="332" parent="299" name="platform">
      <Comment>平台</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="333" parent="299" name="state">
      <Comment>0: 不可用， 1：可用</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="334" parent="299" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="335" parent="299" name="update_time">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="336" parent="299" name="message">
      <Comment>描述信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="337" parent="299" name="secret_msg">
      <Comment>描述信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="338" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="339" parent="299" name="account_name">
      <ColNames>account_name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="340" parent="299" name="platform">
      <ColNames>platform</ColNames>
      <Type>btree</Type>
    </index>
    <key id="341" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="342" parent="299" name="account_name">
      <UnderlyingIndexName>account_name</UnderlyingIndexName>
    </key>
    <column id="343" parent="300" name="table_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>编号</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="344" parent="300" name="table_name">
      <Comment>表名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="345" parent="300" name="table_comment">
      <Comment>表描述</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="346" parent="300" name="sub_table_name">
      <Comment>关联子表的表名</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="347" parent="300" name="sub_table_fk_name">
      <Comment>子表关联的外键名</Comment>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="348" parent="300" name="class_name">
      <Comment>实体类名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="349" parent="300" name="tpl_category">
      <Comment>使用的模板（crud单表操作 tree树表操作 sub主子表操作）</Comment>
      <DefaultExpression>&apos;crud&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="350" parent="300" name="package_name">
      <Comment>生成包路径</Comment>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="351" parent="300" name="module_name">
      <Comment>生成模块名</Comment>
      <Position>9</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="352" parent="300" name="business_name">
      <Comment>生成业务名</Comment>
      <Position>10</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="353" parent="300" name="function_name">
      <Comment>生成功能名</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="354" parent="300" name="function_author">
      <Comment>生成功能作者</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="355" parent="300" name="form_col_num">
      <Comment>表单布局（单列 双列 三列）</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="356" parent="300" name="gen_type">
      <Comment>生成代码方式（0zip压缩包 1自定义路径）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="357" parent="300" name="gen_path">
      <Comment>生成路径（不填默认项目路径）</Comment>
      <DefaultExpression>&apos;/&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="358" parent="300" name="options">
      <Comment>其它生成选项</Comment>
      <Position>16</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="359" parent="300" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="360" parent="300" name="create_time">
      <Comment>创建时间</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="361" parent="300" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="362" parent="300" name="update_time">
      <Comment>更新时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="363" parent="300" name="remark">
      <Comment>备注</Comment>
      <Position>21</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="364" parent="300" name="PRIMARY">
      <ColNames>table_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="365" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="366" parent="301" name="column_id">
      <AutoIncrement>10</AutoIncrement>
      <Comment>编号</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="367" parent="301" name="table_id">
      <Comment>归属表编号</Comment>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="368" parent="301" name="column_name">
      <Comment>列名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="369" parent="301" name="column_comment">
      <Comment>列描述</Comment>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="370" parent="301" name="column_type">
      <Comment>列类型</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="371" parent="301" name="java_type">
      <Comment>JAVA类型</Comment>
      <Position>6</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="372" parent="301" name="java_field">
      <Comment>JAVA字段名</Comment>
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="373" parent="301" name="is_pk">
      <Comment>是否主键（1是）</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="374" parent="301" name="is_increment">
      <Comment>是否自增（1是）</Comment>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="375" parent="301" name="is_required">
      <Comment>是否必填（1是）</Comment>
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="376" parent="301" name="is_insert">
      <Comment>是否为插入字段（1是）</Comment>
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="377" parent="301" name="is_edit">
      <Comment>是否编辑字段（1是）</Comment>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="378" parent="301" name="is_list">
      <Comment>是否列表字段（1是）</Comment>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="379" parent="301" name="is_query">
      <Comment>是否查询字段（1是）</Comment>
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="380" parent="301" name="query_type">
      <Comment>查询方式（等于、不等于、大于、小于、范围）</Comment>
      <DefaultExpression>&apos;EQ&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="381" parent="301" name="html_type">
      <Comment>显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）</Comment>
      <Position>16</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="382" parent="301" name="dict_type">
      <Comment>字典类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="383" parent="301" name="sort">
      <Comment>排序</Comment>
      <Position>18</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="384" parent="301" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="385" parent="301" name="create_time">
      <Comment>创建时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="386" parent="301" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>21</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="387" parent="301" name="update_time">
      <Comment>更新时间</Comment>
      <Position>22</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="388" parent="301" name="PRIMARY">
      <ColNames>column_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="389" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="390" parent="302" name="id">
      <AutoIncrement>6</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="391" parent="302" name="platform">
      <Comment>平台</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="392" parent="302" name="symbol">
      <Comment>交易所对应币对名</Comment>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="393" parent="302" name="state">
      <Comment>0: 不开启， 1：开启订阅</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="394" parent="302" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="395" parent="302" name="update_time">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="396" parent="302" name="message">
      <Comment>描述信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="397" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="398" parent="302" name="platform_symbol">
      <ColNames>platform
symbol</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="399" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="400" parent="302" name="platform_symbol">
      <UnderlyingIndexName>platform_symbol</UnderlyingIndexName>
    </key>
    <column id="401" parent="303" name="config_id">
      <AutoIncrement>100</AutoIncrement>
      <Comment>参数主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="402" parent="303" name="config_name">
      <Comment>参数名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="403" parent="303" name="config_key">
      <Comment>参数键名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="404" parent="303" name="config_value">
      <Comment>参数键值</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="405" parent="303" name="config_type">
      <Comment>系统内置（Y是 N否）</Comment>
      <DefaultExpression>&apos;N&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="406" parent="303" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="407" parent="303" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="408" parent="303" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="409" parent="303" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="410" parent="303" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="411" parent="303" name="PRIMARY">
      <ColNames>config_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="412" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="413" parent="304" name="dept_id">
      <AutoIncrement>200</AutoIncrement>
      <Comment>部门id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="414" parent="304" name="parent_id">
      <Comment>父部门id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="415" parent="304" name="ancestors">
      <Comment>祖级列表</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="416" parent="304" name="dept_name">
      <Comment>部门名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="417" parent="304" name="order_num">
      <Comment>显示顺序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="418" parent="304" name="leader">
      <Comment>负责人</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="419" parent="304" name="phone">
      <Comment>联系电话</Comment>
      <Position>7</Position>
      <StoredType>varchar(11)|0s</StoredType>
    </column>
    <column id="420" parent="304" name="email">
      <Comment>邮箱</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="421" parent="304" name="status">
      <Comment>部门状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="422" parent="304" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="423" parent="304" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="424" parent="304" name="create_time">
      <Comment>创建时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="425" parent="304" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="426" parent="304" name="update_time">
      <Comment>更新时间</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="427" parent="304" name="PRIMARY">
      <ColNames>dept_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="428" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="429" parent="305" name="dict_code">
      <AutoIncrement>100</AutoIncrement>
      <Comment>字典编码</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="430" parent="305" name="dict_sort">
      <Comment>字典排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="431" parent="305" name="dict_label">
      <Comment>字典标签</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="432" parent="305" name="dict_value">
      <Comment>字典键值</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="433" parent="305" name="dict_type">
      <Comment>字典类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="434" parent="305" name="css_class">
      <Comment>样式属性（其他样式扩展）</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="435" parent="305" name="list_class">
      <Comment>表格回显样式</Comment>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="436" parent="305" name="is_default">
      <Comment>是否默认（Y是 N否）</Comment>
      <DefaultExpression>&apos;N&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="437" parent="305" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="438" parent="305" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="439" parent="305" name="create_time">
      <Comment>创建时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="440" parent="305" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="441" parent="305" name="update_time">
      <Comment>更新时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="442" parent="305" name="remark">
      <Comment>备注</Comment>
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="443" parent="305" name="PRIMARY">
      <ColNames>dict_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="444" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="445" parent="306" name="dict_id">
      <AutoIncrement>99</AutoIncrement>
      <Comment>字典主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="446" parent="306" name="dict_name">
      <Comment>字典名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="447" parent="306" name="dict_type">
      <Comment>字典类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="448" parent="306" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="449" parent="306" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="450" parent="306" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="451" parent="306" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="452" parent="306" name="update_time">
      <Comment>更新时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="453" parent="306" name="remark">
      <Comment>备注</Comment>
      <Position>9</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="454" parent="306" name="PRIMARY">
      <ColNames>dict_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="455" parent="306" name="dict_type">
      <ColNames>dict_type</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="456" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="457" parent="306" name="dict_type">
      <UnderlyingIndexName>dict_type</UnderlyingIndexName>
    </key>
    <column id="458" parent="307" name="job_id">
      <AutoIncrement>100</AutoIncrement>
      <Comment>任务ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="459" parent="307" name="job_name">
      <Comment>任务名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="460" parent="307" name="job_group">
      <Comment>任务组名</Comment>
      <DefaultExpression>&apos;DEFAULT&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="461" parent="307" name="invoke_target">
      <Comment>调用目标字符串</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="462" parent="307" name="cron_expression">
      <Comment>cron执行表达式</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="463" parent="307" name="misfire_policy">
      <Comment>计划执行错误策略（1立即执行 2执行一次 3放弃执行）</Comment>
      <DefaultExpression>&apos;3&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="464" parent="307" name="concurrent">
      <Comment>是否并发执行（0允许 1禁止）</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="465" parent="307" name="status">
      <Comment>状态（0正常 1暂停）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="466" parent="307" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="467" parent="307" name="create_time">
      <Comment>创建时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="468" parent="307" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="469" parent="307" name="update_time">
      <Comment>更新时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="470" parent="307" name="remark">
      <Comment>备注信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="471" parent="307" name="PRIMARY">
      <ColNames>job_id
job_name
job_group</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="472" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="473" parent="308" name="job_log_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>任务日志ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="474" parent="308" name="job_name">
      <Comment>任务名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="475" parent="308" name="job_group">
      <Comment>任务组名</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="476" parent="308" name="invoke_target">
      <Comment>调用目标字符串</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="477" parent="308" name="job_message">
      <Comment>日志信息</Comment>
      <Position>5</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="478" parent="308" name="status">
      <Comment>执行状态（0正常 1失败）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="479" parent="308" name="exception_info">
      <Comment>异常信息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="480" parent="308" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="481" parent="308" name="PRIMARY">
      <ColNames>job_log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="482" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="483" parent="309" name="info_id">
      <AutoIncrement>149</AutoIncrement>
      <Comment>访问ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="484" parent="309" name="login_name">
      <Comment>登录账号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="485" parent="309" name="ipaddr">
      <Comment>登录IP地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="486" parent="309" name="login_location">
      <Comment>登录地点</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="487" parent="309" name="browser">
      <Comment>浏览器类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="488" parent="309" name="os">
      <Comment>操作系统</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="489" parent="309" name="status">
      <Comment>登录状态（0成功 1失败）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="490" parent="309" name="msg">
      <Comment>提示消息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="491" parent="309" name="login_time">
      <Comment>访问时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="492" parent="309" name="PRIMARY">
      <ColNames>info_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="493" parent="309" name="idx_sys_logininfor_s">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="494" parent="309" name="idx_sys_logininfor_lt">
      <ColNames>login_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="495" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="496" parent="310" name="menu_id">
      <AutoIncrement>2004</AutoIncrement>
      <Comment>菜单ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="497" parent="310" name="menu_name">
      <Comment>菜单名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="498" parent="310" name="parent_id">
      <Comment>父菜单ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="499" parent="310" name="order_num">
      <Comment>显示顺序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="500" parent="310" name="url">
      <Comment>请求地址</Comment>
      <DefaultExpression>&apos;#&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="501" parent="310" name="target">
      <Comment>打开方式（menuItem页签 menuBlank新窗口）</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="502" parent="310" name="menu_type">
      <Comment>菜单类型（M目录 C菜单 F按钮）</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="503" parent="310" name="visible">
      <Comment>菜单状态（0显示 1隐藏）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="504" parent="310" name="is_refresh">
      <Comment>是否刷新（0刷新 1不刷新）</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="505" parent="310" name="perms">
      <Comment>权限标识</Comment>
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="506" parent="310" name="icon">
      <Comment>菜单图标</Comment>
      <DefaultExpression>&apos;#&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="507" parent="310" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="508" parent="310" name="create_time">
      <Comment>创建时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="509" parent="310" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="510" parent="310" name="update_time">
      <Comment>更新时间</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="511" parent="310" name="remark">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="512" parent="310" name="PRIMARY">
      <ColNames>menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="513" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="514" parent="311" name="notice_id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>公告ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="515" parent="311" name="notice_title">
      <Comment>公告标题</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="516" parent="311" name="notice_type">
      <Comment>公告类型（1通知 2公告）</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="517" parent="311" name="notice_content">
      <Comment>公告内容</Comment>
      <Position>4</Position>
      <StoredType>longblob|0s</StoredType>
    </column>
    <column id="518" parent="311" name="status">
      <Comment>公告状态（0正常 1关闭）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="519" parent="311" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="520" parent="311" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="521" parent="311" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="522" parent="311" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="523" parent="311" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="524" parent="311" name="PRIMARY">
      <ColNames>notice_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="525" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="526" parent="312" name="oper_id">
      <AutoIncrement>238</AutoIncrement>
      <Comment>日志主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="527" parent="312" name="title">
      <Comment>模块标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="528" parent="312" name="business_type">
      <Comment>业务类型（0其它 1新增 2修改 3删除）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="529" parent="312" name="method">
      <Comment>方法名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="530" parent="312" name="request_method">
      <Comment>请求方式</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="531" parent="312" name="operator_type">
      <Comment>操作类别（0其它 1后台用户 2手机端用户）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="532" parent="312" name="oper_name">
      <Comment>操作人员</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="533" parent="312" name="dept_name">
      <Comment>部门名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="534" parent="312" name="oper_url">
      <Comment>请求URL</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="535" parent="312" name="oper_ip">
      <Comment>主机地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="536" parent="312" name="oper_location">
      <Comment>操作地点</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="537" parent="312" name="oper_param">
      <Comment>请求参数</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="538" parent="312" name="json_result">
      <Comment>返回参数</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="539" parent="312" name="status">
      <Comment>操作状态（0正常 1异常）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="540" parent="312" name="error_msg">
      <Comment>错误消息</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="541" parent="312" name="oper_time">
      <Comment>操作时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="542" parent="312" name="cost_time">
      <Comment>消耗时间</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="543" parent="312" name="PRIMARY">
      <ColNames>oper_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="544" parent="312" name="idx_sys_oper_log_bt">
      <ColNames>business_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="545" parent="312" name="idx_sys_oper_log_s">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="546" parent="312" name="idx_sys_oper_log_ot">
      <ColNames>oper_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="547" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="548" parent="313" name="post_id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>岗位ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="549" parent="313" name="post_code">
      <Comment>岗位编码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="550" parent="313" name="post_name">
      <Comment>岗位名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="551" parent="313" name="post_sort">
      <Comment>显示顺序</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="552" parent="313" name="status">
      <Comment>状态（0正常 1停用）</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="553" parent="313" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="554" parent="313" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="555" parent="313" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="556" parent="313" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="557" parent="313" name="remark">
      <Comment>备注</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="558" parent="313" name="PRIMARY">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="559" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="560" parent="314" name="role_id">
      <AutoIncrement>100</AutoIncrement>
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="561" parent="314" name="role_name">
      <Comment>角色名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="562" parent="314" name="role_key">
      <Comment>角色权限字符串</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="563" parent="314" name="role_sort">
      <Comment>显示顺序</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="564" parent="314" name="data_scope">
      <Comment>数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="565" parent="314" name="status">
      <Comment>角色状态（0正常 1停用）</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="566" parent="314" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="567" parent="314" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="568" parent="314" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="569" parent="314" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="570" parent="314" name="update_time">
      <Comment>更新时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="571" parent="314" name="remark">
      <Comment>备注</Comment>
      <Position>12</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="572" parent="314" name="PRIMARY">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="573" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="574" parent="315" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="575" parent="315" name="dept_id">
      <Comment>部门ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="576" parent="315" name="PRIMARY">
      <ColNames>role_id
dept_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="577" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="578" parent="316" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="579" parent="316" name="menu_id">
      <Comment>菜单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="580" parent="316" name="PRIMARY">
      <ColNames>role_id
menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="581" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="582" parent="317" name="user_id">
      <AutoIncrement>111</AutoIncrement>
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="583" parent="317" name="dept_id">
      <Comment>部门ID</Comment>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="584" parent="317" name="login_name">
      <Comment>登录账号</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="585" parent="317" name="user_name">
      <Comment>用户昵称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="586" parent="317" name="user_type">
      <Comment>用户类型（00系统用户 01注册用户）</Comment>
      <DefaultExpression>&apos;00&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="587" parent="317" name="email">
      <Comment>用户邮箱</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="588" parent="317" name="phonenumber">
      <Comment>手机号码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(11)|0s</StoredType>
    </column>
    <column id="589" parent="317" name="sex">
      <Comment>用户性别（0男 1女 2未知）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="590" parent="317" name="avatar">
      <Comment>头像路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="591" parent="317" name="password">
      <Comment>密码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="592" parent="317" name="salt">
      <Comment>盐加密</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="593" parent="317" name="status">
      <Comment>帐号状态（0正常 1停用）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="594" parent="317" name="del_flag">
      <Comment>删除标志（0代表存在 2代表删除）</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="595" parent="317" name="login_ip">
      <Comment>最后登录IP</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>14</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="596" parent="317" name="login_date">
      <Comment>最后登录时间</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="597" parent="317" name="pwd_update_date">
      <Comment>密码最后更新时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="598" parent="317" name="create_by">
      <Comment>创建者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="599" parent="317" name="create_time">
      <Comment>创建时间</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="600" parent="317" name="update_by">
      <Comment>更新者</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="601" parent="317" name="update_time">
      <Comment>更新时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="602" parent="317" name="remark">
      <Comment>备注</Comment>
      <Position>21</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="603" parent="317" name="PRIMARY">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="604" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="605" parent="318" name="sessionId">
      <Comment>用户会话id</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="606" parent="318" name="login_name">
      <Comment>登录账号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="607" parent="318" name="dept_name">
      <Comment>部门名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="608" parent="318" name="ipaddr">
      <Comment>登录IP地址</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="609" parent="318" name="login_location">
      <Comment>登录地点</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="610" parent="318" name="browser">
      <Comment>浏览器类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="611" parent="318" name="os">
      <Comment>操作系统</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="612" parent="318" name="status">
      <Comment>在线状态on_line在线off_line离线</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="613" parent="318" name="start_timestamp">
      <Comment>session创建时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="614" parent="318" name="last_access_time">
      <Comment>session最后访问时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="615" parent="318" name="expire_time">
      <Comment>超时时间，单位为分钟</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="616" parent="318" name="PRIMARY">
      <ColNames>sessionId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="617" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="618" parent="319" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="619" parent="319" name="post_id">
      <Comment>岗位ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="620" parent="319" name="PRIMARY">
      <ColNames>user_id
post_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="621" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="622" parent="320" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="623" parent="320" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <index id="624" parent="320" name="PRIMARY">
      <ColNames>user_id
role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="625" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="626" parent="321" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="627" parent="321" name="trailing_profit_id">
      <Comment>策略id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="628" parent="321" name="price_gain">
      <Comment>% 涨幅</Comment>
      <DefaultExpression>0.000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>float(6,3 digit) unsigned|0s</StoredType>
    </column>
    <column id="629" parent="321" name="trigger_price">
      <Comment>涨幅对应的具体价格</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <Position>4</Position>
      <StoredType>decimal(36,18 digit)|0s</StoredType>
    </column>
    <column id="630" parent="321" name="take_profit">
      <Comment>% 距最高点下跌幅度 - 止盈</Comment>
      <DefaultExpression>0.000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>float(6,3 digit) unsigned|0s</StoredType>
    </column>
    <column id="631" parent="321" name="state">
      <Comment>0: 不启用， 1：进行中，2：完成</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="632" parent="321" name="type">
      <Comment>0:止盈，1:止损</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="633" parent="321" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="634" parent="321" name="update_time">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="635" parent="321" name="message">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="636" parent="321" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="637" parent="321" name="trailing_profit_id">
      <ColNames>trailing_profit_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="638" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="639" parent="322" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="640" parent="322" name="user_id">
      <Comment>userId</Comment>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="641" parent="322" name="parent_id">
      <Comment>parent id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="642" parent="322" name="group_name">
      <Comment>名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="643" parent="322" name="profit_state">
      <Comment>0:亏损，1:盈利</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="644" parent="322" name="state">
      <Comment>0: 不启用， 1：进行中，2：完成并复制新的策略组，3：完成但有账户余额不足-停，4：完成但连续亏3次-停,5:手动停止</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="645" parent="322" name="loss_times">
      <Comment>连续亏损次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="646" parent="322" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="647" parent="322" name="update_time">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="648" parent="322" name="message">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="649" parent="322" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="650" parent="322" name="parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="651" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="652" parent="323" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="653" parent="323" name="user_id">
      <Comment>userId</Comment>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="654" parent="323" name="parent_id">
      <Comment>parent id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="655" parent="323" name="group_name">
      <Comment>名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="656" parent="323" name="profit_state">
      <Comment>0:亏损，1:盈利</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="657" parent="323" name="state">
      <Comment>0: 不启用， 1：进行中，2：完成并复制新的策略组，3：完成但有账户余额不足-停，4：完成但连续亏3次-停,5:手动停止</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="658" parent="323" name="loss_times">
      <Comment>连续亏损次数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="659" parent="323" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="660" parent="323" name="update_time">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="661" parent="323" name="message">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="662" parent="323" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="663" parent="323" name="parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="664" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="665" parent="324" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="666" parent="324" name="group_id">
      <Comment>策略组id</Comment>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="667" parent="324" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="668" parent="324" name="user_name">
      <Comment>用户名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="669" parent="324" name="account_name">
      <Comment>交易账户名</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="670" parent="324" name="platform">
      <Comment>平台</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
      <CollationName>utf8mb3_bin</CollationName>
    </column>
    <column id="671" parent="324" name="symbol">
      <Comment>交易所对应币对名</Comment>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
      <CollationName>utf8mb3_bin</CollationName>
    </column>
    <column id="672" parent="324" name="open_price">
      <Comment>建仓价</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>decimal(36,18 digit) unsigned|0s</StoredType>
    </column>
    <column id="673" parent="324" name="market_price">
      <Comment>当前的市场价</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <Position>9</Position>
      <StoredType>decimal(36,18 digit)|0s</StoredType>
    </column>
    <column id="674" parent="324" name="close_price">
      <Comment>平仓价</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <Position>10</Position>
      <StoredType>decimal(36,18 digit)|0s</StoredType>
    </column>
    <column id="675" parent="324" name="stop_loss_price">
      <Comment>止损价</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>decimal(36,18 digit) unsigned|0s</StoredType>
    </column>
    <column id="676" parent="324" name="stop_loss_rate">
      <Comment>止损比例</Comment>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>12</Position>
      <StoredType>float(6,4 digit)|0s</StoredType>
    </column>
    <column id="677" parent="324" name="highest_price">
      <Comment>策略启动以来最高收益对应的价格</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>decimal(36,18 digit) unsigned|0s</StoredType>
    </column>
    <column id="678" parent="324" name="lowest_price">
      <Comment>策略启动以来最低价格</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <Position>14</Position>
      <StoredType>decimal(36,18 digit)|0s</StoredType>
    </column>
    <column id="679" parent="324" name="amount">
      <Comment>交易数量</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>decimal(36,18 digit) unsigned|0s</StoredType>
    </column>
    <column id="680" parent="324" name="profit_value">
      <Comment>收益值</Comment>
      <DefaultExpression>0.000000000000000000</DefaultExpression>
      <Position>16</Position>
      <StoredType>decimal(36,18 digit)|0s</StoredType>
    </column>
    <column id="681" parent="324" name="profit_rate">
      <Comment>收益率</Comment>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>17</Position>
      <StoredType>float(8,2 digit)|0s</StoredType>
    </column>
    <column id="682" parent="324" name="inst_type">
      <Comment>金融类型：futures, spot</Comment>
      <Position>18</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="683" parent="324" name="leverage">
      <Comment>杠杆位数</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>19</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="684" parent="324" name="position_side">
      <Comment>订单方向：多-long, 空-short</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>20</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="685" parent="324" name="order_type">
      <Comment>订单类型： MARKET, LIMIT</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>21</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="686" parent="324" name="trade_mode">
      <Comment>交易模式： 全仓-CROSSED, 逐仓-ISOLATED</Comment>
      <DefaultExpression>&apos;ISOLATED&apos;</DefaultExpression>
      <Position>22</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="687" parent="324" name="fee">
      <Comment>fee</Comment>
      <DefaultExpression>0.00000000</DefaultExpression>
      <Position>23</Position>
      <StoredType>double(16,8 digit)|0s</StoredType>
    </column>
    <column id="688" parent="324" name="ref_order_open">
      <Comment>来源订单开仓id</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>24</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="689" parent="324" name="ref_order_close">
      <Comment>来源订单平仓id</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>25</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="690" parent="324" name="state">
      <Comment>0: 不启用， 1：进行中，2：完成</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>26</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="691" parent="324" name="strategy_type">
      <Comment>0:普通策略， 1:跟单策略</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>27</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="692" parent="324" name="follow_content">
      <Comment>跟单策略的具体内容(json)</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>28</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="693" parent="324" name="fresh">
      <Comment>订单结束后，是否刷新。0-未刷新，1-刷新</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>29</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="694" parent="324" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>30</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="695" parent="324" name="update_time">
      <Comment>修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>31</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="696" parent="324" name="message">
      <Comment>备注</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>32</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="697" parent="324" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="698" parent="324" name="user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="699" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>
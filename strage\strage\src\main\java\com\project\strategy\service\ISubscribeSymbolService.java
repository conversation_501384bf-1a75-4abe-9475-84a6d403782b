package com.project.strategy.service;

import com.project.strategy.domain.entity.SubscribeSymbol;

import java.util.List;

public interface ISubscribeSymbolService {

    public SubscribeSymbol selectSubscribeSymbolById(Long id);

    public List<SubscribeSymbol> selectSubscribeSymbolList(SubscribeSymbol subscribeSymbol);

    public int insertSubscribeSymbol(SubscribeSymbol subscribeSymbol);

    public int updateSubscribeSymbol(SubscribeSymbol subscribeSymbol);

    public int deleteSubscribeSymbolByIds(String ids);

    public int deleteSubscribeSymbolById(Long id);
}

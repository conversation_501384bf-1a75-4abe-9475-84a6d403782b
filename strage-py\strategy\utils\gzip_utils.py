"""
GZIP压缩解压工具类
迁移自: com.project.strategy.utils.GZIPUtils
"""
import gzip
import io
import logging
from typing import Optional


logger = logging.getLogger(__name__)


class GZIPUtils:
    """GZIP压缩解压工具类"""
    
    GZIP_ENCODE_UTF_8 = "UTF-8"
    GZIP_ENCODE_ISO_8859_1 = "ISO-8859-1"
    
    @staticmethod
    def compress(content: str, encoding: str = None) -> Optional[bytes]:
        """
        压缩字符串
        
        Args:
            content: 需要压缩的字符串
            encoding: 字符编码，默认为UTF-8
            
        Returns:
            压缩后的字节数组，失败返回None
        """
        if not content:
            return None
        
        if encoding is None:
            encoding = GZIPUtils.GZIP_ENCODE_UTF_8
        
        try:
            # 将字符串转换为字节
            content_bytes = content.encode(encoding)
            
            # 使用gzip压缩
            compressed = gzip.compress(content_bytes)
            
            return compressed
            
        except Exception as e:
            logger.error(f"GZIP压缩失败: {e}")
            return None
    
    @staticmethod
    def uncompress(data: bytes) -> Optional[bytes]:
        """
        解压字节数组
        
        Args:
            data: 需要解压的字节数组
            
        Returns:
            解压后的字节数组，失败返回None
        """
        if not data:
            return None
        
        try:
            # 使用gzip解压
            uncompressed = gzip.decompress(data)
            
            return uncompressed
            
        except Exception as e:
            logger.error(f"GZIP解压失败: {e}")
            return None
    
    @staticmethod
    def uncompress_to_string(data: bytes, encoding: str = None) -> Optional[str]:
        """
        解压字节数组为字符串
        
        Args:
            data: 需要解压的字节数组
            encoding: 字符编码，默认为UTF-8
            
        Returns:
            解压后的字符串，失败返回None
        """
        if not data:
            return None
        
        if encoding is None:
            encoding = GZIPUtils.GZIP_ENCODE_UTF_8
        
        try:
            # 解压字节数组
            uncompressed_bytes = GZIPUtils.uncompress(data)
            
            if uncompressed_bytes is None:
                return None
            
            # 转换为字符串
            return uncompressed_bytes.decode(encoding)
            
        except Exception as e:
            logger.error(f"GZIP解压为字符串失败: {e}")
            return None
    
    @staticmethod
    def compress_string(content: str) -> Optional[bytes]:
        """
        压缩字符串（使用UTF-8编码）
        
        Args:
            content: 需要压缩的字符串
            
        Returns:
            压缩后的字节数组，失败返回None
        """
        return GZIPUtils.compress(content, GZIPUtils.GZIP_ENCODE_UTF_8)
    
    @staticmethod
    def uncompress_string(data: bytes) -> Optional[str]:
        """
        解压字节数组为字符串（使用UTF-8编码）
        
        Args:
            data: 需要解压的字节数组
            
        Returns:
            解压后的字符串，失败返回None
        """
        return GZIPUtils.uncompress_to_string(data, GZIPUtils.GZIP_ENCODE_UTF_8)
    
    @staticmethod
    def get_compression_ratio(original: str, compressed: bytes) -> float:
        """
        获取压缩比率
        
        Args:
            original: 原始字符串
            compressed: 压缩后的字节数组
            
        Returns:
            压缩比率（0-1之间，越小压缩效果越好）
        """
        if not original or not compressed:
            return 1.0
        
        try:
            original_size = len(original.encode('utf-8'))
            compressed_size = len(compressed)
            
            return compressed_size / original_size
            
        except Exception as e:
            logger.error(f"计算压缩比率失败: {e}")
            return 1.0
    
    @staticmethod
    def is_gzip_data(data: bytes) -> bool:
        """
        检查字节数组是否为GZIP格式
        
        Args:
            data: 字节数组
            
        Returns:
            是否为GZIP格式
        """
        if not data or len(data) < 2:
            return False
        
        # GZIP文件的魔数是0x1f, 0x8b
        return data[0] == 0x1f and data[1] == 0x8b


# 测试函数
def main():
    """测试GZIP压缩解压功能"""
    test_string = "这是一个测试字符串，用于验证GZIP压缩和解压功能。" * 10
    
    print(f"原始字符串长度: {len(test_string)}")
    print(f"原始字符串: {test_string[:50]}...")
    
    # 压缩
    compressed = GZIPUtils.compress_string(test_string)
    if compressed:
        print(f"压缩后长度: {len(compressed)}")
        print(f"压缩比率: {GZIPUtils.get_compression_ratio(test_string, compressed):.2%}")
        print(f"是否为GZIP格式: {GZIPUtils.is_gzip_data(compressed)}")
        
        # 解压
        decompressed = GZIPUtils.uncompress_string(compressed)
        if decompressed:
            print(f"解压后长度: {len(decompressed)}")
            print(f"解压成功: {test_string == decompressed}")
        else:
            print("解压失败")
    else:
        print("压缩失败")


if __name__ == "__main__":
    main()

/* 若依UI样式 */
.navbar-minimalize {
    float: left;
    margin-left: 10px;
    margin-right: 10px;
    padding: 15px 10px;
    color: #fff;
}

.navbar-minimalize:hover {
    color: #fff;
    text-decoration: none;
}

.user-image {
    width: 25px;
    height: 25px;
    border-radius: 50%;
}

.menuItem {
    color: #676a6c;
    text-decoration: none;
}

.menuItem:hover {
    color: #1ab394;
    text-decoration: none;
}

.noactive {
    color: #676a6c !important;
}

.content-tabs {
    border-bottom: solid 2px #e7eaec;
    background: #fafafa;
    padding: 0;
}

.page-tabs {
    float: left;
    width: calc(100% - 120px);
    overflow: hidden;
}

.page-tabs-content {
    float: left;
    width: 99999px;
}

.menuTab {
    background: #fafafa;
    border: solid 1px #e7eaec;
    border-bottom: none;
    color: #676a6c;
    display: inline-block;
    font-size: 12px;
    line-height: 18px;
    margin-right: 5px;
    padding: 8px 25px 8px 15px;
    position: relative;
    text-decoration: none;
}

.menuTab.active {
    background: #fff;
    color: #555;
}

.roll-nav {
    background: #fafafa;
    border: solid 1px #e7eaec;
    border-bottom: none;
    color: #999;
    display: block;
    float: left;
    font-size: 12px;
    height: 36px;
    line-height: 24px;
    text-align: center;
    text-decoration: none;
    width: 30px;
    padding: 6px 0;
}

.roll-nav:hover {
    color: #777;
    text-decoration: none;
}

.tabLeft {
    border-right: none;
}

.tabRight {
    border-left: none;
}

.tabReload {
    border-left: none;
    width: 60px;
}

.ax_close_max {
    position: fixed;
    right: 15px;
    top: 15px;
    z-index: 99999;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 5px 10px;
    border-radius: 3px;
    display: none;
}

.mainContent {
    margin: 0;
    padding: 0;
    position: absolute;
    top: 86px;
    left: 220px;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.RuoYi_iframe {
    width: 100%;
    height: 100%;
    border: 0;
    margin: 0;
    padding: 0;
}

.footer {
    background: #fff;
    border-top: 1px solid #e7eaec;
    overflow: hidden;
    padding: 10px 20px;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 1000;
}

/* 皮肤样式 */
.skin-blue .navbar-default {
    background-color: rgb(47, 64, 80);
}

.skin-blue .logo {
    background-color: #367fa9;
}

.sidebar-mini .navbar-static-side {
    width: 220px;
}

.fixed-sidebar .navbar-static-side {
    position: fixed;
    width: 220px;
    z-index: 2001;
    height: 100%;
}

.body-small .navbar-static-side {
    width: 70px;
}

.mini-navbar .navbar-static-side {
    width: 70px;
}

.canvas-menu .navbar-static-side {
    position: fixed;
    z-index: 2001;
    height: 100%;
}

/* 响应式 */
@media (max-width: 768px) {
    .navbar-static-side {
        width: 70px;
    }

    .page-tabs {
        width: calc(100% - 60px);
    }

    .mainContent {
        top: 50px;
        left: 70px;
    }
}

/* 菜单收缩状态 */
.mini-navbar .mainContent {
    left: 70px;
}

/* 正常状态确保主内容区域在导航栏右侧 */
.fixed-sidebar .mainContent {
    left: 220px;
}
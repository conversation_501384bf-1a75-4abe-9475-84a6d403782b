<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户余额</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-account-asset">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">账户名：</label>
                    <div class="col-sm-8">
                        <input name="accountName" readonly="true" value="{{ accountBalance.account_name or '' }}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">现货账户：</label>
                    <div class="col-sm-8">
                        <input name="spotBalance" readonly="true" value="{{ accountBalance.spot_balance or '0.00' }}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">合约账户：</label>
                    <div class="col-sm-8">
                        <input name="futuresUBalance" readonly="true" value="{{ accountBalance.futures_u_balance or '0.00' }}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">划转类型：</label>
                    <div class="col-sm-8">
                        <select name="transferType" class="form-control" width="200">
                            <option value="MAIN_UMFUTURE">从现货转到U本位合约</option>
                            <option value="UMFUTURE_MAIN">从U本位合约转到现货</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">划转金额U：</label>
                    <div class="col-sm-8">
                        <input name="transferAmount" class="form-control" type="text" placeholder="请输入划转金额">
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="/static/js/common.js"></script>
    
    <script>
        var prefix = "/strategy/account";
        
        $("#form-account-asset").validate({
            focusCleanup: true,
            rules: {
                transferAmount: {
                    required: true,
                    number: true,
                    min: 0.01
                }
            },
            messages: {
                transferAmount: {
                    required: "请输入划转金额",
                    number: "请输入有效的数字",
                    min: "划转金额必须大于0.01"
                }
            }
        });

        function submitHandler() {
            if ($("#form-account-asset").valid()) {
                $.ajax({
                    url: prefix + "/asset",
                    type: "POST",
                    data: $('#form-account-asset').serialize(),
                    success: function (result) {
                        if (result.code == 0) {
                            alert('划转成功');
                            // 刷新余额显示
                            location.reload();
                        } else {
                            alert(result.msg || '划转失败');
                        }
                    },
                    error: function () {
                        alert('划转失败');
                    }
                });
            }
        }
    </script>
</body>
</html>

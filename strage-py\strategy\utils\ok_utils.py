"""
OKX交易所工具类
迁移自: com.project.strategy.utils.OkUtils
"""
import hmac
import hashlib
import base64
import logging
from typing import Dict, List, Optional
from urllib.parse import urlencode


logger = logging.getLogger(__name__)


class OkUtils:
    """OKX交易所工具类"""
    
    HMAC_SHA256 = "sha256"
    
    @staticmethod
    def sign(method: str, request_path: str, body: str, timestamp: str, secret_key: str) -> str:
        """
        生成OKX API签名
        
        Args:
            method: HTTP方法
            request_path: 请求路径
            body: 请求体
            timestamp: 时间戳
            secret_key: 密钥
            
        Returns:
            签名字符串
        """
        if not secret_key or not request_path:
            return ""
        
        try:
            # 构建预签名字符串
            pre_hash = []
            pre_hash.append(timestamp)
            pre_hash.append(method.upper())
            pre_hash.append(request_path)
            
            if body:
                pre_hash.append(body)
            
            pre_hash_str = ''.join(pre_hash)
            
            # 使用HMAC-SHA256生成签名
            secret_key_bytes = secret_key.encode('utf-8')
            pre_hash_bytes = pre_hash_str.encode('utf-8')
            
            signature = hmac.new(
                secret_key_bytes,
                pre_hash_bytes,
                hashlib.sha256
            ).digest()
            
            # Base64编码
            return base64.b64encode(signature).decode('utf-8')
            
        except Exception as e:
            logger.error(f"生成签名失败: {e}")
            return ""
    
    @staticmethod
    def to_request_path_with_list(path: str, key: str, values: List[str]) -> str:
        """
        构建带列表参数的请求路径
        
        Args:
            path: 基础路径
            key: 参数键
            values: 参数值列表
            
        Returns:
            完整的请求路径
        """
        if not key or not values:
            return path
        
        # 将列表值用逗号连接
        value_str = ','.join(values)
        return f"{path}?{key}={value_str}"
    
    @staticmethod
    def to_request_path(path: str, params: Optional[Dict[str, str]] = None) -> str:
        """
        构建带参数的请求路径
        
        Args:
            path: 基础路径
            params: 参数字典
            
        Returns:
            完整的请求路径
        """
        if not params:
            return path
        
        try:
            # 构建查询字符串
            query_parts = []
            for key, value in params.items():
                if value is not None:
                    query_parts.append(f"{key}={value}")
            
            if not query_parts:
                return path
            
            query_string = '&'.join(query_parts)
            return f"{path}?{query_string}"
            
        except Exception as e:
            logger.error(f"构建请求路径失败: {e}")
            return path
    
    @staticmethod
    def to_request_path_encoded(path: str, params: Optional[Dict[str, str]] = None) -> str:
        """
        构建带URL编码参数的请求路径
        
        Args:
            path: 基础路径
            params: 参数字典
            
        Returns:
            完整的请求路径（参数已URL编码）
        """
        if not params:
            return path
        
        try:
            # 使用urllib.parse.urlencode进行URL编码
            query_string = urlencode(params)
            return f"{path}?{query_string}"
            
        except Exception as e:
            logger.error(f"构建编码请求路径失败: {e}")
            return path
    
    @staticmethod
    def validate_signature_params(method: str, request_path: str, timestamp: str, secret_key: str) -> bool:
        """
        验证签名参数是否有效
        
        Args:
            method: HTTP方法
            request_path: 请求路径
            timestamp: 时间戳
            secret_key: 密钥
            
        Returns:
            参数是否有效
        """
        return (
            bool(method and method.strip()) and
            bool(request_path and request_path.strip()) and
            bool(timestamp and timestamp.strip()) and
            bool(secret_key and secret_key.strip())
        )
    
    @staticmethod
    def get_signature_string(method: str, request_path: str, body: str, timestamp: str) -> str:
        """
        获取签名字符串（用于调试）
        
        Args:
            method: HTTP方法
            request_path: 请求路径
            body: 请求体
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        pre_hash = []
        pre_hash.append(timestamp)
        pre_hash.append(method.upper())
        pre_hash.append(request_path)
        
        if body:
            pre_hash.append(body)
        
        return ''.join(pre_hash)
    
    @staticmethod
    def parse_query_params(url: str) -> Dict[str, str]:
        """
        解析URL中的查询参数
        
        Args:
            url: 完整URL
            
        Returns:
            参数字典
        """
        try:
            if '?' not in url:
                return {}
            
            query_string = url.split('?', 1)[1]
            params = {}
            
            for param in query_string.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    params[key] = value
                else:
                    params[param] = ''
            
            return params
            
        except Exception as e:
            logger.error(f"解析查询参数失败: {e}")
            return {}
    
    @staticmethod
    def remove_empty_params(params: Dict[str, str]) -> Dict[str, str]:
        """
        移除空参数
        
        Args:
            params: 参数字典
            
        Returns:
            过滤后的参数字典
        """
        return {k: v for k, v in params.items() if v is not None and v != ''}


# 测试函数
def main():
    """测试OkUtils功能"""
    # 测试签名生成
    method = "GET"
    request_path = "/api/v5/account/balance"
    body = ""
    timestamp = "2024-01-01T00:00:00.000Z"
    secret_key = "test_secret_key"
    
    signature = OkUtils.sign(method, request_path, body, timestamp, secret_key)
    print(f"生成的签名: {signature}")
    
    # 测试路径构建
    params = {
        "instType": "SPOT",
        "instId": "BTC-USDT"
    }
    
    path_with_params = OkUtils.to_request_path("/api/v5/market/ticker", params)
    print(f"带参数的路径: {path_with_params}")
    
    # 测试列表参数
    values = ["BTC-USDT", "ETH-USDT", "LTC-USDT"]
    path_with_list = OkUtils.to_request_path_with_list("/api/v5/market/tickers", "instId", values)
    print(f"带列表参数的路径: {path_with_list}")
    
    # 测试签名字符串生成
    signature_string = OkUtils.get_signature_string(method, request_path, body, timestamp)
    print(f"签名字符串: {signature_string}")


if __name__ == "__main__":
    main()

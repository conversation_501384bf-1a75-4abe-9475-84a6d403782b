"""
跟踪详情实体类
迁移自: com.project.strategy.domain.entity.TrailingDetail
"""
from dataclasses import dataclass
from typing import Optional
from decimal import Decimal
from datetime import datetime


@dataclass
class TrailingDetail:
    """跟踪详情实体"""
    
    id: Optional[int] = None
    trailing_profit_id: Optional[int] = None  # 策略id
    price_gain: Optional[float] = None  # % 涨幅
    trigger_price: Optional[Decimal] = None  # 触发价格
    take_profit: Optional[float] = None  # % 距最高点下跌幅度 - 止盈
    state: Optional[int] = None  # 0: 不启用， 1：进行中，2：完成
    type: Optional[int] = None  # 类型
    message: Optional[str] = None  # 备注
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        int_fields = ['id', 'trailing_profit_id', 'state', 'type']
        for field_name in int_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, int):
                try:
                    setattr(self, field_name, int(value))
                except (ValueError, TypeError):
                    setattr(self, field_name, None)
        
        # 处理浮点数字段
        float_fields = ['price_gain', 'take_profit']
        for field_name in float_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, float):
                try:
                    setattr(self, field_name, float(value))
                except (ValueError, TypeError):
                    setattr(self, field_name, None)
        
        # 处理Decimal字段
        if self.trigger_price is not None and not isinstance(self.trigger_price, Decimal):
            try:
                self.trigger_price = Decimal(str(self.trigger_price))
            except (ValueError, TypeError):
                self.trigger_price = None
    
    def is_active(self) -> bool:
        """检查是否进行中"""
        return self.state == 1
    
    def is_completed(self) -> bool:
        """检查是否已完成"""
        return self.state == 2
    
    def is_disabled(self) -> bool:
        """检查是否未启用"""
        return self.state == 0
    
    def is_valid(self) -> bool:
        """检查详情信息是否有效"""
        return (
            self.trailing_profit_id is not None and
            self.price_gain is not None and
            self.take_profit is not None and
            self.price_gain > 0 and
            self.take_profit > 0
        )
    
    def should_trigger(self, current_price: Decimal, highest_price: Decimal) -> bool:
        """
        检查是否应该触发止盈
        
        Args:
            current_price: 当前价格
            highest_price: 最高价格
            
        Returns:
            是否应该触发
        """
        if (not self.is_active() or 
            current_price is None or 
            highest_price is None or 
            self.take_profit is None or
            highest_price == 0):
            return False
        
        # 计算从最高点下跌的百分比
        decline_rate = float((highest_price - current_price) / highest_price * 100)
        
        return decline_rate >= self.take_profit
    
    @classmethod
    def builder(cls) -> 'TrailingDetailBuilder':
        """创建构建器"""
        return TrailingDetailBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, Decimal):
                    result[key] = str(value)
                elif isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TrailingDetail':
        """从字典创建实例"""
        # 处理datetime字段
        if 'create_time' in data and data['create_time']:
            if isinstance(data['create_time'], str):
                try:
                    data['create_time'] = datetime.fromisoformat(data['create_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['create_time'] = None
        
        if 'update_time' in data and data['update_time']:
            if isinstance(data['update_time'], str):
                try:
                    data['update_time'] = datetime.fromisoformat(data['update_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['update_time'] = None
        
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"TrailingDetail(id={self.id}, trailing_profit_id={self.trailing_profit_id}, "
                f"price_gain={self.price_gain}%, take_profit={self.take_profit}%, state={self.state})")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"TrailingDetail(id={self.id}, trailing_profit_id={self.trailing_profit_id}, "
                f"price_gain={self.price_gain}, trigger_price={self.trigger_price}, "
                f"take_profit={self.take_profit}, state={self.state}, type={self.type})")


class TrailingDetailBuilder:
    """TrailingDetail构建器"""
    
    def __init__(self):
        self._data = {}
    
    def id(self, id: int) -> 'TrailingDetailBuilder':
        self._data['id'] = id
        return self
    
    def trailing_profit_id(self, trailing_profit_id: int) -> 'TrailingDetailBuilder':
        self._data['trailing_profit_id'] = trailing_profit_id
        return self
    
    def price_gain(self, price_gain: float) -> 'TrailingDetailBuilder':
        self._data['price_gain'] = price_gain
        return self
    
    def trigger_price(self, trigger_price: Decimal) -> 'TrailingDetailBuilder':
        self._data['trigger_price'] = trigger_price
        return self
    
    def take_profit(self, take_profit: float) -> 'TrailingDetailBuilder':
        self._data['take_profit'] = take_profit
        return self
    
    def state(self, state: int) -> 'TrailingDetailBuilder':
        self._data['state'] = state
        return self
    
    def type(self, type: int) -> 'TrailingDetailBuilder':
        self._data['type'] = type
        return self
    
    def message(self, message: str) -> 'TrailingDetailBuilder':
        self._data['message'] = message
        return self
    
    def create_time(self, create_time: datetime) -> 'TrailingDetailBuilder':
        self._data['create_time'] = create_time
        return self
    
    def update_time(self, update_time: datetime) -> 'TrailingDetailBuilder':
        self._data['update_time'] = update_time
        return self
    
    def build(self) -> TrailingDetail:
        """构建TrailingDetail实例"""
        return TrailingDetail(**self._data)

"""
订单簿平台领域模型
迁移自: com.project.strategy.domain.OrderBookPlatform
"""
from dataclasses import dataclass
from typing import Optional
from .order_book_item import OrderBookItem


@dataclass
class OrderBookPlatform:
    """订单簿平台"""
    
    binance: Optional[OrderBookItem] = None
    fameex: Optional[OrderBookItem] = None
    
    def can_long(self, open_diff: float) -> bool:
        """
        判断是否可以做多
        
        Args:
            open_diff: 开仓价差阈值
            
        Returns:
            是否可以做多
        """
        if not self.binance or not self.fameex:
            return False
            
        fameex_ask_px = self.fameex.get_ask_px_double()
        binance_ask_px = self.binance.get_ask_px_double()
        
        if fameex_ask_px is None or binance_ask_px is None or fameex_ask_px == 0:
            return False
            
        price_diff_rate = (binance_ask_px - fameex_ask_px) / fameex_ask_px
        return price_diff_rate >= open_diff
    
    def can_short(self, open_diff: float) -> bool:
        """
        判断是否可以做空
        
        Args:
            open_diff: 开仓价差阈值
            
        Returns:
            是否可以做空
        """
        if not self.binance or not self.fameex:
            return False
            
        binance_bid_px = self.binance.get_bid_px_double()
        fameex_bid_px = self.fameex.get_bid_px_double()
        
        if binance_bid_px is None or fameex_bid_px is None or binance_bid_px == 0:
            return False
            
        price_diff_rate = (fameex_bid_px - binance_bid_px) / binance_bid_px
        return price_diff_rate >= open_diff
    
    def can_close_long(self, close_diff: float) -> bool:
        """
        判断是否可以平多仓
        
        Args:
            close_diff: 平仓价差阈值
            
        Returns:
            是否可以平多仓
        """
        if not self.binance or not self.fameex:
            return False
            
        fameex_bid_px = self.fameex.get_bid_px_double()
        binance_bid_px = self.binance.get_bid_px_double()
        
        if fameex_bid_px is None or binance_bid_px is None or fameex_bid_px == 0:
            return False
            
        price_diff_rate = (binance_bid_px - fameex_bid_px) / fameex_bid_px
        return price_diff_rate <= close_diff
    
    def can_close_short(self, close_diff: float) -> bool:
        """
        判断是否可以平空仓
        
        Args:
            close_diff: 平仓价差阈值
            
        Returns:
            是否可以平空仓
        """
        if not self.binance or not self.fameex:
            return False
            
        binance_ask_px = self.binance.get_ask_px_double()
        fameex_ask_px = self.fameex.get_ask_px_double()
        
        if binance_ask_px is None or fameex_ask_px is None or binance_ask_px == 0:
            return False
            
        price_diff_rate = (fameex_ask_px - binance_ask_px) / binance_ask_px
        return price_diff_rate <= close_diff
    
    def get_long_profit_rate(self) -> Optional[float]:
        """
        获取做多盈利率
        
        Returns:
            做多盈利率（百分比）
        """
        if not self.binance or not self.fameex:
            return None
            
        fameex_ask_px = self.fameex.get_ask_px_double()
        binance_bid_px = self.binance.get_bid_px_double()
        
        if fameex_ask_px is None or binance_bid_px is None or fameex_ask_px == 0:
            return None
            
        return ((binance_bid_px - fameex_ask_px) / fameex_ask_px) * 100
    
    def get_short_profit_rate(self) -> Optional[float]:
        """
        获取做空盈利率
        
        Returns:
            做空盈利率（百分比）
        """
        if not self.binance or not self.fameex:
            return None
            
        binance_ask_px = self.binance.get_ask_px_double()
        fameex_bid_px = self.fameex.get_bid_px_double()
        
        if binance_ask_px is None or fameex_bid_px is None or binance_ask_px == 0:
            return None
            
        return ((fameex_bid_px - binance_ask_px) / binance_ask_px) * 100
    
    def is_valid(self) -> bool:
        """检查平台数据是否有效"""
        return (
            self.binance is not None and 
            self.fameex is not None and
            self.binance.is_valid() and 
            self.fameex.is_valid()
        )
    
    @classmethod
    def builder(cls) -> 'OrderBookPlatformBuilder':
        """创建构建器"""
        return OrderBookPlatformBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'binance': self.binance.to_dict() if self.binance else None,
            'fameex': self.fameex.to_dict() if self.fameex else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'OrderBookPlatform':
        """从字典创建实例"""
        binance = None
        fameex = None
        
        if data.get('binance'):
            binance = OrderBookItem.from_dict(data['binance'])
        if data.get('fameex'):
            fameex = OrderBookItem.from_dict(data['fameex'])
            
        return cls(binance=binance, fameex=fameex)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"OrderBookPlatform(binance={self.binance}, fameex={self.fameex})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"OrderBookPlatform(binance={repr(self.binance)}, fameex={repr(self.fameex)})"


class OrderBookPlatformBuilder:
    """OrderBookPlatform构建器"""
    
    def __init__(self):
        self._data = {}
    
    def binance(self, binance: OrderBookItem) -> 'OrderBookPlatformBuilder':
        self._data['binance'] = binance
        return self
    
    def fameex(self, fameex: OrderBookItem) -> 'OrderBookPlatformBuilder':
        self._data['fameex'] = fameex
        return self
    
    def build(self) -> OrderBookPlatform:
        """构建OrderBookPlatform实例"""
        return OrderBookPlatform(**self._data)

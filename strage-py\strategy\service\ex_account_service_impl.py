"""
交易所账户服务实现类
迁移自: com.project.strategy.service.impl.ExAccountServiceImpl
"""
import logging
from datetime import datetime
from typing import List, Optional

from .account_bean_factory import AccountBeanFactory
from .i_ex_account_service import IExAccountService
from ..dao.ex_account_dao import ExAccountDAO
from ..domain.entity.ex_account import ExAccount

logger = logging.getLogger(__name__)


class ExAccountServiceImpl(IExAccountService):
    """交易所账户服务实现类"""
    
    def __init__(self, account_bean_factory: Optional[AccountBeanFactory] = None, aes_key: str = ""):
        """
        初始化交易所账户服务

        Args:
            account_bean_factory: 账户Bean工厂
            aes_key: AES加密密钥
        """
        self.ex_account_dao = ExAccountDAO()
        self.account_bean_factory = account_bean_factory
        self.aes_key = aes_key
        logger.info("ExAccountServiceImpl initialized")
    
    def select_ex_account_by_id(self, id: int) -> Optional[ExAccount]:
        """
        根据ID查询交易所账户
        对应Java中的selectExAccountById方法

        Args:
            id: 账户ID

        Returns:
            交易所账户对象，不存在返回None
        """
        return self.ex_account_dao.select_ex_account_by_id(id)
    
    def select_ex_account_list(self, ex_account: Optional[ExAccount] = None) -> List[ExAccount]:
        """
        查询交易所账户列表
        对应Java中的selectExAccountList方法

        Args:
            ex_account: 查询条件对象

        Returns:
            交易所账户列表
        """
        ex_account.state = 1
        return self.ex_account_dao.select_ex_account_list(ex_account)
    
    def insert_ex_account(self, ex_account: ExAccount) -> int:
        """
        插入交易所账户
        对应Java中的insertExAccount方法

        Args:
            ex_account: 交易所账户对象

        Returns:
            插入的记录数量
        """
        ex_account.create_time = datetime.now()
        # 注释掉的加密逻辑，根据需要可以启用
        # api_key = ex_account.apikey
        # secret_key = ex_account.secret_key
        # new_aes_key = self.aes_key + api_key[:7]
        # encrypt_str = AESUtil.encrypt(secret_key, new_aes_key)
        # ex_account.secret_key = encrypt_str
        # ex_account.secret_msg = api_key[:14]

        i = self.ex_account_dao.insert_ex_account(ex_account)

        if self.account_bean_factory:
            self.account_bean_factory.add_account_service(ex_account)

        return i
    
    def update_ex_account(self, ex_account: ExAccount) -> int:
        """
        更新交易所账户
        对应Java中的updateExAccount方法

        Args:
            ex_account: 交易所账户对象

        Returns:
            更新的记录数量
        """
        ex_account.update_time = datetime.now()
        return self.ex_account_dao.update_ex_account(ex_account)
    
    def delete_ex_account_by_ids(self, ids: str) -> int:
        """
        根据ID列表批量删除交易所账户
        对应Java中的deleteExAccountByIds方法

        Args:
            ids: ID字符串，逗号分隔

        Returns:
            删除的记录数量
        """
        return self.ex_account_dao.delete_ex_account_by_ids(ids)
    
    def delete_ex_account_by_id(self, id: int) -> int:
        """
        根据ID删除交易所账户
        对应Java中的deleteExAccountById方法

        Args:
            id: 账户ID

        Returns:
            删除的记录数量
        """
        return self.ex_account_dao.delete_ex_account_by_id(id)


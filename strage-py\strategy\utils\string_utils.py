"""
字符串工具类
迁移自: com.common.utils.StringUtils
"""
from typing import Any, Optional, List


class StringUtils:
    """字符串工具类"""
    
    @staticmethod
    def is_null(obj: Any) -> bool:
        """
        判断对象是否为None
        
        Args:
            obj: 要判断的对象
            
        Returns:
            是否为None
        """
        return obj is None
    
    @staticmethod
    def is_not_null(obj: Any) -> bool:
        """
        判断对象是否不为None
        
        Args:
            obj: 要判断的对象
            
        Returns:
            是否不为None
        """
        return obj is not None
    
    @staticmethod
    def is_empty(s: Optional[str]) -> bool:
        """
        判断字符串是否为空
        
        Args:
            s: 要判断的字符串
            
        Returns:
            是否为空
        """
        return s is None or s == ""
    
    @staticmethod
    def is_not_empty(s: Optional[str]) -> bool:
        """
        判断字符串是否不为空
        
        Args:
            s: 要判断的字符串
            
        Returns:
            是否不为空
        """
        return s is not None and s != ""
    
    @staticmethod
    def is_blank(s: Optional[str]) -> bool:
        """
        判断字符串是否为空白
        
        Args:
            s: 要判断的字符串
            
        Returns:
            是否为空白
        """
        return s is None or s.strip() == ""
    
    @staticmethod
    def is_not_blank(s: Optional[str]) -> bool:
        """
        判断字符串是否不为空白
        
        Args:
            s: 要判断的字符串
            
        Returns:
            是否不为空白
        """
        return s is not None and s.strip() != ""
    
    @staticmethod
    def default_if_empty(s: Optional[str], default_str: str) -> str:
        """
        如果字符串为空则返回默认值
        
        Args:
            s: 要判断的字符串
            default_str: 默认值
            
        Returns:
            字符串或默认值
        """
        return default_str if StringUtils.is_empty(s) else s
    
    @staticmethod
    def join(separator: str, items: List[Any]) -> str:
        """
        连接字符串列表
        
        Args:
            separator: 分隔符
            items: 要连接的项目列表
            
        Returns:
            连接后的字符串
        """
        return separator.join(str(item) for item in items if item is not None)

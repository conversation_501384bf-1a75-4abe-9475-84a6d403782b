"""
Binance基础服务类
包含HTTP请求相关的工具方法
"""
import hashlib
import hmac
import logging
import time
from decimal import Decimal
from typing import Dict, Optional, Any
from urllib.parse import urlencode

from .abstract_account_common import AbstractAccountCommon
from ..domain.entity.ex_account import ExAccount
from ..enums import ExchangeEnum
from ..utils import OkHttpUtil

logger = logging.getLogger(__name__)


class BinanceBaseService(AbstractAccountCommon):
    """Binance基础服务类 - 包含HTTP请求相关的工具方法"""
    
    # Binance API端点
    BASE_URL = "https://api.binance.com"
    FUTURES_BASE_URL = "https://fapi.binance.com"
    
    def __init__(self, db_session, account: ExAccount):
        """
        初始化Binance基础服务
        
        Args:
            db_session: 数据库会话
            account: 交易所账户
        """
        self.db_session = db_session
        self.account = account
        self.api_key = account.apikey
        self.secret_key = account.secret_key
        self.account_name = account.account_name
        self.platform = account.platform
        logger.info(f"BinanceBaseService initialized for account: {self.account_name}")
    
    def is_support(self) -> bool:
        """
        是否支持
        
        Returns:
            是否支持此服务
        """
        return (
            self.account is not None and
            self.account.is_active() and
            self.api_key is not None and
            self.secret_key is not None and
            len(self.api_key.strip()) > 0 and
            len(self.secret_key.strip()) > 0
        )
    
    def get_exchange(self) -> ExchangeEnum:
        """
        获取交易所
        
        Returns:
            交易所枚举
        """
        return ExchangeEnum.BINANCE
    
    def get_account_name(self) -> str:
        """
        获取账户名称
        
        Returns:
            账户名称
        """
        return self.account_name or ""
    
    def get_account(self) -> str:
        """
        获取账户
        
        Returns:
            账户信息
        """
        return self.account.ex_account if self.account else ""
    
    def _generate_signature(self, query_string: str) -> str:
        """
        生成签名
        
        Args:
            query_string: 查询字符串
            
        Returns:
            签名字符串
        """
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _get_timestamp(self) -> int:
        """
        获取时间戳
        
        Returns:
            当前时间戳（毫秒）
        """
        return int(time.time() * 1000)
    
    def _build_headers(self) -> Dict[str, str]:
        """
        构建请求头
        
        Returns:
            请求头字典
        """
        headers = {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        }
        return headers
    
    def _make_request(self, method: str, endpoint: str, params: Optional[Dict[str, Any]] = None, 
                     signed: bool = False, base_url: Optional[str] = None) -> Dict[str, Any]:
        """
        发起请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            params: 请求参数
            signed: 是否需要签名
            base_url: 基础URL
            
        Returns:
            响应结果
        """
        try:
            if base_url is None:
                base_url = self.BASE_URL
            
            url = f"{base_url}{endpoint}"
            headers = self._build_headers()
            
            if params is None:
                params = {}
            
            if signed:
                params['timestamp'] = self._get_timestamp()
                query_string = urlencode(params)
                signature = self._generate_signature(query_string)
                params['signature'] = signature
            
            if method.upper() == 'GET':
                if params:
                    url += '?' + urlencode(params)
                response_text = OkHttpUtil.do_get(url, headers)
            elif method.upper() == 'POST':
                response_text = OkHttpUtil.do_post(url, params, headers)
            elif method.upper() == 'PUT':
                response_text = OkHttpUtil.do_put(url, params, headers)
            elif method.upper() == 'DELETE':
                response_text = OkHttpUtil.do_delete(url, headers)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            if response_text:
                import json
                return json.loads(response_text)
            else:
                return {}
                
        except Exception as e:
            logger.error(f"请求失败 - URL: {url}, 错误: {e}")
            return {'error': str(e)}
    
    def get_account_info(self) -> Dict[str, Any]:
        """
        获取账户信息
        
        Returns:
            账户信息
        """
        try:
            return self._make_request('GET', '/api/v3/account', signed=True)
        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return {}
    
    def get_balance(self) -> Decimal:
        """
        获取余额
        
        Returns:
            账户余额
        """
        try:
            account_info = self.get_account_info()
            
            if 'balances' in account_info:
                # 计算USDT余额
                usdt_balance = Decimal('0')
                for balance in account_info['balances']:
                    if balance['asset'] == 'USDT':
                        free = Decimal(balance['free']) if balance['free'] else Decimal('0')
                        locked = Decimal(balance['locked']) if balance['locked'] else Decimal('0')
                        usdt_balance = free + locked
                        break
                
                return usdt_balance
            
            return Decimal('0')
            
        except Exception as e:
            logger.error(f"获取余额失败: {e}")
            return Decimal('0')
    
    def get_server_time(self) -> Dict[str, Any]:
        """
        获取服务器时间
        
        Returns:
            服务器时间信息
        """
        try:
            return self._make_request('GET', '/api/v3/time')
        except Exception as e:
            logger.error(f"获取服务器时间失败: {e}")
            return {}
    
    def get_exchange_info(self) -> Dict[str, Any]:
        """
        获取交易所信息
        
        Returns:
            交易所信息
        """
        try:
            return self._make_request('GET', '/api/v3/exchangeInfo')
        except Exception as e:
            logger.error(f"获取交易所信息失败: {e}")
            return {}
    
    def get_symbol_price(self, symbol: str) -> Dict[str, Any]:
        """
        获取交易对价格
        
        Args:
            symbol: 交易对
            
        Returns:
            价格信息
        """
        try:
            params = {'symbol': symbol}
            return self._make_request('GET', '/api/v3/ticker/price', params)
        except Exception as e:
            logger.error(f"获取交易对价格失败: {e}")
            return {}
    
    def test_connectivity(self) -> bool:
        """
        测试连接性
        
        Returns:
            是否连接成功
        """
        try:
            result = self._make_request('GET', '/api/v3/ping')
            return 'error' not in result
        except Exception as e:
            logger.error(f"测试连接性失败: {e}")
            return False
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"BinanceBaseService(account={self.account_name}, platform={self.platform})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"BinanceBaseService(account_name='{self.account_name}', platform='{self.platform}', supported={self.is_support()})"

"""
账户类型枚举类
迁移自: com.project.strategy.enums.AccountTypeEnum
"""
from enum import Enum
from typing import Optional


class AccountTypeEnum(Enum):
    """账户类型枚举"""
    
    NORMAL = (0, "normal", "普通账户")
    MARKET = (1, "market", "做市场查询接口的账户")
    STRATEGY_MATCH = (2, "strategy_match", "配对策略专用账户")
    OTHERS = (-1, "OTHERS", "Others")
    
    def __init__(self, code: int, name: str, desc: str):
        """
        初始化账户类型枚举

        Args:
            code: 账户类型代码
            name: 账户类型名称
            desc: 账户类型描述
        """
        self.code = code
        self.type_name = name  # 使用type_name避免与Enum.name冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取账户类型代码"""
        return self.code
    
    def get_name(self) -> str:
        """获取账户类型名称"""
        return self.type_name
    
    def get_desc(self) -> str:
        """获取账户类型描述"""
        return self.desc
    
    @classmethod
    def parse_value(cls, code: int) -> Optional['AccountTypeEnum']:
        """
        根据代码解析账户类型枚举
        
        Args:
            code: 账户类型代码
            
        Returns:
            对应的账户类型枚举，如果未找到则返回None
        """
        for account_type_enum in cls:
            if account_type_enum.get_code() == code:
                return account_type_enum
        return None
    
    @classmethod
    def parse_name(cls, name: str) -> Optional['AccountTypeEnum']:
        """
        根据名称解析账户类型枚举
        
        Args:
            name: 账户类型名称
            
        Returns:
            对应的账户类型枚举，如果未找到则返回None
        """
        for account_type_enum in cls:
            if account_type_enum.get_name() == name:
                return account_type_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_name}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"AccountTypeEnum.{self.name}"

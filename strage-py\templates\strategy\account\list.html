<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易所账户管理</title>
    <link rel="shortcut icon" href="/static/favicon.ico">
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.11.1/bootstrap-table.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId" class="form-horizontal">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <i class="fa fa-search"></i> 搜索条件
                            </h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="control-label">账户名称：</label>
                                        <input type="text" name="accountName" class="form-control"
                                            placeholder="请输入账户名称" />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="control-label">平台：</label>
                                        <select name="platform" class="form-control">
                                            <option value="">请选择平台</option>
                                            <option value="BINANCE">币安</option>
                                            <option value="OKEX">欧易</option>
                                            <option value="HUOBI">火币</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="control-label">状态：</label>
                                        <select name="state" class="form-control">
                                            <option value="">请选择状态</option>
                                            <option value="1">启用</option>
                                            <option value="0">禁用</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="control-label" style="visibility: hidden;">操作：</label>
                                        <div>
                                            <button type="button" class="btn btn-primary btn-sm"
                                                onclick="searchTable()">
                                                <i class="fa fa-search"></i> 搜索
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm" onclick="resetForm()">
                                                <i class="fa fa-refresh"></i> 重置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="fa fa-list"></i> 交易所账户列表
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div class="btn-group-sm" id="toolbar" role="group">
                            <button type="button" class="btn btn-success" onclick="showAddModal()">
                                <i class="fa fa-plus"></i> 添加
                            </button>
                            <button type="button" class="btn btn-primary single disabled" disabled
                                onclick="editSelected()">
                                <i class="fa fa-edit"></i> 修改
                            </button>
                            <button type="button" class="btn btn-danger multiple disabled" disabled
                                onclick="batchRemove()">
                                <i class="fa fa-remove"></i> 删除
                            </button>
                            <button type="button" class="btn btn-primary multiple disabled" disabled
                                onclick="showAssetModal()">
                                <i class="fa fa-plus"></i> 余额
                            </button>
                            <div class="pull-right">
                                <button type="button" class="btn btn-default" onclick="refreshTable()">
                                    <i class="fa fa-refresh"></i> 刷新
                                </button>
                            </div>
                        </div>
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加账户模态框 -->
    <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="addModalLabel">添加交易所账户</h4>
                </div>
                <div class="modal-body">
                    <form id="addForm">
                        <div class="form-group">
                            <label>账户名称：</label>
                            <input type="text" name="accountName" class="form-control" placeholder="请输入账户名称" required />
                        </div>
                        <div class="form-group">
                            <label>平台：</label>
                            <select name="platform" class="form-control" required>
                                <option value="">请选择平台</option>
                                <option value="BINANCE">币安</option>
                                <option value="OKEX">欧易</option>
                                <option value="HUOBI">火币</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>API Key：</label>
                            <input type="text" name="apiKey" class="form-control" placeholder="请输入API Key" required />
                        </div>
                        <div class="form-group">
                            <label>Secret Key：</label>
                            <input type="password" name="secretKey" class="form-control" placeholder="请输入Secret Key"
                                required />
                        </div>
                        <div class="form-group">
                            <label>密码：</label>
                            <input type="text" name="password" class="form-control" placeholder="请输入密码（如需要）" />
                        </div>
                        <div class="form-group">
                            <label>状态：</label>
                            <select name="state" class="form-control" required>
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>描述信息：</label>
                            <textarea name="message" class="form-control" rows="3" placeholder="请输入描述信息"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitAddForm()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑账户模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="editModalLabel">修改交易所账户</h4>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" name="id" />
                        <div class="form-group">
                            <label>账户名称：</label>
                            <input type="text" name="accountName" class="form-control" placeholder="请输入账户名称" required />
                        </div>
                        <div class="form-group">
                            <label>平台：</label>
                            <select name="platform" class="form-control" required>
                                <option value="">请选择平台</option>
                                <option value="BINANCE">币安</option>
                                <option value="OKEX">欧易</option>
                                <option value="HUOBI">火币</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>API Key：</label>
                            <input type="text" name="apiKey" class="form-control" placeholder="请输入API Key" required />
                        </div>
                        <div class="form-group">
                            <label>Secret Key：</label>
                            <input type="password" name="secretKey" class="form-control" placeholder="请输入Secret Key"
                                required />
                        </div>
                        <div class="form-group">
                            <label>密码：</label>
                            <input type="text" name="password" class="form-control" placeholder="请输入密码（如需要）" />
                        </div>
                        <div class="form-group">
                            <label>状态：</label>
                            <select name="state" class="form-control" required>
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>描述信息：</label>
                            <textarea name="message" class="form-control" rows="3" placeholder="请输入描述信息"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitEditForm()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 余额查看模态框 -->
    <div class="modal fade" id="assetModal" tabindex="-1" role="dialog" aria-labelledby="assetModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="assetModalLabel">账户余额</h4>
                </div>
                <div class="modal-body">
                    <form id="assetForm">
                        <div class="form-group">
                            <label>账户名：</label>
                            <input type="text" name="accountName" class="form-control" readonly />
                        </div>
                        <div class="form-group">
                            <label>现货账户：</label>
                            <input type="text" name="spotBalance" class="form-control" readonly />
                        </div>
                        <div class="form-group">
                            <label>合约账户：</label>
                            <input type="text" name="futuresUBalance" class="form-control" readonly />
                        </div>
                        <div class="form-group">
                            <label>划转类型：</label>
                            <select name="transferType" class="form-control">
                                <option value="MAIN_UMFUTURE">从现货转到U本位合约</option>
                                <option value="UMFUTURE_MAIN">从U本位合约转到现货</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>划转金额U：</label>
                            <input type="text" name="transferAmount" class="form-control" placeholder="请输入划转金额" />
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="submitTransfer()">确定划转</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.11.1/bootstrap-table.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-table/1.11.1/locale/bootstrap-table-zh-CN.min.js"></script>
    <script src="/static/js/common.js"></script>

    <script type="text/javascript">
        var prefix = "/strategy/account";
        var editFlag = true;
        var removeFlag = true;
        var tableData = []; // 存储表格数据

        $(function () {
            console.log('开始初始化 Bootstrap Table');
            $('#bootstrap-table').bootstrapTable({
                url: prefix + "/list",
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                dataType: 'json',
                cache: false,
                striped: true,
                pagination: true,
                sortable: true,
                sortOrder: "desc",
                queryParams: function (params) {
                    var searchParams = {
                        pageSize: params.limit,
                        pageNum: params.offset / params.limit + 1,
                        accountName: $('input[name="accountName"]').val() || '',
                        platform: $('select[name="platform"]').val() || '',
                        state: $('select[name="state"]').val() || '',
                        message: $('input[name="message"]').val() || ''
                    };
                    console.log('搜索参数:', searchParams);
                    return searchParams;
                },
                responseHandler: function (res) {
                    console.log('Bootstrap Table 接收到的数据:', res);
                    if (res.code === 0) {
                        console.log('数据行数:', res.rows.length);
                        return {
                            "total": res.total,
                            "rows": res.rows
                        };
                    } else {
                        console.error('后端返回错误:', res.msg);
                        return {
                            "total": 0,
                            "rows": []
                        };
                    }
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '账户ID',
                    visible: false
                },
                {
                    field: 'account_name',
                    title: '账户名称'
                },
                {
                    field: 'apikey',
                    title: 'API Key',
                    formatter: function (value, row, index) {
                        if (value && value.length > 8) {
                            return value.substring(0, 8) + '****';
                        }
                        return value;
                    }
                },
                {
                    field: 'platform',
                    title: '平台'
                },
                {
                    field: 'state',
                    title: '状态',
                    formatter: function (value, row, index) {
                        if (value == 1) {
                            return '<span class="label label-success">启用</span>';
                        } else {
                            return '<span class="label label-danger">禁用</span>';
                        }
                    }
                },
                {
                    field: 'message',
                    title: '描述信息'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (editFlag) {
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editRowInline(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        }
                        if (removeFlag) {
                            actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="removeRowInline(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
            });

            // 添加表格事件监听
            $('#bootstrap-table').on('load-success.bs.table', function (e, data) {
                console.log('表格加载成功，数据:', data);
                // 手动渲染数据作为备用方案
                setTimeout(function () {
                    var tbody = $('#bootstrap-table tbody');
                    if (tbody.find('tr').length <= 1 && data.rows && data.rows.length > 0) {
                        console.log('Bootstrap Table渲染失败，使用手动渲染');
                        renderTableManually(data.rows);
                    }
                }, 100);
            });

            $('#bootstrap-table').on('load-error.bs.table', function (e, status) {
                console.error('表格加载失败，状态:', status);
            });

            $('#bootstrap-table').on('post-body.bs.table', function () {
                console.log('表格渲染完成');
            });
        });

        // 手动渲染表格数据
        function renderTableManually(rows) {
            var tbody = $('#bootstrap-table tbody');
            tbody.empty();

            // 存储数据到全局变量
            tableData = rows;

            if (rows && rows.length > 0) {
                $.each(rows, function (index, row) {
                    var tr = $('<tr>');

                    // 复选框
                    tr.append('<td><input type="checkbox" value="' + row.id + '" onchange="updateButtonStates()"></td>');

                    // 账户名称
                    tr.append('<td>' + (row.account_name || '') + '</td>');

                    // API Key (脱敏显示)
                    var apiKey = row.apikey || '';
                    if (apiKey.length > 8) {
                        apiKey = apiKey.substring(0, 8) + '****';
                    }
                    tr.append('<td>' + apiKey + '</td>');

                    // 平台
                    tr.append('<td>' + (row.platform || '') + '</td>');

                    // 状态
                    var stateHtml = '';
                    if (row.state == 1) {
                        stateHtml = '<span class="label label-success">启用</span>';
                    } else {
                        stateHtml = '<span class="label label-danger">禁用</span>';
                    }
                    tr.append('<td>' + stateHtml + '</td>');

                    // 描述信息
                    tr.append('<td>' + (row.message || '') + '</td>');

                    // 操作
                    var actions = [];
                    if (editFlag) {
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editRowInline(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    }
                    if (removeFlag) {
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="removeRowInline(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    }
                    tr.append('<td>' + actions.join('') + '</td>');

                    tbody.append(tr);
                });
            } else {
                tbody.append('<tr><td colspan="7" class="text-center">没有找到匹配的记录</td></tr>');
            }
        }

        // 搜索
        function searchTable() {
            $('#bootstrap-table').bootstrapTable('refresh');
        }

        // 重置
        function resetForm() {
            $('#formId')[0].reset();
            $('#bootstrap-table').bootstrapTable('refresh');
        }

        // 刷新表格
        function refreshTable() {
            $('#bootstrap-table').bootstrapTable('refresh');
        }

        // 编辑选中的记录（单选）
        function editSelected() {
            var selections = getSelectedRows();
            if (selections.length != 1) {
                alert('请选择一条记录进行编辑');
                return;
            }
            editRowInline(selections[0].id);
        }

        // 显示余额模态框（单选）
        function showAssetModal() {
            var selections = getSelectedRows();
            if (selections.length != 1) {
                alert('请选择一个账户查看余额');
                return;
            }
            var account = selections[0];

            // 显示模态框
            $('#assetModal').modal('show');
            $('#assetForm input[name="accountName"]').val(account.account_name);

            // 获取账户余额
            loadAccountBalance(account.id);
        }

        // 加载账户余额
        function loadAccountBalance(accountId) {
            $.ajax({
                url: prefix + "/asset/" + accountId + "/data",
                type: "GET",
                success: function (result) {
                    if (result.code == 0) {
                        var data = result.data;
                        $('#assetForm input[name="spotBalance"]').val(data.spotBalance || '0.00');
                        $('#assetForm input[name="futuresUBalance"]').val(data.futuresUBalance || '0.00');
                    } else {
                        $('#assetForm input[name="spotBalance"]').val('获取失败');
                        $('#assetForm input[name="futuresUBalance"]').val('获取失败');
                    }
                },
                error: function () {
                    $('#assetForm input[name="spotBalance"]').val('获取失败');
                    $('#assetForm input[name="futuresUBalance"]').val('获取失败');
                }
            });
        }

        // 提交划转
        function submitTransfer() {
            var formData = $('#assetForm').serialize();
            $.ajax({
                url: prefix + "/asset",
                type: "POST",
                data: formData,
                success: function (result) {
                    if (result.code == 0) {
                        alert('划转成功');
                        $('#assetModal').modal('hide');
                        // 重新加载余额
                        var selections = getSelectedRows();
                        if (selections.length > 0) {
                            loadAccountBalance(selections[0].id);
                        }
                    } else {
                        alert(result.msg || '划转失败');
                    }
                },
                error: function () {
                    alert('划转失败');
                }
            });
        }

        // 获取选中的行数据
        function getSelectedRows() {
            var selections = [];
            $('#bootstrap-table tbody input[type="checkbox"]:checked').each(function () {
                var id = $(this).val();
                var row = tableData.find(function (item) {
                    return item.id == id;
                });
                if (row) {
                    selections.push(row);
                }
            });
            return selections;
        }

        // 更新按钮状态
        function updateButtonStates() {
            var selections = getSelectedRows();
            var singleCount = selections.length == 1;
            var multipleCount = selections.length > 0;

            // 单选按钮（修改）
            $('.single').prop('disabled', !singleCount).toggleClass('disabled', !singleCount);

            // 多选按钮（删除、余额）
            $('.multiple').prop('disabled', !multipleCount).toggleClass('disabled', !multipleCount);
        }

        // 显示添加模态框
        function showAddModal() {
            $('#addModal').modal('show');
            $('#addForm')[0].reset();
        }

        // 提交添加表单
        function submitAddForm() {
            var formData = $('#addForm').serialize();
            $.ajax({
                url: prefix + "/add",
                type: "POST",
                data: formData,
                success: function (result) {
                    if (result.code == 0) {
                        alert('新增成功');
                        $('#addModal').modal('hide');
                        $('#bootstrap-table').bootstrapTable('refresh');
                    } else {
                        alert(result.msg || '新增失败');
                    }
                },
                error: function () {
                    alert('新增失败');
                }
            });
        }

        // 提交编辑表单
        function submitEditForm() {
            var formData = $('#editForm').serialize();
            $.ajax({
                url: prefix + "/edit",
                type: "POST",
                data: formData,
                success: function (result) {
                    if (result.code == 0) {
                        alert('修改成功');
                        $('#editModal').modal('hide');
                        $('#bootstrap-table').bootstrapTable('refresh');
                    } else {
                        alert(result.msg || '修改失败');
                    }
                },
                error: function () {
                    alert('修改失败');
                }
            });
        }

        // 行内编辑功能
        function editRowInline(id) {
            // 从全局变量中查找对应的行数据
            var row = tableData.find(function (item) {
                return item.id == id;
            });

            if (row) {
                $('#editModal').modal('show');
                $('#editForm input[name="id"]').val(row.id);
                $('#editForm input[name="accountName"]').val(row.account_name);
                $('#editForm select[name="platform"]').val(row.platform);
                $('#editForm input[name="apiKey"]').val(row.apikey);
                $('#editForm input[name="secretKey"]').val(row.secret_key);
                $('#editForm input[name="password"]').val(row.password);
                $('#editForm select[name="state"]').val(row.state);
                $('#editForm textarea[name="message"]').val(row.message);
            }
        }

        // 行内删除功能
        function removeRowInline(id) {
            if (confirm('确定删除该条记录吗？')) {
                $.ajax({
                    url: prefix + "/remove",
                    type: "POST",
                    data: { ids: id },
                    success: function (result) {
                        if (result.code == 0) {
                            alert('删除成功');
                            $('#bootstrap-table').bootstrapTable('refresh');
                        } else {
                            alert(result.msg || '删除失败');
                        }
                    },
                    error: function () {
                        alert('删除失败');
                    }
                });
            }
        }

        // 批量删除
        function batchRemove() {
            var selections = getSelectedRows();
            if (selections.length == 0) {
                alert('请选择要删除的数据');
                return;
            }
            if (confirm('确认删除选中的' + selections.length + '条数据吗？')) {
                var ids = [];
                $.each(selections, function (index, row) {
                    ids.push(row.id);
                });
                $.ajax({
                    url: prefix + "/remove",
                    type: "POST",
                    data: { ids: ids.join(',') },
                    success: function (result) {
                        if (result.code == 0) {
                            alert('删除成功');
                            refreshTable();
                        } else {
                            alert(result.msg || '删除失败');
                        }
                    },
                    error: function () {
                        alert('删除失败');
                    }
                });
            }
        }


    </script>
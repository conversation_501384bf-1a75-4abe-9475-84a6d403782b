"""
账户Bean工厂类
迁移自: com.project.strategy.service.impl.AccountBeanFactory
"""
import logging
from typing import Dict, List, Optional
from ..context.account_context import AccountContext
from .i_account_service import IAccountService
from ..dao.ex_account_dao import ExAccountDAO
from ..domain.entity.ex_account import ExAccount
from ..enums import ExchangeEnum, InstrumentEnum
from .binance_futures_service import BinanceFuturesService
from .binance_spot_service import BinanceSpotService

logger = logging.getLogger(__name__)


class AccountBeanFactory:
    """账户Bean工厂类 - 负责创建和管理账户服务实例"""

    def __init__(self, db_session, account_context: AccountContext):
        """
        初始化账户Bean工厂

        Args:
            db_session: 数据库会话
            account_context: 账户上下文
        """
        self.db_session = db_session
        self.account_context = account_context
        self.ex_account_dao = ExAccountDAO()
        logger.info("AccountBeanFactory initialized")
    
    def create_account_services(self) -> None:
        """
        创建账户服务实例
        对应Java中的@PostConstruct register()方法
        """
        try:
            logger.info("开始创建账户服务实例...")

            # 创建查询条件，与Java版本保持一致
            search_account = ExAccount()
            search_account.state = 1
            search_account.platform = ExchangeEnum.BINANCE.get_name()

            # 查询符合条件的账户列表
            account_list = self.ex_account_dao.select_ex_account_list(search_account)

            if not account_list:
                logger.warning("没有找到符合条件的交易所账户")
                return

            created_count = 0

            for ex_account in account_list:
                try:
                    # 为每个账户创建期货服务
                    futures_service = self._create_futures_service(ex_account)
                    if futures_service:
                        futures_bean_name = f"{ex_account.platform}_{InstrumentEnum.FUTURES_U.get_value()}_{ex_account.account_name}"
                        self.account_context.add_account_client(futures_bean_name, futures_service)
                        created_count += 1
                        logger.info(f"创建期货服务成功: {futures_bean_name}")

                    # 为每个账户创建现货服务
                    spot_service = self._create_spot_service(ex_account)
                    if spot_service:
                        spot_bean_name = f"{ex_account.platform}_{InstrumentEnum.SPOT.get_value()}_{ex_account.account_name}"
                        self.account_context.add_account_client(spot_bean_name, spot_service)
                        created_count += 1
                        logger.info(f"创建现货服务成功: {spot_bean_name}")

                except Exception as e:
                    logger.error(f"创建账户服务失败 - 账户ID: {ex_account.id}, 错误: {e}")

            logger.info(f"账户服务创建完成，共创建 {created_count} 个服务实例")

        except Exception as e:
            logger.error(f"创建账户服务实例失败: {e}")
    
    def _create_futures_service(self, ex_account: ExAccount) -> Optional[IAccountService]:
        """
        创建期货服务实例
        对应Java中的BinanceFuturesService创建逻辑

        Args:
            ex_account: 交易所账户

        Returns:
            期货服务实例，创建失败返回None
        """
        try:
            # 创建期货服务实例，传递与Java版本相同的属性
            futures_service = BinanceFuturesService(
                db_session=self.db_session,
                account=ex_account
            )

            # 设置属性（对应Java中的addPropertyValue）
            futures_service.account = ex_account.ex_account
            futures_service.account_name = ex_account.account_name
            futures_service.api_key = ex_account.apikey
            futures_service.secret_key = ex_account.secret_key

            return futures_service

        except Exception as e:
            logger.error(f"创建期货服务实例失败: {e}")
            return None

    def _create_spot_service(self, ex_account: ExAccount) -> Optional[IAccountService]:
        """
        创建现货服务实例
        对应Java中的BinanceSpotService创建逻辑

        Args:
            ex_account: 交易所账户

        Returns:
            现货服务实例，创建失败返回None
        """
        try:
            # 创建现货服务实例，传递与Java版本相同的属性
            spot_service = BinanceSpotService(
                db_session=self.db_session,
                account=ex_account
            )

            # 设置属性（对应Java中的addPropertyValue）
            spot_service.account = ex_account.ex_account
            spot_service.account_name = ex_account.account_name
            spot_service.api_key = ex_account.apikey
            spot_service.secret_key = ex_account.secret_key

            return spot_service

        except Exception as e:
            logger.error(f"创建现货服务实例失败: {e}")
            return None
    
    def register(self, ex_account: ExAccount) -> None:
        """
        注册单个账户服务
        对应Java中的register(ExAccount exAccount)方法

        Args:
            ex_account: 交易所账户
        """
        try:
            # 创建期货服务
            futures_service = self._create_futures_service(ex_account)
            if futures_service:
                futures_bean_name = f"{ex_account.platform}_{InstrumentEnum.FUTURES_U.get_value()}_{ex_account.account_name}"
                self.account_context.add_account_client(futures_bean_name, futures_service)
                logger.info(f"注册期货服务成功: {futures_bean_name}")

            # 创建现货服务
            spot_service = self._create_spot_service(ex_account)
            if spot_service:
                spot_bean_name = f"{ex_account.platform}_{InstrumentEnum.SPOT.get_value()}_{ex_account.account_name}"
                self.account_context.add_account_client(spot_bean_name, spot_service)
                logger.info(f"注册现货服务成功: {spot_bean_name}")

        except Exception as e:
            logger.error(f"注册账户服务失败: {e}")

    def add_account_service(self, ex_account: ExAccount) -> bool:
        """
        添加账户服务
        对应Java中的register(ExAccount exAccount)方法

        Args:
            ex_account: 交易所账户

        Returns:
            是否添加成功
        """
        try:
            success_count = 0

            # 创建期货服务
            futures_service = self._create_futures_service(ex_account)
            if futures_service:
                futures_bean_name = f"{ex_account.platform}_{InstrumentEnum.FUTURES_U.get_value()}_{ex_account.account_name}"
                self.account_context.add_account_client(futures_bean_name, futures_service)
                success_count += 1
                logger.info(f"添加期货服务成功: {futures_bean_name}")

            # 创建现货服务
            spot_service = self._create_spot_service(ex_account)
            if spot_service:
                spot_bean_name = f"{ex_account.platform}_{InstrumentEnum.SPOT.get_value()}_{ex_account.account_name}"
                self.account_context.add_account_client(spot_bean_name, spot_service)
                success_count += 1
                logger.info(f"添加现货服务成功: {spot_bean_name}")

            return success_count == 2

        except Exception as e:
            logger.error(f"添加账户服务失败: {e}")
            return False
    
    def refresh_account_services(self) -> None:
        """
        刷新账户服务
        """
        try:
            logger.info("开始刷新账户服务...")

            # 清空现有的客户端
            self.account_context.refresh_clients({})

            # 重新创建账户服务
            self.create_account_services()

            logger.info("账户服务刷新完成")

        except Exception as e:
            logger.error(f"刷新账户服务失败: {e}")
    
    def get_service_by_criteria(self, exchange: ExchangeEnum, 
                               instrument: InstrumentEnum, 
                               account_name: str) -> Optional[IAccountService]:
        """
        根据条件获取服务
        
        Args:
            exchange: 交易所
            instrument: 交易工具
            account_name: 账户名称
            
        Returns:
            匹配的服务实例，未找到返回None
        """
        return self.account_context.get_account_client(exchange, instrument, account_name)
    
    def get_services_by_exchange(self, exchange: ExchangeEnum) -> List[IAccountService]:
        """
        根据交易所获取服务列表
        
        Args:
            exchange: 交易所
            
        Returns:
            服务列表
        """
        return self.account_context.get_platform_client(exchange)
    
    def remove_account_service(self, account_name: str, platform: str = None) -> bool:
        """
        移除账户服务

        Args:
            account_name: 账户名称
            platform: 平台名称（可选）

        Returns:
            是否移除成功
        """
        try:
            removed_count = 0
            all_clients = self.account_context.get_all_clients()

            # 查找匹配的Bean名称（期货和现货服务）
            for bean_name in list(all_clients.keys()):
                if account_name in bean_name and (platform is None or platform in bean_name):
                    self.account_context.remove_account_client(bean_name)
                    logger.info(f"移除账户服务成功: {bean_name}")
                    removed_count += 1

            if removed_count > 0:
                logger.info(f"共移除 {removed_count} 个账户服务")
                return True
            else:
                logger.warning(f"未找到账户名为 {account_name} 的服务")
                return False

        except Exception as e:
            logger.error(f"移除账户服务失败: {e}")
            return False
    
    def get_service_count(self) -> int:
        """
        获取服务数量
        
        Returns:
            服务数量
        """
        return self.account_context.get_client_count()
    
    def get_service_status(self) -> Dict[str, bool]:
        """
        获取服务状态
        
        Returns:
            服务状态字典
        """
        return self.account_context.validate_clients()
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"AccountBeanFactory(services={self.get_service_count()})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"AccountBeanFactory(service_count={self.get_service_count()})"

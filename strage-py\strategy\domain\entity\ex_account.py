"""
交易所账户实体类
迁移自: com.project.strategy.domain.entity.ExAccount
"""
from dataclasses import dataclass
from typing import Optional
from datetime import datetime


@dataclass
class ExAccount:
    """交易所账户实体"""
    
    id: Optional[int] = None
    ex_account: Optional[str] = None
    user_id: Optional[int] = None
    account_name: Optional[str] = None  # account_name
    apikey: Optional[str] = None  # platfrom名称:jupiter
    secret_key: Optional[str] = None  # pool address
    password: Optional[str] = None  # pool address
    platform: Optional[str] = None  # 平台
    state: Optional[int] = None  # 0: 不可用， 1：可用
    message: Optional[str] = None  # 描述信息
    secret_msg: Optional[str] = None  # 描述信息
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        if self.id is not None and not isinstance(self.id, int):
            self.id = int(self.id) if str(self.id).isdigit() else None
            
        if self.user_id is not None and not isinstance(self.user_id, int):
            self.user_id = int(self.user_id) if str(self.user_id).isdigit() else None
            
        if self.state is not None and not isinstance(self.state, int):
            self.state = int(self.state) if str(self.state).isdigit() else None
    
    def is_active(self) -> bool:
        """检查账户是否可用"""
        return self.state == 1
    
    def is_inactive(self) -> bool:
        """检查账户是否不可用"""
        return self.state == 0
    
    def is_valid(self) -> bool:
        """检查账户信息是否有效"""
        return (
            self.apikey is not None and 
            self.secret_key is not None and 
            len(self.apikey.strip()) > 0 and 
            len(self.secret_key.strip()) > 0 and
            self.is_active()
        )
    
    def get_credentials(self) -> dict:
        """获取认证信息"""
        return {
            'apikey': self.apikey,
            'secret_key': self.secret_key,
            'password': self.password
        }
    
    @classmethod
    def builder(cls) -> 'ExAccountBuilder':
        """创建构建器"""
        return ExAccountBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ExAccount':
        """从字典创建实例"""
        # 处理datetime字段
        if 'create_time' in data and data['create_time']:
            if isinstance(data['create_time'], str):
                try:
                    data['create_time'] = datetime.fromisoformat(data['create_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['create_time'] = None
        
        if 'update_time' in data and data['update_time']:
            if isinstance(data['update_time'], str):
                try:
                    data['update_time'] = datetime.fromisoformat(data['update_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['update_time'] = None
        
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ExAccount(id={self.id}, account_name='{self.account_name}', platform='{self.platform}', state={self.state})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"ExAccount(id={self.id}, user_id={self.user_id}, "
                f"account_name='{self.account_name}', platform='{self.platform}', "
                f"apikey='***', state={self.state})")


class ExAccountBuilder:
    """ExAccount构建器"""
    
    def __init__(self):
        self._data = {}
    
    def id(self, id: int) -> 'ExAccountBuilder':
        self._data['id'] = id
        return self
    
    def ex_account(self, ex_account: str) -> 'ExAccountBuilder':
        self._data['ex_account'] = ex_account
        return self
    
    def user_id(self, user_id: int) -> 'ExAccountBuilder':
        self._data['user_id'] = user_id
        return self
    
    def account_name(self, account_name: str) -> 'ExAccountBuilder':
        self._data['account_name'] = account_name
        return self
    
    def apikey(self, apikey: str) -> 'ExAccountBuilder':
        self._data['apikey'] = apikey
        return self
    
    def secret_key(self, secret_key: str) -> 'ExAccountBuilder':
        self._data['secret_key'] = secret_key
        return self
    
    def password(self, password: str) -> 'ExAccountBuilder':
        self._data['password'] = password
        return self
    
    def platform(self, platform: str) -> 'ExAccountBuilder':
        self._data['platform'] = platform
        return self
    
    def state(self, state: int) -> 'ExAccountBuilder':
        self._data['state'] = state
        return self
    
    def message(self, message: str) -> 'ExAccountBuilder':
        self._data['message'] = message
        return self
    
    def secret_msg(self, secret_msg: str) -> 'ExAccountBuilder':
        self._data['secret_msg'] = secret_msg
        return self
    
    def create_time(self, create_time: datetime) -> 'ExAccountBuilder':
        self._data['create_time'] = create_time
        return self
    
    def update_time(self, update_time: datetime) -> 'ExAccountBuilder':
        self._data['update_time'] = update_time
        return self
    
    def build(self) -> ExAccount:
        """构建ExAccount实例"""
        return ExAccount(**self._data)

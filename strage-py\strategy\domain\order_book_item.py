"""
订单簿项目领域模型
迁移自: com.project.strategy.domain.OrderBookItem
"""
from dataclasses import dataclass
from typing import Optional
import threading


@dataclass
class OrderBookItem:
    """订单簿项目"""
    
    # 卖方数据
    ask_px: Optional[str] = None  # 卖价
    ask_amount: Optional[str] = None  # 卖量
    
    # 买方数据
    bid_px: Optional[str] = None  # 买价
    bid_amount: Optional[str] = None  # 买量
    
    timestamp: Optional[int] = None  # 时间戳
    
    # 使用锁来保证线程安全（对应Java的volatile）
    _lock: threading.RLock = threading.RLock()
    
    def __post_init__(self):
        """初始化后处理"""
        if not hasattr(self, '_lock'):
            self._lock = threading.RLock()
    
    def get_ask_px_double(self) -> Optional[float]:
        """获取卖价的浮点数值"""
        with self._lock:
            if self.ask_px is None:
                return None
            try:
                return float(self.ask_px)
            except (ValueError, TypeError):
                return None
    
    def get_ask_amount_double(self) -> Optional[float]:
        """获取卖量的浮点数值"""
        with self._lock:
            if self.ask_amount is None:
                return None
            try:
                return float(self.ask_amount)
            except (ValueError, TypeError):
                return None
    
    def get_bid_px_double(self) -> Optional[float]:
        """获取买价的浮点数值"""
        with self._lock:
            if self.bid_px is None:
                return None
            try:
                return float(self.bid_px)
            except (ValueError, TypeError):
                return None
    
    def get_bid_amount_double(self) -> Optional[float]:
        """获取买量的浮点数值"""
        with self._lock:
            if self.bid_amount is None:
                return None
            try:
                return float(self.bid_amount)
            except (ValueError, TypeError):
                return None
    
    def get_ask_usdt_value(self) -> Optional[float]:
        """获取卖方USDT价值"""
        ask_px = self.get_ask_px_double()
        ask_amount = self.get_ask_amount_double()
        
        if ask_px is not None and ask_amount is not None:
            return ask_px * ask_amount
        return None
    
    def get_bid_usdt_value(self) -> Optional[float]:
        """获取买方USDT价值"""
        bid_px = self.get_bid_px_double()
        bid_amount = self.get_bid_amount_double()
        
        if bid_px is not None and bid_amount is not None:
            return bid_px * bid_amount
        return None
    
    def update_ask(self, px: str, amount: str) -> None:
        """更新卖方数据（线程安全）"""
        with self._lock:
            self.ask_px = px
            self.ask_amount = amount
    
    def update_bid(self, px: str, amount: str) -> None:
        """更新买方数据（线程安全）"""
        with self._lock:
            self.bid_px = px
            self.bid_amount = amount
    
    def update_timestamp(self, timestamp: int) -> None:
        """更新时间戳（线程安全）"""
        with self._lock:
            self.timestamp = timestamp
    
    def is_valid(self) -> bool:
        """检查数据是否有效"""
        return (
            self.ask_px is not None and 
            self.ask_amount is not None and
            self.bid_px is not None and 
            self.bid_amount is not None and
            self.get_ask_px_double() is not None and
            self.get_ask_amount_double() is not None and
            self.get_bid_px_double() is not None and
            self.get_bid_amount_double() is not None
        )
    
    def get_spread(self) -> Optional[float]:
        """获取买卖价差"""
        ask_px = self.get_ask_px_double()
        bid_px = self.get_bid_px_double()
        
        if ask_px is not None and bid_px is not None:
            return ask_px - bid_px
        return None
    
    def get_spread_percentage(self) -> Optional[float]:
        """获取买卖价差百分比"""
        ask_px = self.get_ask_px_double()
        bid_px = self.get_bid_px_double()
        
        if ask_px is not None and bid_px is not None and bid_px > 0:
            return ((ask_px - bid_px) / bid_px) * 100
        return None
    
    @classmethod
    def builder(cls) -> 'OrderBookItemBuilder':
        """创建构建器"""
        return OrderBookItemBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'ask_px': self.ask_px,
            'ask_amount': self.ask_amount,
            'bid_px': self.bid_px,
            'bid_amount': self.bid_amount,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'OrderBookItem':
        """从字典创建实例"""
        return cls(
            ask_px=data.get('ask_px'),
            ask_amount=data.get('ask_amount'),
            bid_px=data.get('bid_px'),
            bid_amount=data.get('bid_amount'),
            timestamp=data.get('timestamp')
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"OrderBookItem(ask={self.ask_px}@{self.ask_amount}, "
                f"bid={self.bid_px}@{self.bid_amount}, ts={self.timestamp})")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"OrderBookItem(ask_px='{self.ask_px}', ask_amount='{self.ask_amount}', "
                f"bid_px='{self.bid_px}', bid_amount='{self.bid_amount}', timestamp={self.timestamp})")


class OrderBookItemBuilder:
    """OrderBookItem构建器"""
    
    def __init__(self):
        self._data = {}
    
    def ask_px(self, ask_px: str) -> 'OrderBookItemBuilder':
        self._data['ask_px'] = ask_px
        return self
    
    def ask_amount(self, ask_amount: str) -> 'OrderBookItemBuilder':
        self._data['ask_amount'] = ask_amount
        return self
    
    def bid_px(self, bid_px: str) -> 'OrderBookItemBuilder':
        self._data['bid_px'] = bid_px
        return self
    
    def bid_amount(self, bid_amount: str) -> 'OrderBookItemBuilder':
        self._data['bid_amount'] = bid_amount
        return self
    
    def timestamp(self, timestamp: int) -> 'OrderBookItemBuilder':
        self._data['timestamp'] = timestamp
        return self
    
    def build(self) -> OrderBookItem:
        """构建OrderBookItem实例"""
        return OrderBookItem(**self._data)

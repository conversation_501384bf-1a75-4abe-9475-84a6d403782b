"""
系统管理控制器
处理系统相关的HTTP请求
"""
import logging
import os
import uuid
from datetime import datetime
from flask import Blueprint, render_template, request, jsonify, session, redirect
from services.auth_service import AuthService, login_required
from services.user_service import UserService
from utils.log_decorator import log_insert, log_update, log_delete, log_export, log_operation, BusinessType

logger = logging.getLogger(__name__)

system_bp = Blueprint('system', __name__, url_prefix='/system')

# 首页路由已迁移到app.py中

@system_bp.route('/main')
@login_required
def main():
    """主页面内容"""
    return render_template('system/main.html')

@system_bp.route('/role')
@login_required
def role_list():
    """角色管理"""
    return render_template('system/role.html')

# 用户管理相关路由
@system_bp.route('/user')
@login_required
def user_list():
    """用户列表页面"""
    return render_template('user/user.html')

@system_bp.route('/user/detail/<int:user_id>')
@login_required
def user_detail(user_id):
    """用户详情页面"""
    try:
        user = UserService.select_user_by_id(user_id)
        if not user:
            return render_template('error/404.html'), 404
        
        return render_template('user/detail.html', user=user)
    
    except Exception as e:
        logger.error(f"查看用户详情失败: {e}")
        return render_template('error/500.html'), 500

@system_bp.route('/user/add', methods=['GET'])
@login_required
def user_add_page():
    """新增用户页面"""
    return render_template('user/add.html')

@system_bp.route('/user/add', methods=['POST'])
@login_required
@log_operation("用户管理", BusinessType.INSERT)
def user_add_submit():
    """新增用户"""
    try:
        # 验证表单数据
        form_data = request.form.to_dict()
        errors = UserService.validate_user_data(form_data)
        
        if errors:
            return jsonify({
                'code': 1,
                'msg': '; '.join(errors)
            })
        
        # 创建用户对象
        current_user = AuthService.get_current_user()
        create_by = current_user.get('login_name', 'admin') if current_user else 'admin'
        user = UserService.create_user_from_form_data(form_data, create_by)
        
        # 保存用户
        user_id = UserService.insert_user(user)
        
        if user_id:
            return jsonify({
                'code': 0,
                'msg': '新增成功'
            })
        else:
            return jsonify({
                'code': 1,
                'msg': '新增失败'
            })
    
    except ValueError as e:
        return jsonify({
            'code': 1,
            'msg': str(e)
        })
    except Exception as e:
        logger.error(f"新增用户失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '新增失败'
        })

@system_bp.route('/user/edit/<int:user_id>')
@login_required
def user_edit_page(user_id):
    """编辑用户页面"""
    try:
        user = UserService.select_user_by_id(user_id)
        if not user:
            return render_template('error/404.html'), 404
        
        return render_template('user/edit.html', user=user)
    
    except Exception as e:
        logger.error(f"编辑用户页面失败: {e}")
        return render_template('error/500.html'), 500

@system_bp.route('/user/edit', methods=['POST'])
@login_required
@log_operation("用户管理", BusinessType.UPDATE)
def user_edit_submit():
    """修改用户"""
    try:
        # 获取表单数据
        user_id = request.form.get('userId')
        if not user_id:
            return jsonify({
                'code': 1,
                'msg': '用户ID不能为空'
            })
        
        # 获取原用户信息
        user = UserService.select_user_by_id(user_id)
        if not user:
            return jsonify({
                'code': 1,
                'msg': '用户不存在'
            })
        
        # 验证表单数据（编辑模式）
        form_data = request.form.to_dict()
        errors = UserService.validate_user_data(form_data, is_edit=True)

        if errors:
            return jsonify({
                'code': 1,
                'msg': '; '.join(errors)
            })
        
        # 更新用户信息
        current_user = AuthService.get_current_user()
        update_by = current_user.get('login_name', 'admin') if current_user else 'admin'
        user = UserService.update_user_from_form_data(user, form_data, update_by)
        
        # 保存用户
        success = UserService.update_user(user)
        
        if success:
            return jsonify({
                'code': 0,
                'msg': '修改成功'
            })
        else:
            return jsonify({
                'code': 1,
                'msg': '修改失败'
            })
    
    except ValueError as e:
        return jsonify({
            'code': 1,
            'msg': str(e)
        })
    except Exception as e:
        logger.error(f"修改用户失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '修改失败'
        })

@system_bp.route('/user/remove', methods=['POST'])
@login_required
@log_operation("用户管理", BusinessType.DELETE)
def user_remove():
    """删除用户"""
    try:
        ids = request.form.get('ids', '').strip()
        if not ids:
            return jsonify({
                'code': 1,
                'msg': '请选择要删除的用户'
            })
        
        user_ids = [int(id.strip()) for id in ids.split(',') if id.strip().isdigit()]
        if not user_ids:
            return jsonify({
                'code': 1,
                'msg': '无效的用户ID'
            })
        
        # 删除用户
        deleted_count = UserService.delete_user_by_ids(user_ids)
        
        if deleted_count > 0:
            return jsonify({
                'code': 0,
                'msg': f'成功删除{deleted_count}个用户'
            })
        else:
            return jsonify({
                'code': 1,
                'msg': '删除失败'
            })
    
    except Exception as e:
        logger.error(f"删除用户失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '删除失败'
        })

# 个人中心相关路由
@system_bp.route('/user/profile')
@login_required
def user_profile_page():
    """个人中心页面"""
    try:
        # 获取当前登录用户信息
        current_user = AuthService.get_current_user()
        if current_user and current_user.get('user_id'):
            user_id = current_user.get('user_id')
            user = UserService.select_user_by_id(user_id)
            if user:
                return render_template('user/profile.html', user=user)
            else:
                logger.error(f"用户不存在: user_id={user_id}")
                return redirect('/login')
        else:
            logger.error("无法获取当前用户信息")
            return redirect('/login')
    
    except Exception as e:
        logger.error(f"个人中心页面失败: {e}")
        return redirect('/login')

@system_bp.route('/user/profile/update', methods=['POST'])
@login_required
@log_operation("个人信息", BusinessType.UPDATE)
def user_profile_update():
    """更新个人信息"""
    try:
        current_user = AuthService.get_current_user()
        user_id = current_user.get('user_id') if current_user else None
        if not user_id:
            return jsonify({
                'code': 1,
                'msg': '用户未登录'
            })
        
        # 获取用户信息
        user = UserService.select_user_by_id(user_id)
        if not user:
            return jsonify({
                'code': 1,
                'msg': '用户不存在'
            })
        
        # 验证表单数据（个人中心编辑模式）
        form_data = request.form.to_dict()
        errors = UserService.validate_user_data(form_data, is_edit=True)

        if errors:
            return jsonify({
                'code': 1,
                'msg': '; '.join(errors)
            })
        
        # 更新用户信息
        update_by = current_user.get('login_name', 'admin')
        user = UserService.update_user_from_form_data(user, form_data, update_by)
        
        # 保存用户
        success = UserService.update_user(user)
        
        if success:
            # 更新session中的用户名
            if 'user' in session:
                session['user']['user_name'] = user.user_name
            
            return jsonify({
                'code': 0,
                'msg': '修改成功'
            })
        else:
            return jsonify({
                'code': 1,
                'msg': '修改失败'
            })
    
    except ValueError as e:
        return jsonify({
            'code': 1,
            'msg': str(e)
        })
    except Exception as e:
        logger.error(f"更新个人信息失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '修改失败'
        })

@system_bp.route('/user/profile/resetPwd', methods=['POST'])
@login_required
@log_operation("重置密码", BusinessType.UPDATE)
def user_profile_reset_pwd():
    """重置个人密码"""
    try:
        current_user = AuthService.get_current_user()
        user_id = current_user.get('user_id') if current_user else None
        old_password = request.form.get('oldPassword', '').strip()
        new_password = request.form.get('newPassword', '').strip()

        if not user_id:
            return jsonify({
                'code': 1,
                'msg': '用户未登录'
            })

        if not old_password or not new_password:
            return jsonify({
                'code': 1,
                'msg': '旧密码和新密码不能为空'
            })

        if len(new_password) < 6:
            return jsonify({
                'code': 1,
                'msg': '新密码长度不能少于6位'
            })

        # 更新密码
        success = UserService.update_user_password(user_id, old_password, new_password)

        if success:
            return jsonify({
                'code': 0,
                'msg': '密码修改成功'
            })
        else:
            return jsonify({
                'code': 1,
                'msg': '密码修改失败'
            })

    except ValueError as e:
        return jsonify({
            'code': 1,
            'msg': str(e)
        })
    except Exception as e:
        logger.error(f"修改个人密码失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '密码修改异常，请联系管理员'
        })

@system_bp.route('/user/profile/checkPassword', methods=['POST'])
@login_required
def profile_check_password():
    """检查密码是否正确"""
    try:
        password = request.form.get('password', '').strip()
        current_user = AuthService.get_current_user()
        user_id = current_user.get('user_id') if current_user else None

        if not password or not user_id:
            return jsonify(False)

        # 获取用户信息
        user = UserService.select_user_by_id(user_id)
        if not user:
            return jsonify(False)

        # 验证密码
        is_valid = user.check_password(password)
        return jsonify(is_valid)

    except Exception as e:
        logger.error(f"检查密码失败: {e}")
        return jsonify(False)

@system_bp.route('/user/profile/checkEmailUnique', methods=['POST'])
@login_required
def profile_check_email_unique():
    """检查邮箱唯一性（个人中心）"""
    try:
        email = request.form.get('email', '').strip()
        current_user = AuthService.get_current_user()
        user_id = request.form.get('userId') or (current_user.get('user_id') if current_user else None)

        if not email:
            return jsonify(True)

        is_unique = UserService.check_email_unique(email, user_id)
        return jsonify(is_unique)

    except Exception as e:
        logger.error(f"检查邮箱唯一性失败: {e}")
        return jsonify(False)

@system_bp.route('/user/profile/checkPhoneUnique', methods=['POST'])
@login_required
def profile_check_phone_unique():
    """检查手机号唯一性（个人中心）"""
    try:
        phonenumber = request.form.get('phonenumber', '').strip()
        current_user = AuthService.get_current_user()
        user_id = request.form.get('userId') or (current_user.get('user_id') if current_user else None)

        if not phonenumber:
            return jsonify(True)

        is_unique = UserService.check_phone_unique(phonenumber, user_id)
        return jsonify(is_unique)

    except Exception as e:
        logger.error(f"检查手机号唯一性失败: {e}")
        return jsonify(False)

@system_bp.route('/user/profile/updateAvatar', methods=['POST'])
@login_required
@log_operation("头像上传", BusinessType.UPDATE)
def update_avatar():
    """更新用户头像"""
    try:
        # 获取当前用户
        current_user = AuthService.get_current_user()
        if not current_user or not current_user.get('user_id'):
            return jsonify({
                'code': 1,
                'msg': '用户未登录'
            })

        user_id = current_user.get('user_id')

        # 检查是否有上传的文件
        if 'avatarfile' not in request.files:
            return jsonify({
                'code': 1,
                'msg': '请选择要上传的头像文件'
            })

        file = request.files['avatarfile']
        if file.filename == '':
            return jsonify({
                'code': 1,
                'msg': '请选择要上传的头像文件'
            })

        # 验证文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not _allowed_file(file.filename, allowed_extensions):
            return jsonify({
                'code': 1,
                'msg': '只支持 PNG、JPG、JPEG、GIF 格式的图片文件'
            })

        # 验证文件大小 (2MB)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > 2 * 1024 * 1024:  # 2MB
            return jsonify({
                'code': 1,
                'msg': '头像文件大小不能超过2MB'
            })

        # 验证文件内容是否为图片
        if not _is_valid_image(file):
            return jsonify({
                'code': 1,
                'msg': '上传的文件不是有效的图片格式'
            })

        # 生成唯一文件名
        file_ext = _get_file_extension(file.filename)
        unique_filename = f"{datetime.now().strftime('%Y%m%d')}_{uuid.uuid4().hex[:8]}.{file_ext}"

        # 确保上传目录存在
        from flask import current_app
        upload_dir = os.path.join(current_app.static_folder, 'uploads', 'avatars')
        os.makedirs(upload_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # 生成相对路径用于数据库存储
        avatar_url = f"/static/uploads/avatars/{unique_filename}"

        # 更新数据库中的用户头像
        user = UserService.select_user_by_id(user_id)
        if user:
            # 删除旧头像文件（如果存在且不是默认头像）
            if user.avatar and user.avatar.startswith('/static/uploads/avatars/'):
                old_file_path = os.path.join(current_app.static_folder, user.avatar[8:])  # 去掉 '/static/' 前缀
                if os.path.exists(old_file_path):
                    try:
                        os.remove(old_file_path)
                    except Exception as e:
                        logger.warning(f"删除旧头像文件失败: {e}")

            # 更新用户头像
            success = UserService.update_user_avatar(user_id, avatar_url)
            if success:
                # 更新session中的用户信息
                if 'user' in session:
                    session['user']['avatar'] = avatar_url

                return jsonify({
                    'code': 0,
                    'msg': '头像上传成功',
                    'data': {
                        'avatar_url': avatar_url
                    }
                })
            else:
                # 删除已上传的文件
                if os.path.exists(file_path):
                    os.remove(file_path)
                return jsonify({
                    'code': 1,
                    'msg': '头像更新失败，请重试'
                })
        else:
            # 删除已上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            return jsonify({
                'code': 1,
                'msg': '用户不存在'
            })

    except Exception as e:
        logger.error(f"头像上传失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '头像上传异常，请稍后重试'
        })

def _allowed_file(filename, allowed_extensions):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def _get_file_extension(filename):
    """获取文件扩展名"""
    return filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

def _is_valid_image(file):
    """验证文件是否为有效图片"""
    try:
        # 重置文件指针
        file.seek(0)

        # 读取文件头部分字节来检测图片格式
        header = file.read(32)
        file.seek(0)

        # 检查常见图片格式的文件头
        image_signatures = {
            b'\xff\xd8\xff': 'jpeg',
            b'\x89\x50\x4e\x47\x0d\x0a\x1a\x0a': 'png',
            b'\x47\x49\x46\x38\x37\x61': 'gif',
            b'\x47\x49\x46\x38\x39\x61': 'gif',
        }

        for signature in image_signatures:
            if header.startswith(signature):
                return True

        return False
    except Exception:
        return False

@system_bp.route('/user/importData', methods=['POST'])
@login_required
@log_operation("用户导入", BusinessType.IMPORT)
def user_import_data():
    """用户数据导入"""
    try:
        # 检查是否有上传的文件
        if 'file' not in request.files:
            return jsonify({
                'code': 1,
                'msg': '请选择要导入的文件'
            })

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'code': 1,
                'msg': '请选择要导入的文件'
            })

        # 验证文件类型
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            return jsonify({
                'code': 1,
                'msg': '只支持Excel格式的文件'
            })

        # 读取Excel文件
        import pandas as pd
        from io import BytesIO

        try:
            # 读取Excel文件
            df = pd.read_excel(BytesIO(file.read()))

            # 验证必要的列是否存在
            required_columns = ['登录账号', '用户名称']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return jsonify({
                    'code': 1,
                    'msg': f'Excel文件缺少必要的列: {", ".join(missing_columns)}'
                })

            # 获取当前用户
            current_user = AuthService.get_current_user()
            create_by = current_user.get('login_name', 'admin') if current_user else 'admin'

            success_count = 0
            error_messages = []

            # 逐行处理数据
            for index, row in df.iterrows():
                try:
                    # 构建用户数据，需要将中文值转换为数据库存储的数字值
                    sex_value = str(row.get('性别', '男')).strip()
                    sex_map = {'男': '0', '女': '1', '未知': '2'}
                    sex_code = sex_map.get(sex_value, '0')

                    status_value = str(row.get('状态', '正常')).strip()
                    status_map = {'正常': '0', '停用': '1'}
                    status_code = status_map.get(status_value, '0')

                    user_data = {
                        'loginName': str(row.get('登录账号', '')).strip(),
                        'userName': str(row.get('用户名称', '')).strip(),
                        'email': str(row.get('邮箱', '')).strip() if pd.notna(row.get('邮箱')) else '',
                        'phonenumber': str(row.get('手机号', '')).strip() if pd.notna(row.get('手机号')) else '',
                        'sex': sex_code,
                        'status': status_code,
                        'remark': str(row.get('备注', '')).strip() if pd.notna(row.get('备注')) else ''
                    }

                    # 验证数据
                    errors = UserService.validate_user_data(user_data, is_edit=False)
                    if errors:
                        error_messages.append(f"第{index+2}行: {'; '.join(errors)}")
                        continue

                    # 创建用户对象
                    user = UserService.create_user_from_form_data(user_data, create_by)

                    # 保存用户
                    user_id = UserService.insert_user(user)
                    if user_id:
                        success_count += 1
                    else:
                        error_messages.append(f"第{index+2}行: 保存失败")

                except ValueError as e:
                    error_messages.append(f"第{index+2}行: {str(e)}")
                except Exception as e:
                    error_messages.append(f"第{index+2}行: 处理失败 - {str(e)}")

            # 返回结果
            result_msg = f"导入完成，成功导入{success_count}条记录"
            if error_messages:
                result_msg += f"，失败{len(error_messages)}条"
                if len(error_messages) <= 10:  # 只显示前10个错误
                    result_msg += f"：{'; '.join(error_messages[:10])}"
                else:
                    result_msg += f"：{'; '.join(error_messages[:10])}..."

            return jsonify({
                'code': 0 if success_count > 0 else 1,
                'msg': result_msg
            })

        except Exception as e:
            return jsonify({
                'code': 1,
                'msg': f'文件解析失败: {str(e)}'
            })

    except Exception as e:
        logger.error(f"用户数据导入失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '导入失败，请稍后重试'
        })

@system_bp.route('/user/importTemplate', methods=['GET'])
@login_required
def user_import_template():
    """下载用户导入模板"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
        from io import BytesIO

        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "用户导入模板"

        # 定义表头
        headers = ['登录账号', '用户名称', '邮箱', '手机号', '性别', '状态', '备注']

        # 设置表头样式
        header_font = Font(bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border

        # 添加示例数据行
        example_data = [
            ['admin001', '管理员001', '<EMAIL>', '13800138001', '男', '正常', '系统管理员'],
            ['user001', '普通用户001', '<EMAIL>', '13800138002', '女', '正常', '普通用户'],
            ['test001', '测试用户001', '<EMAIL>', '13800138003', '男', '停用', '测试账号']
        ]

        for row_idx, row_data in enumerate(example_data, 2):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.border = border
                cell.alignment = Alignment(horizontal='left', vertical='center')

        # 设置列宽
        column_widths = [15, 20, 25, 15, 8, 10, 20]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width

        # 添加数据验证说明
        ws.cell(row=6, column=1, value="数据说明：")
        ws.cell(row=7, column=1, value="1. 登录账号：必填，不能重复，建议使用字母数字组合")
        ws.cell(row=8, column=1, value="2. 用户名称：必填，用于显示的用户姓名")
        ws.cell(row=9, column=1, value="3. 邮箱：选填，格式需正确，不能重复")
        ws.cell(row=10, column=1, value="4. 手机号：选填，11位数字，不能重复")
        ws.cell(row=11, column=1, value="5. 性别：必须填写 男/女/未知 其中之一，默认为男")
        ws.cell(row=12, column=1, value="6. 状态：必须填写 正常/停用 其中之一，默认为正常")
        ws.cell(row=13, column=1, value="7. 备注：选填，用户备注信息")
        ws.cell(row=14, column=1, value="注意：性别和状态字段必须使用指定的中文值，系统会自动转换为数据库格式")
        ws.cell(row=15, column=1, value="请在导入前删除数据说明的相关文字")

        # 设置说明文字样式
        for row in range(6, 15):
            cell = ws.cell(row=row, column=1)
            if row == 6:
                cell.font = Font(bold=True, color='FF0000')
            elif row == 14:
                cell.font = Font(bold=True, color='FF6600')  # 橙色加粗显示重要提示
            else:
                cell.font = Font(color='666666')

        # 合并说明单元格
        for row in range(7, 15):
            ws.merge_cells(f'A{row}:G{row}')

        # 保存到内存
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        # 创建响应
        from flask import make_response
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=user_import_template_{datetime.now().strftime("%Y%m%d")}.xlsx'
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        logger.info("用户导入模板下载成功")
        return response

    except Exception as e:
        logger.error(f"生成用户导入模板失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '模板生成失败，请稍后重试'
        })

@system_bp.route('/user/export', methods=['GET'])
@login_required
@log_operation("用户导出", BusinessType.EXPORT)
def user_export():
    """用户数据导出"""
    try:
        # 获取搜索条件
        search_params = {
            'login_name': request.args.get('loginName', '').strip() or None,
            'user_name': request.args.get('userName', '').strip() or None,
            'phonenumber': request.args.get('phonenumber', '').strip() or None,
            'status': request.args.get('status', '').strip() or None,
            'begin_time': request.args.get('params[beginTime]', '').strip() or None,
            'end_time': request.args.get('params[endTime]', '').strip() or None
        }

        # 查询所有符合条件的用户数据
        result = UserService.select_user_list(page_num=1, page_size=10000, search_params=search_params)
        users = result['rows']

        # 创建Excel文件
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill
        from io import BytesIO

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "用户数据"

        # 设置表头
        headers = ['用户编号', '登录账号', '用户名称', '邮箱', '手机号码', '性别', '状态', '创建时间', '备注']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        # 性别和状态映射
        sex_map = {'0': '男', '1': '女', '2': '未知'}
        status_map = {'0': '正常', '1': '停用'}

        # 填充数据
        for row, user in enumerate(users, 2):
            ws.cell(row=row, column=1, value=user.user_id)
            ws.cell(row=row, column=2, value=user.login_name)
            ws.cell(row=row, column=3, value=user.user_name)
            ws.cell(row=row, column=4, value=user.email or '')
            ws.cell(row=row, column=5, value=user.phonenumber or '')
            ws.cell(row=row, column=6, value=sex_map.get(user.sex, '未知'))
            ws.cell(row=row, column=7, value=status_map.get(user.status, '未知'))
            ws.cell(row=row, column=8, value=user.create_time.strftime('%Y-%m-%d %H:%M:%S') if user.create_time else '')
            ws.cell(row=row, column=9, value=user.remark or '')

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存到内存
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        # 创建响应
        from flask import make_response
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=users_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        return response

    except Exception as e:
        logger.error(f"用户数据导出失败: {e}")
        return jsonify({
            'code': 1,
            'msg': '导出失败，请稍后重试'
        })

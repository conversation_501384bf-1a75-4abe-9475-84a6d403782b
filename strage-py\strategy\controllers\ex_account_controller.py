"""
交易所账户控制器
迁移自: com.project.strategy.controller.ExAccountController
"""
import logging
from flask import Blueprint, render_template, request, jsonify
from services.auth_service import login_required
from strategy.service.ex_account_service_impl import ExAccountServiceImpl
from strategy.domain.entity.ex_account import ExAccount
from utils.log_decorator import log_operation, BusinessType
from datetime import datetime

logger = logging.getLogger(__name__)

# 创建蓝图
ex_account_bp = Blueprint('ex_account', __name__, url_prefix='/strategy/account')

def get_ex_account_service():
    """获取交易所账户服务实例"""
    return ExAccountServiceImpl()

@ex_account_bp.route('/')
@login_required
def index():
    """
    交易所账户列表页面
    对应: list()
    """
    return render_template('strategy/account/list.html')

@ex_account_bp.route('/list', methods=['POST'])
@login_required
def list_accounts():
    """
    查询交易所账户列表
    对应: list(@RequestBody ExAccount exAccount)
    """
    try:
        service = get_ex_account_service()
        
        # 获取请求参数
        if request.is_json:
            data = request.get_json() or {}
            account_name = data.get('accountName', '').strip()
            platform = data.get('platform', '').strip()
            state = data.get('state', '').strip()
            message = data.get('message', '').strip()
            page_num = int(data.get('pageNum', 1))
            page_size = int(data.get('pageSize', 10))
        else:
            # Form格式
            account_name = request.form.get('accountName', '').strip()
            platform = request.form.get('platform', '').strip()
            state = request.form.get('state', '').strip()
            message = request.form.get('message', '').strip()
            page_num = int(request.form.get('pageNum', 1))
            page_size = int(request.form.get('pageSize', 10))

        logger.info(f"查询参数: accountName={account_name}, platform={platform}, state={state}, message={message}")
        
        # 构建查询条件
        ex_account = ExAccount()
        if account_name:
            ex_account.account_name = account_name
        if platform:
            ex_account.platform = platform
        if state:
            ex_account.state = int(state)
        if message:
            ex_account.message = message

        # 查询列表
        account_list = service.select_ex_account_list(ex_account)
        
        # 分页处理
        total = len(account_list)
        start_index = (page_num - 1) * page_size
        end_index = start_index + page_size
        paged_list = account_list[start_index:end_index]
        
        # 转换为字典格式
        data_list = []
        for item in paged_list:
            data_dict = item.to_dict()
            data_list.append(data_dict)

        return jsonify({
            'code': 0,
            'msg': '查询成功',
            'total': total,
            'rows': data_list
        })
    except Exception as e:
        logger.error(f"查询交易所账户列表失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'查询失败: {str(e)}'
        })

@ex_account_bp.route('/add', methods=['POST'])
@login_required
@log_operation("新增交易所账户", BusinessType.INSERT)
def add_account():
    """
    新增交易所账户
    对应: addSave(@Validated ExAccount exAccount)
    """
    try:
        service = get_ex_account_service()
        
        # 获取表单数据
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()
        
        # 创建账户对象
        ex_account = ExAccount()
        ex_account.user_id = data.get('userId', 1)  # 默认用户ID，实际应从session获取
        ex_account.ex_account = data.get('exAccount', '').strip()
        ex_account.account_name = data.get('accountName', '').strip()
        ex_account.platform = data.get('platform', '').strip()
        ex_account.apikey = data.get('apiKey', '').strip()
        ex_account.secret_key = data.get('secretKey', '').strip()
        ex_account.password = data.get('password', '').strip()
        ex_account.state = int(data.get('state', 1))
        ex_account.message = data.get('message', '').strip()
        ex_account.secret_msg = data.get('secretMsg', '').strip()
        ex_account.create_time = datetime.now()
        ex_account.update_time = datetime.now()
        
        # 插入数据
        result = service.insert_ex_account(ex_account)
        
        if result:
            return jsonify({
                'code': 0,
                'msg': '新增成功'
            })
        else:
            return jsonify({
                'code': 500,
                'msg': '新增失败'
            })
    except Exception as e:
        logger.error(f"新增交易所账户失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'新增失败: {str(e)}'
        })

@ex_account_bp.route('/edit', methods=['POST'])
@login_required
@log_operation("修改交易所账户", BusinessType.UPDATE)
def edit_account():
    """
    修改交易所账户
    对应: editSave(@Validated ExAccount exAccount)
    """
    try:
        service = get_ex_account_service()
        
        # 获取表单数据
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()
        
        # 获取原有账户信息
        account_id = int(data.get('id'))
        ex_account = service.select_ex_account_by_id(account_id)
        
        if not ex_account:
            return jsonify({
                'code': 404,
                'msg': '账户不存在'
            })
        
        # 更新账户信息
        ex_account.ex_account = data.get('exAccount', '').strip()
        ex_account.account_name = data.get('accountName', '').strip()
        ex_account.platform = data.get('platform', '').strip()
        ex_account.apikey = data.get('apiKey', '').strip()
        ex_account.secret_key = data.get('secretKey', '').strip()
        ex_account.password = data.get('password', '').strip()
        ex_account.state = int(data.get('state', 1))
        ex_account.message = data.get('message', '').strip()
        ex_account.secret_msg = data.get('secretMsg', '').strip()
        ex_account.update_time = datetime.now()
        
        # 更新数据
        result = service.update_ex_account(ex_account)
        
        if result:
            return jsonify({
                'code': 0,
                'msg': '修改成功'
            })
        else:
            return jsonify({
                'code': 500,
                'msg': '修改失败'
            })
    except Exception as e:
        logger.error(f"修改交易所账户失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'修改失败: {str(e)}'
        })

@ex_account_bp.route('/remove', methods=['POST'])
@login_required
@log_operation("删除交易所账户", BusinessType.DELETE)
def remove_account():
    """
    删除交易所账户
    对应: remove(String ids)
    """
    try:
        service = get_ex_account_service()
        
        # 获取要删除的ID
        if request.is_json:
            data = request.get_json()
            ids = data.get('ids', '')
        else:
            ids = request.form.get('ids', '')
        
        if not ids:
            return jsonify({
                'code': 400,
                'msg': '请选择要删除的数据'
            })
        
        # 批量删除
        deleted_count = service.delete_ex_account_by_ids(ids)
        
        if deleted_count > 0:
            return jsonify({
                'code': 0,
                'msg': f'删除成功，共删除{deleted_count}条记录'
            })
        else:
            return jsonify({
                'code': 500,
                'msg': '删除失败'
            })
    except Exception as e:
        logger.error(f"删除交易所账户失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'删除失败: {str(e)}'
        })

@ex_account_bp.route('/get/<int:account_id>')
@login_required
def get_account(account_id):
    """
    获取账户详情
    """
    try:
        service = get_ex_account_service()
        ex_account = service.select_ex_account_by_id(account_id)
        
        if ex_account:
            return jsonify({
                'code': 0,
                'msg': '获取成功',
                'data': ex_account.to_dict()
            })
        else:
            return jsonify({
                'code': 404,
                'msg': '账户不存在'
            })
    except Exception as e:
        logger.error(f"获取账户详情失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'获取失败: {str(e)}'
        })

@ex_account_bp.route('/asset/<int:account_id>')
@login_required
def get_account_balance(account_id):
    """
    获取账户余额页面
    对应Java: @GetMapping("/asset/{id}")
    """
    try:
        service = get_ex_account_service()
        ex_account = service.select_ex_account_by_id(account_id)

        if not ex_account:
            return render_template('error.html', message='账户不存在')

        # 调用BinanceService获取余额
        from strategy.service.service_manager import ServiceManager
        service_manager = ServiceManager()
        account_balance = service_manager.get_balance(ex_account.account_name)

        return render_template('strategy/account/asset.html', accountBalance=account_balance)
    except Exception as e:
        logger.error(f"获取账户余额页面失败: {e}")
        return render_template('error.html', message=f'获取失败: {str(e)}')

@ex_account_bp.route('/asset/<int:account_id>/data')
@login_required
def get_account_balance_data(account_id):
    """
    获取账户余额数据（AJAX接口）
    """
    try:
        service = get_ex_account_service()
        ex_account = service.select_ex_account_by_id(account_id)

        if not ex_account:
            return jsonify({
                'code': 404,
                'msg': '账户不存在'
            })

        # 调用BinanceService获取余额
        from strategy.service.service_manager import ServiceManager
        service_manager = ServiceManager()
        account_balance = service_manager.get_balance(ex_account.account_name)

        balance_data = {
            'accountName': account_balance.account_name,
            'spotBalance': str(account_balance.spot_balance) if account_balance.spot_balance else '0.00',
            'futuresUBalance': str(account_balance.futures_u_balance) if account_balance.futures_u_balance else '0.00'
        }

        return jsonify({
            'code': 0,
            'msg': '获取成功',
            'data': balance_data
        })
    except Exception as e:
        logger.error(f"获取账户余额失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'获取失败: {str(e)}'
        })

@ex_account_bp.route('/asset', methods=['POST'])
@login_required
@log_operation("资产划转", BusinessType.UPDATE)
def asset_save():
    """
    账户划转
    对应Java: @PostMapping("/asset")
    """
    try:
        from decimal import Decimal
        from strategy.enums.transfer_internal_enum import TransferInternalEnum
        from strategy.service.service_manager import ServiceManager

        # 获取表单数据
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        account_name = data.get('accountName', '').strip()
        transfer_type = data.get('transferType', '').strip()
        transfer_amount_str = data.get('transferAmount', '').strip()

        # 验证参数
        if not account_name:
            return jsonify({
                'code': 400,
                'msg': '账户名称不能为空'
            })

        if not transfer_type:
            return jsonify({
                'code': 400,
                'msg': '划转类型不能为空'
            })

        if not transfer_amount_str:
            return jsonify({
                'code': 400,
                'msg': '划转金额不能为空'
            })

        try:
            transfer_amount = Decimal(transfer_amount_str)
            if transfer_amount <= 0:
                return jsonify({
                    'code': 400,
                    'msg': '划转金额必须大于0'
                })
        except (ValueError, TypeError):
            return jsonify({
                'code': 400,
                'msg': '划转金额格式不正确'
            })

        # 解析划转类型枚举
        transfer_internal_enum = TransferInternalEnum.parse_value(transfer_type)
        if not transfer_internal_enum:
            return jsonify({
                'code': 400,
                'msg': '不支持的划转类型'
            })

        # 调用BinanceService进行划转
        service_manager = ServiceManager()
        success = service_manager.transfer_asset(account_name, transfer_amount, transfer_internal_enum)

        if success:
            return jsonify({
                'code': 0,
                'msg': '划转成功'
            })
        else:
            return jsonify({
                'code': 500,
                'msg': '划转失败'
            })

    except Exception as e:
        logger.error(f"账户划转失败: {e}")
        return jsonify({
            'code': 500,
            'msg': f'划转失败: {str(e)}'
        })

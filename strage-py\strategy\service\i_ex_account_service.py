"""
交易所账户服务接口
迁移自: com.project.strategy.service.IExAccountService
"""
from abc import ABC, abstractmethod
from typing import List, Optional

from ..domain.entity.ex_account import ExAccount


class IExAccountService(ABC):
    """交易所账户服务接口"""
    
    @abstractmethod
    def select_ex_account_by_id(self, id: int) -> Optional[ExAccount]:
        """
        根据ID查询交易所账户
        
        Args:
            id: 账户ID
            
        Returns:
            交易所账户对象，不存在返回None
        """
        pass
    
    @abstractmethod
    def select_ex_account_list(self, ex_account: Optional[ExAccount] = None) -> List[ExAccount]:
        """
        查询交易所账户列表
        
        Args:
            ex_account: 查询条件对象
            
        Returns:
            交易所账户列表
        """
        pass
    
    @abstractmethod
    def insert_ex_account(self, ex_account: ExAccount) -> bool:
        """
        插入交易所账户
        
        Args:
            ex_account: 交易所账户对象
            
        Returns:
            是否插入成功
        """
        pass
    
    @abstractmethod
    def update_ex_account(self, ex_account: ExAccount) -> bool:
        """
        更新交易所账户
        
        Args:
            ex_account: 交易所账户对象
            
        Returns:
            是否更新成功
        """
        pass
    
    @abstractmethod
    def delete_ex_account_by_ids(self, ids: str) -> int:
        """
        根据ID列表批量删除交易所账户
        
        Args:
            ids: ID字符串，逗号分隔
            
        Returns:
            删除的记录数量
        """
        pass
    
    @abstractmethod
    def delete_ex_account_by_id(self, id: int) -> bool:
        """
        根据ID删除交易所账户
        
        Args:
            id: 账户ID
            
        Returns:
            是否删除成功
        """
        pass

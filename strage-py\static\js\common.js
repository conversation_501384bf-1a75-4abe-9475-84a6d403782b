/**
 * 通用js方法封装处理
 */

// 当前table相关信息
var table = {
    config: {},
    // 当前实例配置
    options: {},
    // 设置实例配置
    set: function (id) {
        if ($.common.getLength(table.config) > 1) {
            var tableId = $.common.isEmpty(id) ? $(event.currentTarget).parents(".bootstrap-table").find("table.table").attr("id") : id;
            if ($.common.isNotEmpty(tableId)) {
                table.options = table.get(tableId);
            }
        }
    },
    // 获取实例配置
    get: function (id) {
        return table.config[id];
    },
    // 记住选择实例组
    rememberSelecteds: {},
    // 记住选择ID组
    rememberSelectedIds: {}
};

// 表格封装处理
(function ($) {
    $.extend({
        // 表格封装处理
        table: {
            // 初始化表格参数
            init: function (options) {
                var defaults = {
                    id: "bootstrap-table",
                    type: 0, // 0 代表bootstrapTable 1代表bootstrapTreeTable
                    method: 'post',
                    height: undefined,
                    sidePagination: "server",
                    sortName: "",
                    sortOrder: "asc",
                    pagination: true,
                    paginationLoop: false,
                    pageSize: 10,
                    pageNumber: 1,
                    pageList: [10, 25, 50],
                    toolbar: "toolbar",
                    loadingTemplate: function (loadingMessage) {
                        return '<div class="bootstrap-table-loading">' + loadingMessage + '</div>';
                    },
                    //刷新事件
                    onRefresh: function (params) {
                        table.set($(this).attr("id"));
                    },
                    //切换视图事件
                    onToggle: function (cardView) {
                        table.set($(this).attr("id"));
                    },
                    //选择行事件
                    onCheck: function (row) {
                        table.set($(this).attr("id"));
                    },
                    //取消选择行事件
                    onUncheck: function (row) {
                        table.set($(this).attr("id"));
                    },
                    //全选事件
                    onCheckAll: function (rows) {
                        table.set($(this).attr("id"));
                    },
                    //取消全选事件
                    onUncheckAll: function (rows) {
                        table.set($(this).attr("id"));
                    },
                    //加载成功事件
                    onLoadSuccess: function (data) {
                        table.set($(this).attr("id"));
                    },
                    //加载失败事件
                    onLoadError: function (status) {
                        table.set($(this).attr("id"));
                    },
                    //双击行事件
                    onDblClickRow: function (row, $element) {
                        table.set($(this).attr("id"));
                    },
                    //单击行事件
                    onClickRow: function (row, $element) {
                        table.set($(this).attr("id"));
                    },
                    // 行样式
                    rowStyle: function (row, index) {
                        return {};
                    },
                    // 数据处理
                    responseHandler: function (res) {
                        if (typeof table_data_handler == 'function') {
                            return table_data_handler(res);
                        }
                        return {
                            "total": res.total,
                            "rows": res.rows
                        };
                    },
                };
                var options = $.extend(defaults, options);
                table.config[options.id] = options;
                table.options = options;
                $('#' + options.id).bootstrapTable({
                    id: options.id,
                    url: options.url,                                   // 请求后台的URL（*）
                    contentType: "application/x-www-form-urlencoded",   // 编码类型
                    method: options.method,                             // 请求方式（*）
                    cache: false,                                       // 设置为 false 禁用 AJAX 数据缓存， 默认为true
                    height: options.height,                             // 表格的高度
                    striped: true,                                      // 设置为 true 会有隔行变色效果
                    sortable: true,                                     // 排序
                    sortStable: true,                                   // 设置为 true 将获得稳定的排序
                    sortName: options.sortName,                         // 排序字段
                    sortOrder: options.sortOrder,                       // 排序方式  asc 或者 desc
                    pagination: options.pagination,                     // 分页
                    paginationLoop: options.paginationLoop,             // 设置为 true 启用分页条无限循环的功能。
                    pageNumber: options.pageNumber,                     // 初始化加载第一页，默认第一页
                    pageSize: options.pageSize,                         // 每页的记录行数（*）
                    pageList: options.pageList,                         // 可供选择的每页的行数（*）
                    sidePagination: options.sidePagination,             // 分页方式：client客户端分页，server服务端分页（*）
                    contextMenuTrigger: "right",                        // pc端默认值为"right"，移动端默认值为"touchstart"
                    contextMenuTriggerMobile: "touchstart",             // 移动端触发contextmenu事件，默认值为"touchstart"
                    toolbar: '#' + options.toolbar,                     // 指定工作栏
                    loadingTemplate: options.loadingTemplate,
                    responseHandler: options.responseHandler,
                    queryParams: options.queryParams,                   // 查询参数函数（重要！）
                    onRefresh: options.onRefresh,
                    onToggle: options.onToggle,
                    onCheck: options.onCheck,
                    onUncheck: options.onUncheck,
                    onCheckAll: options.onCheckAll,
                    onUncheckAll: options.onUncheckAll,
                    onClickRow: options.onClickRow,
                    onDblClickRow: options.onDblClickRow,
                    onLoadSuccess: options.onLoadSuccess,
                    onLoadError: options.onLoadError,
                    rowStyle: options.rowStyle,
                    columns: options.columns
                });
            }
        }
    });

    // 扩展通用方法
    $.extend($.common, {
        // 字符串格式化
        sprintf: function (str) {
            var args = arguments;
            var flag = true;
            var i = 1;
            str = str.replace(/%s/g, function () {
                var arg = args[i++];
                if (typeof arg === 'undefined') {
                    flag = false;
                    return '';
                }
                return arg;
            });
            return flag ? str : '';
        },
        // 数组连接
        join: function (array, separator) {
            if (!array || array.length === 0) {
                return '';
            }
            return array.join(separator || ',');
        },
        // 获取字符串长度
        getLength: function (obj) {
            if ($.isArray(obj)) {
                return obj.length;
            } else if ($.isPlainObject(obj)) {
                return Object.keys(obj).length;
            } else if (typeof obj === 'string') {
                return obj.length;
            }
            return 0;
        }
    });

    // 扩展表格方法
    $.extend($.table, {
        // 查询参数
        queryParams: function (params) {
            var curParams = {
                // 传递标准参数
                pageSize: params.limit,
                pageNum: params.offset / params.limit + 1,
                searchValue: params.search,
                orderByColumn: params.sort,
                isAsc: params.order
            };
            var currentId = $('.layui-laypage .layui-laypage-curr em').eq(1).text();
            if (currentId) {
                curParams.pageNum = currentId;
            }
            return curParams;
        },
        // 搜索
        search: function (formId, tableId, type) {
            var currentId = $('.layui-laypage .layui-laypage-curr em').eq(1).text();
            var params = {};
            if (formId) {
                params = $('#' + formId).serializeArray();
            }
            if (tableId) {
                $('#' + tableId).bootstrapTable('refresh', {
                    query: params
                });
            }
        },
        // 选择列
        selectColumns: function (column) {
            var rows = $('#bootstrap-table').bootstrapTable('getSelections');
            if (rows.length === 0) {
                return [];
            }
            return rows.map(function (row) {
                return row[column];
            });
        },
        // 选择行
        selectRows: function () {
            return $('#bootstrap-table').bootstrapTable('getSelections');
        }
    });
})(jQuery);

<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增订阅交易对</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.min.css') }}" rel="stylesheet">
</head>

<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-symbol-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">平台：</label>
                    <div class="col-sm-8">
                        <select name="platform" class="form-control" width="200">
                            <option value="BINANCE">币安</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">币对名：</label>
                    <div class="col-sm-8">
                        <input name="symbol" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">状态：</label>
                    <div class="col-sm-8">
                        <select name="state" class="form-control" width="200">
                            <option value="1">启用</option>
                            <option value="0">不启用</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">描述信息：</label>
                    <div class="col-sm-8">
                        <input name="message" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <div class="col-sm-8 col-sm-offset-3">
                        <button type="button" class="btn btn-primary" onclick="submitHandler()">确定</button>
                        <button type="button" class="btn btn-default" onclick="parent.layer.closeAll()">取消</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- jQuery -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <!-- jQuery Validate -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/localization/messages_zh.min.js"></script>
    <!-- 通用JS -->
    <script src="{{ url_for('static', filename='ruoyi/js/ry-ui.js') }}"></script>

    <script>
        var prefix = "/strategy/symbol";

        $("#form-symbol-add").validate({
            focusCleanup: true,
            rules: {
                platform: {
                    required: true
                },
                symbol: {
                    required: true
                },
                state: {
                    required: true
                }
            },
            messages: {
                platform: {
                    required: "请选择平台"
                },
                symbol: {
                    required: "请输入币对名"
                },
                state: {
                    required: "请选择状态"
                }
            }
        });

        function submitHandler() {
            if ($("#form-symbol-add").valid()) {
                $.ajax({
                    url: prefix + "/add",
                    type: "POST",
                    data: $('#form-symbol-add').serialize(),
                    dataType: "json",
                    success: function (result) {
                        if (result.code == 0) {
                            parent.layer.msg("新增成功", { icon: 1 });
                            parent.layer.closeAll();
                            parent.location.reload();
                        } else {
                            parent.layer.msg(result.msg, { icon: 2 });
                        }
                    },
                    error: function () {
                        parent.layer.msg("系统错误", { icon: 2 });
                    }
                });
            }
        }
    </script>
</body>

</html>
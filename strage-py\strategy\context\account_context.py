"""
账户上下文管理类
迁移自: com.project.strategy.context.AccountContext
"""
import logging
from typing import Dict, List, Optional, Any
from ..enums import ExchangeEnum, InstrumentEnum


logger = logging.getLogger(__name__)


class IAccountService:
    """账户服务接口（基类）"""
    
    def is_support(self) -> bool:
        """是否支持"""
        raise NotImplementedError
    
    def get_exchange(self) -> ExchangeEnum:
        """获取交易所"""
        raise NotImplementedError
    
    def get_instrument(self) -> InstrumentEnum:
        """获取交易工具"""
        raise NotImplementedError
    
    def get_account_name(self) -> str:
        """获取账户名称"""
        raise NotImplementedError




class AccountContext:
    """账户上下文管理类"""
    
    def __init__(self):
        """初始化账户上下文"""
        self._cached_client: Dict[str, IAccountService] = {}
        self._application_context: Optional[Any] = None
        logger.info("AccountContext initialized")
    
    def get_platform_client(self, exchange: ExchangeEnum) -> List[IAccountService]:
        """
        获取指定交易所的平台客户端列表
        
        Args:
            exchange: 交易所枚举
            
        Returns:
            账户服务列表
        """
        result = []
        
        for bean_name, client in self._cached_client.items():
            try:
                if client.is_support() and client.get_exchange() == exchange:
                    result.append(client)
            except Exception as e:
                logger.error(f"检查客户端 {bean_name} 时出错: {e}")
        
        logger.debug(f"找到 {len(result)} 个 {exchange.get_name()} 交易所客户端")
        return result
    
    def get_account_client(self, exchange_enum: ExchangeEnum, 
                          instrument_enum: InstrumentEnum, 
                          account_name: str) -> Optional[IAccountService]:
        """
        获取指定条件的账户客户端
        
        Args:
            exchange_enum: 交易所枚举
            instrument_enum: 交易工具枚举
            account_name: 账户名称
            
        Returns:
            匹配的账户服务，未找到返回None
        """
        for bean_name, client in self._cached_client.items():
            try:
                if (client.is_support() and 
                    client.get_exchange() == exchange_enum and
                    client.get_instrument() == instrument_enum and
                    account_name == client.get_account_name()):
                    
                    logger.debug(f"找到匹配的账户客户端: {bean_name}")
                    return client
                    
            except Exception as e:
                logger.error(f"检查客户端 {bean_name} 时出错: {e}")
        
        logger.warning(f"未找到匹配的账户客户端: {exchange_enum.get_name()}-{instrument_enum.get_value()}-{account_name}")
        return None
    
    def add_account_client(self, bean_name: str, new_client: IAccountService) -> None:
        """
        添加账户客户端
        
        Args:
            bean_name: Bean名称
            new_client: 新的账户服务
        """
        if not isinstance(new_client, IAccountService):
            raise ValueError("new_client must be an instance of IAccountService")
        
        self._cached_client[bean_name] = new_client
        logger.info(f"添加账户客户端: {bean_name}")
    
    def remove_account_client(self, bean_name: str) -> bool:
        """
        移除账户客户端
        
        Args:
            bean_name: Bean名称
            
        Returns:
            是否成功移除
        """
        if bean_name in self._cached_client:
            del self._cached_client[bean_name]
            logger.info(f"移除账户客户端: {bean_name}")
            return True
        return False
    
    def get_all_clients(self) -> Dict[str, IAccountService]:
        """
        获取所有账户客户端
        
        Returns:
            所有账户客户端字典
        """
        return self._cached_client.copy()
    
    def get_client_count(self) -> int:
        """
        获取客户端数量
        
        Returns:
            客户端数量
        """
        return len(self._cached_client)
    
    def get_supported_clients(self) -> Dict[str, IAccountService]:
        """
        获取所有支持的客户端
        
        Returns:
            支持的客户端字典
        """
        supported = {}
        for bean_name, client in self._cached_client.items():
            try:
                if client.is_support():
                    supported[bean_name] = client
            except Exception as e:
                logger.error(f"检查客户端 {bean_name} 支持状态时出错: {e}")
        
        return supported
    
    def refresh_clients(self, clients: Dict[str, IAccountService]) -> None:
        """
        刷新客户端缓存
        
        Args:
            clients: 新的客户端字典
        """
        self._cached_client = clients.copy()
        logger.info(f"刷新客户端缓存，共 {len(self._cached_client)} 个客户端")
    
    def set_application_context(self, application_context: Any) -> None:
        """
        设置应用上下文
        
        Args:
            application_context: 应用上下文
        """
        self._application_context = application_context
        logger.debug("设置应用上下文")
    
    def get_context(self) -> Optional[Any]:
        """
        获取应用上下文
        
        Returns:
            应用上下文
        """
        return self._application_context
    
    def after_properties_set(self) -> None:
        """
        属性设置后的初始化方法
        """
        # 在Python中，我们可能需要手动注册客户端
        # 或者从配置中加载客户端
        logger.info("AccountContext 属性设置完成")
    
    def get_clients_by_exchange(self, exchange: ExchangeEnum) -> Dict[str, IAccountService]:
        """
        按交易所获取客户端
        
        Args:
            exchange: 交易所枚举
            
        Returns:
            指定交易所的客户端字典
        """
        result = {}
        for bean_name, client in self._cached_client.items():
            try:
                if client.is_support() and client.get_exchange() == exchange:
                    result[bean_name] = client
            except Exception as e:
                logger.error(f"检查客户端 {bean_name} 时出错: {e}")
        
        return result
    
    def get_clients_by_instrument(self, instrument: InstrumentEnum) -> Dict[str, IAccountService]:
        """
        按交易工具获取客户端
        
        Args:
            instrument: 交易工具枚举
            
        Returns:
            指定交易工具的客户端字典
        """
        result = {}
        for bean_name, client in self._cached_client.items():
            try:
                if client.is_support() and client.get_instrument() == instrument:
                    result[bean_name] = client
            except Exception as e:
                logger.error(f"检查客户端 {bean_name} 时出错: {e}")
        
        return result
    
    def validate_clients(self) -> Dict[str, bool]:
        """
        验证所有客户端的有效性
        
        Returns:
            客户端验证结果字典
        """
        validation_results = {}
        
        for bean_name, client in self._cached_client.items():
            try:
                # 基本验证
                is_valid = (
                    client.is_support() and
                    client.get_exchange() is not None and
                    client.get_instrument() is not None and
                    client.get_account_name() is not None and
                    len(client.get_account_name().strip()) > 0
                )
                validation_results[bean_name] = is_valid
                
            except Exception as e:
                logger.error(f"验证客户端 {bean_name} 时出错: {e}")
                validation_results[bean_name] = False
        
        return validation_results
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取账户上下文状态

        Returns:
            状态信息字典
        """
        return {
            'total_clients': len(self._cached_client),
            'client_keys': list(self._cached_client.keys()),
            'active_clients': len([client for client in self._cached_client.values() if client is not None])
        }

    def clear_all_clients(self) -> None:
        """
        清除所有客户端
        """
        self._cached_client.clear()
        logger.info("已清除所有账户客户端")

    def __str__(self) -> str:
        """字符串表示"""
        return f"AccountContext(clients={len(self._cached_client)})"

    def __repr__(self) -> str:
        """对象表示"""
        return f"AccountContext(cached_clients={list(self._cached_client.keys())})"


# 全局账户上下文实例
account_context = AccountContext()

"""
全局缓存类
迁移自: com.project.strategy.GlobalCache
"""
import threading
from typing import Dict, Any, Optional
from collections import defaultdict
import json
import logging


logger = logging.getLogger(__name__)


class GlobalCache:
    """全局缓存类"""
    
    # 深度数据缓存 - 存储订单簿深度信息
    DEPTH: Dict[str, Dict[str, Any]] = {}
    
    # 价格数据缓存 - 存储实时价格信息
    PRICE: Dict[str, Dict[str, Any]] = {}
    
    # 策略跟踪缓存 - 存储策略跟踪信息
    STRATEGY_TRAILING: Dict[str, Dict[int, Any]] = defaultdict(dict)
    
    # 线程锁
    _depth_lock = threading.RLock()
    _price_lock = threading.RLock()
    _strategy_lock = threading.RLock()
    
    @classmethod
    def put_depth(cls, key: str, data: Dict[str, Any]) -> None:
        """
        存储深度数据
        
        Args:
            key: 缓存键
            data: 深度数据
        """
        with cls._depth_lock:
            cls.DEPTH[key] = data.copy() if data else {}
            logger.debug(f"存储深度数据: {key}")
    
    @classmethod
    def get_depth(cls, key: str) -> Optional[Dict[str, Any]]:
        """
        获取深度数据
        
        Args:
            key: 缓存键
            
        Returns:
            深度数据，不存在返回None
        """
        with cls._depth_lock:
            return cls.DEPTH.get(key)
    
    @classmethod
    def remove_depth(cls, key: str) -> bool:
        """
        移除深度数据
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功移除
        """
        with cls._depth_lock:
            if key in cls.DEPTH:
                del cls.DEPTH[key]
                logger.debug(f"移除深度数据: {key}")
                return True
            return False
    
    @classmethod
    def clear_depth(cls) -> None:
        """清空所有深度数据"""
        with cls._depth_lock:
            cls.DEPTH.clear()
            logger.info("清空所有深度数据")
    
    @classmethod
    def put_price(cls, key: str, data: Dict[str, Any]) -> None:
        """
        存储价格数据
        
        Args:
            key: 缓存键
            data: 价格数据
        """
        with cls._price_lock:
            cls.PRICE[key] = data.copy() if data else {}
            logger.debug(f"存储价格数据: {key}")
    
    @classmethod
    def get_price(cls, key: str) -> Optional[Dict[str, Any]]:
        """
        获取价格数据
        
        Args:
            key: 缓存键
            
        Returns:
            价格数据，不存在返回None
        """
        with cls._price_lock:
            return cls.PRICE.get(key)
    
    @classmethod
    def remove_price(cls, key: str) -> bool:
        """
        移除价格数据
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功移除
        """
        with cls._price_lock:
            if key in cls.PRICE:
                del cls.PRICE[key]
                logger.debug(f"移除价格数据: {key}")
                return True
            return False
    
    @classmethod
    def clear_price(cls) -> None:
        """清空所有价格数据"""
        with cls._price_lock:
            cls.PRICE.clear()
            logger.info("清空所有价格数据")
    
    @classmethod
    def put_strategy_trailing(cls, strategy_key: str, trailing_id: int, trailing_profit: Any) -> None:
        """
        存储策略跟踪数据
        
        Args:
            strategy_key: 策略键
            trailing_id: 跟踪ID
            trailing_profit: 跟踪盈利对象
        """
        with cls._strategy_lock:
            if strategy_key not in cls.STRATEGY_TRAILING:
                cls.STRATEGY_TRAILING[strategy_key] = {}
            cls.STRATEGY_TRAILING[strategy_key][trailing_id] = trailing_profit
            logger.debug(f"存储策略跟踪数据: {strategy_key}-{trailing_id}")
    
    @classmethod
    def get_strategy_trailing(cls, strategy_key: str, trailing_id: int) -> Optional[Any]:
        """
        获取策略跟踪数据
        
        Args:
            strategy_key: 策略键
            trailing_id: 跟踪ID
            
        Returns:
            跟踪盈利对象，不存在返回None
        """
        with cls._strategy_lock:
            strategy_map = cls.STRATEGY_TRAILING.get(strategy_key)
            if strategy_map:
                return strategy_map.get(trailing_id)
            return None
    
    @classmethod
    def get_strategy_trailing_map(cls, strategy_key: str) -> Optional[Dict[int, Any]]:
        """
        获取策略的所有跟踪数据
        
        Args:
            strategy_key: 策略键
            
        Returns:
            跟踪数据字典，不存在返回None
        """
        with cls._strategy_lock:
            return cls.STRATEGY_TRAILING.get(strategy_key)
    
    @classmethod
    def remove_strategy_trailing(cls, strategy_key: str, trailing_id: int) -> bool:
        """
        移除策略跟踪数据
        
        Args:
            strategy_key: 策略键
            trailing_id: 跟踪ID
            
        Returns:
            是否成功移除
        """
        with cls._strategy_lock:
            strategy_map = cls.STRATEGY_TRAILING.get(strategy_key)
            if strategy_map and trailing_id in strategy_map:
                del strategy_map[trailing_id]
                logger.debug(f"移除策略跟踪数据: {strategy_key}-{trailing_id}")
                
                # 如果策略下没有跟踪数据了，移除整个策略键
                if not strategy_map:
                    del cls.STRATEGY_TRAILING[strategy_key]
                    logger.debug(f"移除空策略键: {strategy_key}")
                
                return True
            return False
    
    @classmethod
    def remove_strategy_trailing_all(cls, strategy_key: str) -> bool:
        """
        移除策略的所有跟踪数据
        
        Args:
            strategy_key: 策略键
            
        Returns:
            是否成功移除
        """
        with cls._strategy_lock:
            if strategy_key in cls.STRATEGY_TRAILING:
                del cls.STRATEGY_TRAILING[strategy_key]
                logger.debug(f"移除策略所有跟踪数据: {strategy_key}")
                return True
            return False
    
    @classmethod
    def clear_strategy_trailing(cls) -> None:
        """清空所有策略跟踪数据"""
        with cls._strategy_lock:
            cls.STRATEGY_TRAILING.clear()
            logger.info("清空所有策略跟踪数据")
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, int]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计字典
        """
        with cls._depth_lock, cls._price_lock, cls._strategy_lock:
            return {
                'depth_count': len(cls.DEPTH),
                'price_count': len(cls.PRICE),
                'strategy_count': len(cls.STRATEGY_TRAILING),
                'total_trailing_count': sum(len(v) for v in cls.STRATEGY_TRAILING.values())
            }
    
    @classmethod
    def clear_all(cls) -> None:
        """清空所有缓存数据"""
        cls.clear_depth()
        cls.clear_price()
        cls.clear_strategy_trailing()
        logger.info("清空所有缓存数据")
    
    @classmethod
    def export_cache_data(cls) -> Dict[str, Any]:
        """
        导出缓存数据（用于备份或调试）
        
        Returns:
            包含所有缓存数据的字典
        """
        with cls._depth_lock, cls._price_lock, cls._strategy_lock:
            return {
                'depth': dict(cls.DEPTH),
                'price': dict(cls.PRICE),
                'strategy_trailing': dict(cls.STRATEGY_TRAILING),
                'stats': cls.get_cache_stats()
            }
    
    @classmethod
    def import_cache_data(cls, data: Dict[str, Any]) -> bool:
        """
        导入缓存数据（用于恢复）

        Args:
            data: 缓存数据字典

        Returns:
            是否成功导入
        """
        try:
            with cls._depth_lock, cls._price_lock, cls._strategy_lock:
                if 'depth' in data:
                    cls.DEPTH.update(data['depth'])
                if 'price' in data:
                    cls.PRICE.update(data['price'])
                if 'strategy_trailing' in data:
                    cls.STRATEGY_TRAILING.update(data['strategy_trailing'])

                logger.info("成功导入缓存数据")
                return True

        except Exception as e:
            logger.error(f"导入缓存数据失败: {e}")
            return False

# 测试函数
def main():
    """测试GlobalCache功能"""
    # 测试深度数据
    print("测试深度数据...")
    depth_data = {
        'asks': [['50000', '1.0'], ['50001', '2.0']],
        'bids': [['49999', '1.5'], ['49998', '2.5']],
        'timestamp': 1640995200000
    }
    
    GlobalCache.put_depth('BTCUSDT', depth_data)
    retrieved_depth = GlobalCache.get_depth('BTCUSDT')
    print(f"存储和获取深度数据成功: {retrieved_depth is not None}")
    
    # 测试价格数据
    print("\n测试价格数据...")
    price_data = {
        'symbol': 'BTCUSDT',
        'price': '50000.00',
        'timestamp': 1640995200000
    }
    
    GlobalCache.put_price('BTCUSDT', price_data)
    retrieved_price = GlobalCache.get_price('BTCUSDT')
    print(f"存储和获取价格数据成功: {retrieved_price is not None}")
    
    # 测试策略跟踪数据
    print("\n测试策略跟踪数据...")
    from strategy.domain.entity.trailing_profit import TrailingProfit
    from decimal import Decimal
    
    trailing_profit = TrailingProfit(
        id=1,
        symbol='BTCUSDT',
        amount=Decimal('1.0'),
        position_side='long'
    )
    
    GlobalCache.put_strategy_trailing('strategy1', 1, trailing_profit)
    retrieved_trailing = GlobalCache.get_strategy_trailing('strategy1', 1)
    print(f"存储和获取策略跟踪数据成功: {retrieved_trailing is not None}")
    
    # 测试缓存统计
    print("\n缓存统计:")
    stats = GlobalCache.get_cache_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()

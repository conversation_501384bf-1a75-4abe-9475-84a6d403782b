"""
Binance交易对领域模型
迁移自: com.project.strategy.domain.BinanceSymbol
"""
from dataclasses import dataclass
from typing import Optional
from datetime import datetime


@dataclass
class BinanceSymbol:
    """Binance交易对"""
    
    id: Optional[int] = None
    account_id: Optional[int] = None
    symbol: Optional[str] = None
    symbol_bn: Optional[str] = None
    min: Optional[float] = None
    max: Optional[float] = None
    leverage: Optional[int] = None
    status: Optional[int] = None
    update_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        if self.id is not None and not isinstance(self.id, int):
            self.id = int(self.id) if str(self.id).isdigit() else None
            
        if self.account_id is not None and not isinstance(self.account_id, int):
            self.account_id = int(self.account_id) if str(self.account_id).isdigit() else None
            
        if self.min is not None and not isinstance(self.min, float):
            try:
                self.min = float(self.min)
            except (ValueError, TypeError):
                self.min = None
                
        if self.max is not None and not isinstance(self.max, float):
            try:
                self.max = float(self.max)
            except (ValueError, TypeError):
                self.max = None
                
        if self.leverage is not None and not isinstance(self.leverage, int):
            self.leverage = int(self.leverage) if str(self.leverage).isdigit() else None
            
        if self.status is not None and not isinstance(self.status, int):
            self.status = int(self.status) if str(self.status).isdigit() else None
    
    def get_tag(self) -> str:
        """获取标签（账户ID-交易对）"""
        if self.account_id is not None and self.symbol is not None:
            return f"{self.account_id}-{self.symbol}"
        return ""
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'BinanceSymbol':
        """从字典创建实例"""
        # 处理datetime字段
        if 'update_time' in data and data['update_time']:
            if isinstance(data['update_time'], str):
                try:
                    data['update_time'] = datetime.fromisoformat(data['update_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['update_time'] = None
        
        return cls(**data)
    
    @classmethod
    def builder(cls) -> 'BinanceSymbolBuilder':
        """创建构建器"""
        return BinanceSymbolBuilder()
    
    def is_valid(self) -> bool:
        """验证交易对是否有效"""
        return (
            self.symbol is not None and 
            len(self.symbol.strip()) > 0 and
            self.account_id is not None
        )
    
    def is_active(self) -> bool:
        """检查交易对是否激活"""
        return self.status == 1
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"BinanceSymbol(id={self.id}, symbol='{self.symbol}', account_id={self.account_id})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"BinanceSymbol(id={self.id}, account_id={self.account_id}, "
                f"symbol='{self.symbol}', leverage={self.leverage}, status={self.status})")
    
    def __eq__(self, other) -> bool:
        """相等性比较"""
        if not isinstance(other, BinanceSymbol):
            return False
        return (
            self.id == other.id and
            self.account_id == other.account_id and
            self.symbol == other.symbol and
            self.symbol_bn == other.symbol_bn and
            self.min == other.min and
            self.max == other.max and
            self.leverage == other.leverage and
            self.status == other.status and
            self.update_time == other.update_time
        )
    
    def __hash__(self) -> int:
        """哈希值计算"""
        return hash((
            self.id, self.account_id, self.symbol, self.symbol_bn,
            self.min, self.max, self.leverage, self.status, self.update_time
        ))


class BinanceSymbolBuilder:
    """BinanceSymbol构建器"""
    
    def __init__(self):
        self._data = {}
    
    def id(self, id: int) -> 'BinanceSymbolBuilder':
        self._data['id'] = id
        return self
    
    def account_id(self, account_id: int) -> 'BinanceSymbolBuilder':
        self._data['account_id'] = account_id
        return self
    
    def symbol(self, symbol: str) -> 'BinanceSymbolBuilder':
        self._data['symbol'] = symbol
        return self
    
    def symbol_bn(self, symbol_bn: str) -> 'BinanceSymbolBuilder':
        self._data['symbol_bn'] = symbol_bn
        return self
    
    def min(self, min: float) -> 'BinanceSymbolBuilder':
        self._data['min'] = min
        return self
    
    def max(self, max: float) -> 'BinanceSymbolBuilder':
        self._data['max'] = max
        return self
    
    def leverage(self, leverage: int) -> 'BinanceSymbolBuilder':
        self._data['leverage'] = leverage
        return self
    
    def status(self, status: int) -> 'BinanceSymbolBuilder':
        self._data['status'] = status
        return self
    
    def update_time(self, update_time: datetime) -> 'BinanceSymbolBuilder':
        self._data['update_time'] = update_time
        return self
    
    def build(self) -> BinanceSymbol:
        """构建BinanceSymbol实例"""
        return BinanceSymbol(**self._data)

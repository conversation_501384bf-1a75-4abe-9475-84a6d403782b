"""
订单类型枚举类
迁移自: com.project.strategy.enums.OrderTypeEnum
"""
from enum import Enum
from typing import Optional


class OrderTypeEnum(Enum):
    """订单类型枚举"""
    
    BN_MARKET = (0, "MARKET", "市价单")
    BN_LIMIT = (1, "LIMIT", "限价单")
    UNKNOWN = (-1, "null", "未知")
    
    def __init__(self, code: int, value: str, desc: str):
        """
        初始化订单类型枚举
        
        Args:
            code: 类型代码
            value: 类型值
            desc: 类型描述
        """
        self.code = code
        self.type_value = value  # 使用type_value避免与Enum.value冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取类型代码"""
        return self.code
    
    def get_value(self) -> str:
        """获取类型值"""
        return self.type_value
    
    def get_desc(self) -> str:
        """获取类型描述"""
        return self.desc
    
    @classmethod
    def parse_code(cls, code: int) -> Optional['OrderTypeEnum']:
        """
        根据代码解析订单类型枚举
        
        Args:
            code: 类型代码
            
        Returns:
            对应的订单类型枚举，如果未找到则返回None
        """
        for order_type_enum in cls:
            if order_type_enum.get_code() == code:
                return order_type_enum
        return None
    
    @classmethod
    def parse_value(cls, value: str) -> Optional['OrderTypeEnum']:
        """
        根据值解析订单类型枚举
        
        Args:
            value: 类型值
            
        Returns:
            对应的订单类型枚举，如果未找到则返回None
        """
        for order_type_enum in cls:
            if order_type_enum.get_value() == value:
                return order_type_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_value}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"OrderTypeEnum.{self.name}"

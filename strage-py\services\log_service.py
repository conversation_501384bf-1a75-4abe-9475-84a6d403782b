"""
日志服务类
处理操作日志和登录日志相关的业务逻辑
"""
import logging
from datetime import datetime
from dao.log_dao import OperLogDAO, LoginLogDAO
from models.log import OperLog, LoginLog

logger = logging.getLogger(__name__)

class OperLogService:
    """操作日志服务类"""
    
    @staticmethod
    def select_oper_log_list(page=1, size=10, title=None, oper_name=None, oper_ip=None,
                           status=None, business_types=None, begin_time=None, end_time=None):
        """分页查询操作日志列表"""
        try:
            return OperLogDAO.select_oper_log_list(
                page=page, size=size, title=title, oper_name=oper_name,
                oper_ip=oper_ip, status=status, business_types=business_types,
                begin_time=begin_time, end_time=end_time
            )
        except Exception as e:
            logger.error(f"查询操作日志列表失败: {e}")
            return {
                'total': 0,
                'rows': []
            }
    
    @staticmethod
    def export_oper_log(title=None, oper_name=None, oper_ip=None, status=None,
                       business_types=None, begin_time=None, end_time=None):
        """导出操作日志"""
        try:
            return OperLogDAO.export_oper_log(
                title=title, oper_name=oper_name, oper_ip=oper_ip,
                status=status, business_types=business_types,
                begin_time=begin_time, end_time=end_time
            )
        except Exception as e:
            logger.error(f"导出操作日志失败: {e}")
            return []

    @staticmethod
    def select_oper_log_by_id(oper_id):
        """根据ID查询操作日志"""
        try:
            return OperLogDAO.select_oper_log_by_id(oper_id)
        except Exception as e:
            logger.error(f"根据ID查询操作日志失败: {e}")
            return None

    @staticmethod
    def insert_oper_log(title, business_type, method, request_method, oper_name,
                       oper_url, oper_ip, oper_location=None, oper_param=None,
                       json_result=None, status=0, error_msg=None, cost_time=0):
        """插入操作日志"""
        try:
            from models.log import OperLog
            from datetime import datetime
            from utils.ip_location import get_ip_location

            # 创建操作日志对象
            oper_log = OperLog()
            oper_log.title = title
            oper_log.business_type = business_type
            oper_log.method = method
            oper_log.request_method = request_method
            oper_log.oper_name = oper_name
            oper_log.oper_url = oper_url
            oper_log.oper_ip = oper_ip
            oper_log.oper_location = oper_location or get_ip_location(oper_ip)
            oper_log.oper_param = oper_param
            oper_log.json_result = json_result
            oper_log.status = status
            oper_log.error_msg = error_msg
            oper_log.oper_time = datetime.now()
            oper_log.cost_time = cost_time

            # 插入数据库
            return OperLogDAO.insert_oper_log(oper_log)

        except Exception as e:
            logger.error(f"插入操作日志失败: {e}")
            return False
    
    @staticmethod
    def delete_oper_log_by_ids(oper_ids):
        """批量删除操作日志"""
        try:
            if not oper_ids:
                return 0
            
            return OperLogDAO.delete_oper_log_by_ids(oper_ids)
        except Exception as e:
            logger.error(f"批量删除操作日志失败: {e}")
            raise
    
    @staticmethod
    def clean_oper_log():
        """清空操作日志"""
        try:
            return OperLogDAO.clean_oper_log()
        except Exception as e:
            logger.error(f"清空操作日志失败: {e}")
            raise


class LoginLogService:
    """登录日志服务类"""
    
    @staticmethod
    def select_login_log_list(page=1, size=10, login_name=None, status=None,
                            begin_time=None, end_time=None):
        """分页查询登录日志列表"""
        try:
            return LoginLogDAO.select_login_log_list(
                page=page, size=size, login_name=login_name,
                status=status, begin_time=begin_time, end_time=end_time
            )
        except Exception as e:
            logger.error(f"查询登录日志列表失败: {e}")
            return {
                'total': 0,
                'rows': []
            }
    
    @staticmethod
    def delete_login_log_by_ids(info_ids):
        """批量删除登录日志"""
        try:
            if not info_ids:
                return 0
            
            return LoginLogDAO.delete_login_log_by_ids(info_ids)
        except Exception as e:
            logger.error(f"批量删除登录日志失败: {e}")
            raise
    
    @staticmethod
    def clean_login_log():
        """清空登录日志"""
        try:
            return LoginLogDAO.clean_login_log()
        except Exception as e:
            logger.error(f"清空登录日志失败: {e}")
            raise

"""
基础DAO类
提供通用的数据访问方法
"""
import logging
from utils.database import db_manager

logger = logging.getLogger(__name__)

class BaseDAO:
    """基础DAO类，提供通用的数据访问方法"""
    
    @staticmethod
    def execute_query(sql, params=None):
        """执行查询SQL"""
        try:
            return db_manager.execute_query(sql, params)
        except Exception as e:
            logger.error(f"执行查询失败: {sql}, 参数: {params}, 错误: {e}")
            raise
    
    @staticmethod
    def execute_update(sql, params=None):
        """执行更新SQL"""
        try:
            return db_manager.execute_update(sql, params)
        except Exception as e:
            logger.error(f"执行更新失败: {sql}, 参数: {params}, 错误: {e}")
            raise
    
    @staticmethod
    def execute_insert(sql, params=None):
        """执行插入SQL，返回插入的ID"""
        try:
            return db_manager.execute_insert(sql, params)
        except Exception as e:
            logger.error(f"执行插入失败: {sql}, 参数: {params}, 错误: {e}")
            raise

    @staticmethod
    def execute_batch_insert(sql, params_list):
        """执行批量插入SQL"""
        try:
            return db_manager.execute_batch_insert(sql, params_list)
        except Exception as e:
            logger.error(f"执行批量插入失败: {sql}, 参数: {params_list}, 错误: {e}")
            raise
    
    @staticmethod
    def build_where_clause(conditions, params):
        """构建WHERE子句"""
        if not conditions:
            return "", []
        
        where_parts = []
        where_params = []
        
        for condition, value in conditions.items():
            if value is not None:
                if condition.endswith('_like'):
                    # 模糊查询
                    field = condition[:-5]  # 去掉_like后缀
                    where_parts.append(f"{field} LIKE %s")
                    where_params.append(f"%{value}%")
                elif condition.endswith('_in'):
                    # IN查询
                    field = condition[:-3]  # 去掉_in后缀
                    if isinstance(value, (list, tuple)) and value:
                        placeholders = ','.join(['%s'] * len(value))
                        where_parts.append(f"{field} IN ({placeholders})")
                        where_params.extend(value)
                elif condition.endswith('_gte'):
                    # 大于等于
                    field = condition[:-4]  # 去掉_gte后缀
                    where_parts.append(f"{field} >= %s")
                    where_params.append(value)
                elif condition.endswith('_lte'):
                    # 小于等于
                    field = condition[:-4]  # 去掉_lte后缀
                    where_parts.append(f"{field} <= %s")
                    where_params.append(value)
                else:
                    # 等于查询
                    where_parts.append(f"{condition} = %s")
                    where_params.append(value)
        
        where_clause = " AND ".join(where_parts) if where_parts else ""
        return where_clause, where_params
    
    @staticmethod
    def build_pagination_sql(base_sql, page=1, size=10):
        """构建分页SQL"""
        offset = (page - 1) * size
        return f"{base_sql} LIMIT %s OFFSET %s", [size, offset]
    
    @staticmethod
    def get_total_count(table_name, where_clause="", where_params=None):
        """获取总记录数"""
        sql = f"SELECT COUNT(*) as total FROM {table_name}"
        if where_clause:
            sql += f" WHERE {where_clause}"
        
        result = BaseDAO.execute_query(sql, where_params or [])
        return result[0]['total'] if result else 0

"""
订单状态枚举类
迁移自: com.project.strategy.enums.OrderStateEnum
"""
from enum import Enum
from typing import Optional


class OrderStateEnum(Enum):
    """订单状态枚举"""
    
    FAIL = (-2, "FAIL", "失败")
    UNAVAILABLE = (-1, "UNAVAILABLE", "不启用")
    UNHANDLE = (0, "UNHANDLE", "待处理")
    HOLDING = (1, "HOLDING", "持仓中")
    LIMITING = (2, "LIMITING", "挂单中")
    COMPLETED = (3, "COMPLETED", "已完成")
    TAKE_PROFIT = (4, "TAKE_PROFIT", "已止盈")
    STOP_LOSS = (5, "STOP_LOSS", "已止损")
    STOP_MANUAL = (6, "STOP_MANUAL", "手动停止")
    FOLLOW_READY = (7, "FOLLOW_READY", "跟单策略准备开仓")
    UNKNOWN = (999, "null", "未知")
    
    def __init__(self, code: int, value: str, desc: str):
        """
        初始化订单状态枚举
        
        Args:
            code: 状态代码
            value: 状态值
            desc: 状态描述
        """
        self.code = code
        self.type_value = value  # 使用type_value避免与Enum.value冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取状态代码"""
        return self.code
    
    def get_value(self) -> str:
        """获取状态值"""
        return self.type_value
    
    def get_desc(self) -> str:
        """获取状态描述"""
        return self.desc
    
    @classmethod
    def parse_value(cls, value: str) -> Optional['OrderStateEnum']:
        """
        根据状态值解析订单状态枚举
        
        Args:
            value: 状态值
            
        Returns:
            对应的订单状态枚举，如果未找到则返回None
        """
        for order_enum in cls:
            if order_enum.get_value() == value:
                return order_enum
        return None
    
    @classmethod
    def parse_code(cls, code: int) -> Optional['OrderStateEnum']:
        """
        根据状态代码解析订单状态枚举
        
        Args:
            code: 状态代码
            
        Returns:
            对应的订单状态枚举，如果未找到则返回None
        """
        for order_enum in cls:
            if order_enum.get_code() == code:
                return order_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_value}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"OrderStateEnum.{self.name}"

"""
策略交易模块服务层
包含所有业务服务类
"""

from .abstract_account_common import AbstractAccountCommon
from .account_bean_factory import AccountBeanFactory
from .binance_futures_service import BinanceFuturesService
from .binance_service import BinanceService
from .binance_spot_service import BinanceSpotService
from .binance_stream_service import BinanceStreamService
from .ex_account_service_impl import ExAccountServiceImpl
from .i_account_service import IAccountService
# 新增的服务接口和实现
from .i_ex_account_service import IExAccountService
from .i_subscribe_symbol_service import ISubscribeSymbolService
from .i_trailing_group_service import ITrailingGroupService
from .service_manager import ServiceManager
from .subscribe_symbol_service_impl import SubscribeSymbolServiceImpl
from .trailing_binance_service_impl import TrailingBinanceServiceImpl
from .trailing_group_service_impl import TrailingGroupServiceImpl

__all__ = [
    'IAccountService',
    'AbstractAccountCommon',
    'AccountBeanFactory',
    'BinanceService',
    'BinanceSpotService',
    'BinanceFuturesService',
    'ServiceManager',
    'IExAccountService',
    'ISubscribeSymbolService',
    'ITrailingGroupService',
    'ExAccountServiceImpl',
    'SubscribeSymbolServiceImpl',
    'TrailingGroupServiceImpl',
    'BinanceStreamService',
    'TrailingBinanceServiceImpl',
]

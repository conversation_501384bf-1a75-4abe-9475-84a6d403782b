<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('划转')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-account-asset" th:object="${accountBalance}">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">账户名：</label>
                <div class="col-sm-8">
                    <input name="accountName" readonly="true"  th:field="*{accountName}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">现货账户：</label>
                <div class="col-sm-8">
                    <input name="spotBalance" readonly="true" th:field="*{spotBalance}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">合约账户：</label>
                <div class="col-sm-8">
                    <input name="futuresUBalance" readonly="true"  th:field="*{futuresUBalance}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">划转类型：</label>
                <div class="col-sm-8">
                    <select name="transferType" class="form-control" width="200">
                        <option value="MAIN_UMFUTURE">从现货转到U本位合约</option>
                        <option value="UMFUTURE_MAIN">从U本位合约转到现货</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">划转金额U：</label>
                <div class="col-sm-8">
                    <input name="transferAmount"  class="form-control" type="text">
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "strategy/account";
    $("#form-account-asset").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/asset", $('#form-account-asset').serialize());
        }
    }
</script>
</body>
</html>
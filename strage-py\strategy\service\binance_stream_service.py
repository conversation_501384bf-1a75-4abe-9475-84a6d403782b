"""
Binance流服务类
迁移自: com.project.strategy.service.impl.BinanceStreamService
"""
import asyncio
import json
import logging
import time
from decimal import Decimal
from typing import Dict, List, Optional, Any

import websockets

from .i_subscribe_symbol_service import ISubscribeSymbolService
from ..domain.entity.subscribe_symbol import SubscribeSymbol
from ..enums import ExchangeEnum
from ..global_cache import GlobalCache

logger = logging.getLogger(__name__)


class BinanceStreamService:
    """Binance流服务类 - 处理WebSocket数据流"""
    
    def __init__(self, subscribe_symbol_service: ISubscribeSymbolService, 
                 trailing_binance_service=None):
        """
        初始化Binance流服务
        
        Args:
            subscribe_symbol_service: 订阅交易对服务
            trailing_binance_service: 跟踪Binance服务
        """
        self.subscribe_symbol_service = subscribe_symbol_service
        self.trailing_binance_service = trailing_binance_service
        self.websocket_connections: Dict[str, Any] = {}
        self.is_running = False
        # 对应Java版本的UMWebsocketClientImpl client，在Python版本中用于保持接口一致性
        self.client = None
        logger.info("BinanceStreamService initialized")
    
    def symbol_subscribe(self) -> None:
        """
        订阅交易对深度数据
        对应Java中的symbolSubscribe方法
        """
        try:
            # 获取所有订阅的交易对
            symbol_list = self.subscribe_symbol_service.select_subscribe_symbol_list(SubscribeSymbol())
            
            for sub_symbol in symbol_list:
                symbol = sub_symbol.symbol.upper()
                cache_key = f"{ExchangeEnum.BINANCE.get_name()}_{symbol}"
                
                # 检查缓存中的深度数据
                depth_json = GlobalCache.DEPTH.get(cache_key)
                
                # 如果缓存中没有数据或数据超过30秒，重新订阅
                current_time = int(time.time() * 1000)
                if (depth_json is None or 
                    (depth_json.get('time', 0) + 30 * 1000) < current_time):
                    
                    logger.info(f"重新订阅交易对深度数据: {symbol}")
                    self.subscribe_depth(self.client, symbol, 5, 500)
            
        except Exception as e:
            logger.error(f"订阅交易对失败: {e}")

    def subscribe_depth(self, client, symbol: str, levels: int, speed: int) -> None:
        """
        订阅深度数据
        对应Java中的subscribeDepth方法

        Args:
            client: WebSocket客户端（为了保持与Java版本的方法签名一致，但在Python版本中不使用）
            symbol: 交易对
            levels: 深度级别
            speed: 更新速度（毫秒）
        """
        try:
            logger.info(f"订阅深度数据: {symbol}, levels: {levels}, speed: {speed}")
            asyncio.create_task(self._subscribe_depth_async(symbol, levels, speed))
        except Exception as e:
            logger.error(f"订阅深度数据失败 {symbol}: {e}")

    async def _subscribe_depth_async(self, symbol: str, levels: int, speed: int) -> None:
        """
        异步订阅深度数据
        
        Args:
            symbol: 交易对
            levels: 深度级别
            speed: 更新速度（毫秒）
        """
        try:
            # Binance WebSocket URL
            ws_url = f"wss://fstream.binance.com/ws/{symbol.lower()}@depth{levels}@{speed}ms"
            
            async with websockets.connect(ws_url) as websocket:
                self.websocket_connections[symbol] = websocket
                logger.info(f"WebSocket连接成功: {symbol}")
                
                async for message in websocket:
                    try:
                        await self._handle_depth_message(message)
                    except Exception as e:
                        logger.error(f"处理深度消息失败: {e}")
                        
        except Exception as e:
            logger.error(f"WebSocket连接失败 {symbol}: {e}")
            # 清理连接
            if symbol in self.websocket_connections:
                del self.websocket_connections[symbol]
    
    async def _handle_depth_message(self, message: str) -> None:
        """
        处理深度消息
        
        Args:
            message: WebSocket消息
        """
        try:
            event_json = json.loads(message)
            
            # 提取数据
            event_time = event_json.get('E', 0)
            symbol = event_json.get('s', '')
            bid_arr = event_json.get('b', [])
            ask_arr = event_json.get('a', [])
            
            if not symbol or not bid_arr or not ask_arr:
                return
            
            # 构建结果对象
            res_obj = {
                'time': event_time,
                'symbol': symbol,
                'bidArr': bid_arr,
                'askArr': ask_arr
            }
            
            # 存储到全局缓存
            cache_key = f"{ExchangeEnum.BINANCE.get_name()}_{symbol}"
            GlobalCache.DEPTH[cache_key] = res_obj
            
            # 提取买一价和卖一价
            buying_price = Decimal(str(bid_arr[0][0])) if bid_arr and bid_arr[0] else Decimal('0')  # 买盘一的价格，即可卖出价格
            selling_price = Decimal(str(ask_arr[0][0])) if ask_arr and ask_arr[0] else Decimal('0')  # 卖盘一的价格，即可买入的价格
            
            # 构建价格对象
            price_obj = {
                'buying': buying_price,
                'selling': selling_price,
                'time': event_time
            }
            
            # 存储价格到全局缓存
            GlobalCache.PRICE[cache_key] = price_obj

            # 根据当前最新的价格判断策略是否触发
            if self.trailing_binance_service:
                await self._handle_trailing_async(symbol)
            
            logger.debug(f"处理深度数据: {cache_key} -> buying: {buying_price}, selling: {selling_price}")
            
        except Exception as e:
            logger.error(f"处理深度消息失败: {e}")
    
    async def _handle_trailing_async(self, symbol: str) -> None:
        """
        异步处理跟踪策略
        
        Args:
            symbol: 交易对
        """
        try:
            if hasattr(self.trailing_binance_service, 'sync_handle_trailing'):
                # 在线程池中执行同步方法
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    None, 
                    self.trailing_binance_service.sync_handle_trailing, 
                    symbol
                )
        except Exception as e:
            logger.error(f"处理跟踪策略失败: {e}")
    
    def start_streaming(self) -> None:
        """
        启动流服务
        """
        try:
            if self.is_running:
                logger.warning("流服务已在运行中")
                return
            
            self.is_running = True
            logger.info("启动Binance流服务...")
            
            # 启动订阅
            self.symbol_subscribe()
            
        except Exception as e:
            logger.error(f"启动流服务失败: {e}")
            self.is_running = False
    
    def stop_streaming(self) -> None:
        """
        停止流服务
        """
        try:
            logger.info("停止Binance流服务...")
            self.is_running = False
            
            # 关闭所有WebSocket连接
            for symbol, websocket in self.websocket_connections.items():
                try:
                    if websocket and not websocket.closed:
                        asyncio.create_task(websocket.close())
                except Exception as e:
                    logger.error(f"关闭WebSocket连接失败 {symbol}: {e}")
            
            self.websocket_connections.clear()
            logger.info("流服务已停止")
            
        except Exception as e:
            logger.error(f"停止流服务失败: {e}")
    
    def get_connection_status(self) -> Dict[str, bool]:
        """
        获取连接状态
        
        Returns:
            连接状态字典
        """
        try:
            status = {}
            for symbol, websocket in self.websocket_connections.items():
                status[symbol] = websocket is not None and not websocket.closed
            return status
        except Exception as e:
            logger.error(f"获取连接状态失败: {e}")
            return {}
    
    def reconnect_symbol(self, symbol: str) -> None:
        """
        重连指定交易对
        
        Args:
            symbol: 交易对
        """
        try:
            logger.info(f"重连交易对: {symbol}")
            
            # 关闭现有连接
            if symbol in self.websocket_connections:
                websocket = self.websocket_connections[symbol]
                if websocket and not websocket.closed:
                    asyncio.create_task(websocket.close())
                del self.websocket_connections[symbol]
            
            # 重新订阅
            asyncio.create_task(self._subscribe_depth_async(symbol, 5, 500))
            
        except Exception as e:
            logger.error(f"重连交易对失败 {symbol}: {e}")
    
    def get_active_symbols(self) -> List[str]:
        """
        获取活跃的交易对列表
        
        Returns:
            活跃的交易对列表
        """
        try:
            return list(self.websocket_connections.keys())
        except Exception as e:
            logger.error(f"获取活跃交易对列表失败: {e}")
            return []
    
    def is_symbol_connected(self, symbol: str) -> bool:
        """
        检查交易对是否已连接
        
        Args:
            symbol: 交易对
            
        Returns:
            是否已连接
        """
        try:
            if symbol not in self.websocket_connections:
                return False
            
            websocket = self.websocket_connections[symbol]
            return websocket is not None and not websocket.closed
            
        except Exception as e:
            logger.error(f"检查交易对连接状态失败 {symbol}: {e}")
            return False
    
    def get_latest_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取最新价格
        
        Args:
            symbol: 交易对
            
        Returns:
            最新价格信息
        """
        try:
            cache_key = f"{ExchangeEnum.BINANCE.get_name()}_{symbol.upper()}"
            return GlobalCache.PRICE.get(cache_key)
        except Exception as e:
            logger.error(f"获取最新价格失败 {symbol}: {e}")
            return None
    
    def get_latest_depth(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取最新深度数据
        
        Args:
            symbol: 交易对
            
        Returns:
            最新深度数据
        """
        try:
            cache_key = f"{ExchangeEnum.BINANCE.get_name()}_{symbol.upper()}"
            return GlobalCache.DEPTH.get(cache_key)
        except Exception as e:
            logger.error(f"获取最新深度数据失败 {symbol}: {e}")
            return None


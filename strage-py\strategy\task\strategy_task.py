"""
策略任务类
迁移自: com.project.strategy.task.StrategyTask
"""
import asyncio
import logging
import threading
import time
from typing import Optional
from datetime import datetime, timedelta


logger = logging.getLogger(__name__)


class StrategyTask:
    """策略任务类 - 负责执行各种策略相关的定时任务"""
    
    def __init__(self):
        """初始化策略任务"""
        self._trailing_profit_service = None
        self._group_service = None
        self._is_running = False
        self._tasks = {}
        self._stop_event = threading.Event()
        logger.info("StrategyTask initialized")
    
    def set_trailing_profit_service(self, service) -> None:
        """
        设置跟踪盈利服务
        
        Args:
            service: 跟踪盈利服务实例
        """
        self._trailing_profit_service = service
        logger.info("设置跟踪盈利服务")
    
    def set_group_service(self, service) -> None:
        """
        设置组服务
        
        Args:
            service: 组服务实例
        """
        self._group_service = service
        logger.info("设置组服务")
    
    def start_follow(self) -> None:
        """
        开始跟单任务
        对应Java中的@Scheduled(fixedDelay = 10 * 1000) startFollow()
        """
        try:
            if self._trailing_profit_service is None:
                logger.warning("跟踪盈利服务未设置，跳过跟单任务")
                return
            
            logger.debug("执行跟单任务...")
            self._trailing_profit_service.start_follow()
            logger.debug("跟单任务执行完成")
            
        except Exception as e:
            logger.error(f"StrategyTask startFollow error: {e}", exc_info=True)
    
    def start_trailing(self) -> None:
        """
        开始跟踪任务
        对应Java中的@Scheduled(fixedDelay = 8 * 1000) startTraling()
        """
        try:
            if self._trailing_profit_service is None:
                logger.warning("跟踪盈利服务未设置，跳过跟踪任务")
                return
            
            logger.debug("执行跟踪任务...")
            self._trailing_profit_service.start_trailing()
            logger.debug("跟踪任务执行完成")
            
        except Exception as e:
            logger.error(f"StrategyTask startTrailing error: {e}", exc_info=True)
    
    def position_update(self) -> None:
        """
        持仓更新任务
        对应Java中的@Scheduled(fixedDelay = 8 * 1000) positionUpdate()
        """
        try:
            if self._trailing_profit_service is None:
                logger.warning("跟踪盈利服务未设置，跳过持仓更新任务")
                return
            
            logger.debug("执行持仓更新任务...")
            self._trailing_profit_service.position_update()
            logger.debug("持仓更新任务执行完成")
            
        except Exception as e:
            logger.error(f"StrategyTask positionUpdate error: {e}", exc_info=True)
    
    def strategy_group(self) -> None:
        """
        策略组处理任务
        对应Java中的@Scheduled(fixedDelay = 8 * 1000) strategGroup()
        """
        try:
            if self._group_service is None:
                logger.warning("组服务未设置，跳过策略组任务")
                return
            
            logger.debug("执行策略组任务...")
            self._group_service.handle_group_state()
            logger.debug("策略组任务执行完成")
            
        except Exception as e:
            logger.error(f"strategGroup error: {e}", exc_info=True)
    
    def refresh_order(self) -> None:
        """
        刷新订单任务
        对应Java中的@Scheduled(fixedDelay = 8 * 1000) refreshOrder()
        """
        try:
            if self._trailing_profit_service is None:
                logger.warning("跟踪盈利服务未设置，跳过刷新订单任务")
                return
            
            logger.debug("执行刷新订单任务...")
            self._trailing_profit_service.refresh_order()
            logger.debug("刷新订单任务执行完成")
            
        except Exception as e:
            logger.error(f"StrategyTask refreshOrder error: {e}", exc_info=True)
    
    def _run_task_loop(self, task_name: str, task_func, interval: float) -> None:
        """
        运行任务循环
        
        Args:
            task_name: 任务名称
            task_func: 任务函数
            interval: 执行间隔（秒）
        """
        logger.info(f"启动任务循环: {task_name}, 间隔: {interval}秒")
        
        while not self._stop_event.is_set():
            try:
                start_time = time.time()
                task_func()
                execution_time = time.time() - start_time
                
                # 计算下次执行时间
                sleep_time = max(0, interval - execution_time)
                if sleep_time > 0:
                    self._stop_event.wait(sleep_time)
                    
            except Exception as e:
                logger.error(f"任务 {task_name} 执行异常: {e}", exc_info=True)
                # 异常后等待一段时间再继续
                self._stop_event.wait(min(interval, 5.0))
        
        logger.info(f"任务循环已停止: {task_name}")
    
    def start_all_tasks(self) -> None:
        """启动所有定时任务"""
        if self._is_running:
            logger.warning("任务已在运行中")
            return
        
        self._is_running = True
        self._stop_event.clear()
        
        # 定义任务配置 (任务名称, 任务函数, 间隔秒数)
        task_configs = [
            ("start_follow", self.start_follow, 10.0),
            ("start_trailing", self.start_trailing, 8.0),
            ("position_update", self.position_update, 8.0),
            ("strategy_group", self.strategy_group, 8.0),
            ("refresh_order", self.refresh_order, 8.0),
        ]
        
        # 启动所有任务线程
        for task_name, task_func, interval in task_configs:
            thread = threading.Thread(
                target=self._run_task_loop,
                args=(task_name, task_func, interval),
                name=f"StrategyTask-{task_name}",
                daemon=True
            )
            thread.start()
            self._tasks[task_name] = thread
        
        logger.info(f"已启动 {len(self._tasks)} 个策略任务")
    
    def stop_all_tasks(self) -> None:
        """停止所有定时任务"""
        if not self._is_running:
            logger.warning("任务未在运行")
            return
        
        logger.info("正在停止所有策略任务...")
        self._stop_event.set()
        
        # 等待所有线程结束
        for task_name, thread in self._tasks.items():
            try:
                thread.join(timeout=5.0)
                if thread.is_alive():
                    logger.warning(f"任务 {task_name} 未能在5秒内停止")
            except Exception as e:
                logger.error(f"停止任务 {task_name} 时出错: {e}")
        
        self._tasks.clear()
        self._is_running = False
        logger.info("所有策略任务已停止")
    
    def is_running(self) -> bool:
        """检查任务是否在运行"""
        return self._is_running
    
    def get_task_status(self) -> dict:
        """
        获取任务状态
        
        Returns:
            任务状态字典
        """
        status = {
            'is_running': self._is_running,
            'task_count': len(self._tasks),
            'tasks': {}
        }
        
        for task_name, thread in self._tasks.items():
            status['tasks'][task_name] = {
                'alive': thread.is_alive(),
                'name': thread.name
            }
        
        return status
    
    def execute_single_task(self, task_name: str) -> bool:
        """
        执行单个任务（用于测试或手动触发）
        
        Args:
            task_name: 任务名称
            
        Returns:
            是否执行成功
        """
        task_map = {
            'start_follow': self.start_follow,
            'start_trailing': self.start_trailing,
            'position_update': self.position_update,
            'strategy_group': self.strategy_group,
            'refresh_order': self.refresh_order,
        }
        
        if task_name not in task_map:
            logger.error(f"未知任务: {task_name}")
            return False
        
        try:
            logger.info(f"手动执行任务: {task_name}")
            task_map[task_name]()
            return True
        except Exception as e:
            logger.error(f"手动执行任务 {task_name} 失败: {e}", exc_info=True)
            return False
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"StrategyTask(running={self._is_running}, tasks={len(self._tasks)})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"StrategyTask(is_running={self._is_running}, task_names={list(self._tasks.keys())})"


# 全局策略任务实例
strategy_task = StrategyTask()

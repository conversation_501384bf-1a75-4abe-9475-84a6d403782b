"""
服务管理器
统一管理所有服务实例
"""
import logging
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Optional, Any

from .account_bean_factory import AccountBeanFactory
from .i_account_service import IAccountService
from ..context.account_context import AccountContext
from ..dao.ex_account_dao import ExAccountDAO
from ..domain.entity.account_balance import AccountBalance
from ..enums import ExchangeEnum, InstrumentEnum, TransferInternalEnum

logger = logging.getLogger(__name__)


class ServiceManager:
    """服务管理器 - 统一管理所有服务实例"""
    
    def __init__(self, db_session):
        """
        初始化服务管理器
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
        self.account_context = AccountContext()
        self.account_bean_factory = AccountBeanFactory(db_session, self.account_context)
        self.ex_account_dao = ExAccountDAO()
        logger.info("ServiceManager initialized")
    
    def initialize_services(self) -> None:
        """
        初始化所有服务
        """
        try:
            logger.info("开始初始化服务...")
            
            # 创建账户服务实例
            self.account_bean_factory.create_account_services()
            
            logger.info("服务初始化完成")
            
        except Exception as e:
            logger.error(f"服务初始化失败: {e}")
            raise
    
    def get_account_service(self, exchange: ExchangeEnum, 
                           instrument: InstrumentEnum, 
                           account_name: str) -> Optional[IAccountService]:
        """
        获取账户服务
        
        Args:
            exchange: 交易所
            instrument: 交易工具
            account_name: 账户名称
            
        Returns:
            账户服务实例
        """
        return self.account_context.get_account_client(exchange, instrument, account_name)
    
    def get_balance(self, account_name: str) -> AccountBalance:
        """
        获取账户余额
        对应Java中的BinanceService.getBalance方法
        
        Args:
            account_name: 账户名称
            
        Returns:
            账户余额信息
        """
        try:
            # 获取现货和期货账户服务
            spot_service = self.get_account_service(
                ExchangeEnum.BINANCE, 
                InstrumentEnum.SPOT, 
                account_name
            )
            
            futures_service = self.get_account_service(
                ExchangeEnum.BINANCE, 
                InstrumentEnum.FUTURES_U, 
                account_name
            )
            
            # 创建账户余额对象
            account_balance = AccountBalance()
            account_balance.account_name = account_name
            
            # 获取现货余额
            if spot_service:
                try:
                    spot_balance = spot_service.get_balance()
                    account_balance.spot_balance = spot_balance.quantize(
                        Decimal('0.0001'), rounding=ROUND_DOWN
                    )
                except Exception as e:
                    logger.warning(f"获取现货余额失败: {e}")
                    account_balance.spot_balance = Decimal('0.0000')
            else:
                logger.warning(f"未找到账户 {account_name} 的现货服务")
                account_balance.spot_balance = Decimal('0.0000')

            # 获取期货余额
            if futures_service:
                try:
                    futures_balance = futures_service.get_balance()
                    account_balance.futures_u_balance = futures_balance.quantize(
                        Decimal('0.0001'), rounding=ROUND_DOWN
                    )
                except Exception as e:
                    logger.warning(f"获取期货余额失败: {e}")
                    account_balance.futures_u_balance = Decimal('0.0000')
            else:
                logger.warning(f"未找到账户 {account_name} 的期货服务")
                account_balance.futures_u_balance = Decimal('0.0000')
            
            logger.info(f"获取账户余额成功: {account_name}, 现货: {account_balance.spot_balance}, 期货: {account_balance.futures_u_balance}")
            return account_balance

        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            # 返回空余额
            account_balance = AccountBalance()
            account_balance.account_name = account_name
            account_balance.spot_balance = Decimal('0.0000')
            account_balance.futures_u_balance = Decimal('0.0000')
            return account_balance
    
    def transfer_asset(self, account_name: str, amount: Decimal, 
                      transfer_internal_enum: TransferInternalEnum) -> bool:
        """
        资产转账
        对应Java中的BinanceService.transferAsset方法
        
        Args:
            account_name: 账户名称
            amount: 转账金额
            transfer_internal_enum: 转账类型
            
        Returns:
            是否转账成功
        """
        try:
            # 获取现货账户服务
            spot_service = self.get_account_service(
                ExchangeEnum.BINANCE, 
                InstrumentEnum.SPOT, 
                account_name
            )
            
            if spot_service:
                return spot_service.transfer_asset(account_name, amount, transfer_internal_enum)
            else:
                logger.error(f"未找到账户 {account_name} 的现货服务")
                return False
                
        except Exception as e:
            logger.error(f"资产转账失败: {e}")
            return False
    
    def refresh_account_services(self) -> None:
        """
        刷新账户服务
        """
        try:
            self.account_bean_factory.refresh_account_services()
            logger.info("账户服务刷新完成")
        except Exception as e:
            logger.error(f"刷新账户服务失败: {e}")
    
    def add_account_service(self, account_id: int) -> bool:
        """
        添加账户服务
        
        Args:
            account_id: 账户ID
            
        Returns:
            是否添加成功
        """
        try:
            account = self.ex_account_dao.select_ex_account_by_id(account_id)
            if account:
                return self.account_bean_factory.add_account_service(account)
            else:
                logger.error(f"未找到账户ID为 {account_id} 的账户")
                return False
                
        except Exception as e:
            logger.error(f"添加账户服务失败: {e}")
            return False
    
    def remove_account_service(self, account_id: int) -> bool:
        """
        移除账户服务

        Args:
            account_id: 账户ID

        Returns:
            是否移除成功
        """
        try:
            # 先根据ID查询账户信息获取账户名称
            ex_account = self.ex_account_dao.select_ex_account_by_id(account_id)
            if not ex_account:
                logger.warning(f"未找到ID为 {account_id} 的账户")
                return False

            return self.account_bean_factory.remove_account_service(ex_account.account_name)
        except Exception as e:
            logger.error(f"移除账户服务失败: {e}")
            return False
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            服务状态信息
        """
        try:
            status = {
                'total_services': self.account_bean_factory.get_service_count(),
                'service_status': self.account_bean_factory.get_service_status(),
                'account_context_status': self.account_context.get_status()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取服务状态失败: {e}")
            return {'error': str(e)}
    
    def validate_services(self) -> Dict[str, bool]:
        """
        验证所有服务
        
        Returns:
            验证结果字典
        """
        try:
            return self.account_context.validate_clients()
        except Exception as e:
            logger.error(f"验证服务失败: {e}")
            return {}
    
    def get_all_account_names(self) -> list:
        """
        获取所有账户名称

        Returns:
            账户名称列表
        """
        try:
            from ..domain.entity.ex_account import ExAccount
            ex_account = ExAccount()
            ex_account.state = 1
            active_accounts = self.ex_account_dao.select_ex_account_list(ex_account)
            return [account.account_name for account in active_accounts if account.account_name]
        except Exception as e:
            logger.error(f"获取账户名称列表失败: {e}")
            return []
    
    def test_connectivity(self) -> Dict[str, bool]:
        """
        测试所有服务的连接性
        
        Returns:
            连接性测试结果
        """
        try:
            results = {}
            all_clients = self.account_context.get_all_clients()
            
            for bean_name, client in all_clients.items():
                try:
                    if hasattr(client, 'test_connectivity'):
                        results[bean_name] = client.test_connectivity()
                    else:
                        results[bean_name] = client.is_support()
                except Exception as e:
                    logger.error(f"测试服务 {bean_name} 连接性失败: {e}")
                    results[bean_name] = False
            
            return results
            
        except Exception as e:
            logger.error(f"测试连接性失败: {e}")
            return {}
    
    def shutdown(self) -> None:
        """
        关闭服务管理器
        """
        try:
            logger.info("正在关闭服务管理器...")
            
            # 清理账户上下文
            self.account_context.clear_all_clients()
            
            # 关闭数据库会话
            if self.db_session:
                self.db_session.close()
            
            logger.info("服务管理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭服务管理器失败: {e}")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ServiceManager(services={self.account_bean_factory.get_service_count()})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"ServiceManager(service_count={self.account_bean_factory.get_service_count()})"

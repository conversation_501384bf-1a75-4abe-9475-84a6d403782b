"""
Binance现货服务类
迁移自: com.project.strategy.service.impl.BinanceSpotService
"""
import logging
import time
from decimal import Decimal

from .abstract_account_common import AbstractAccountCommon
from ..domain.entity.ex_account import ExAccount
from ..enums import InstrumentEnum, ExchangeEnum, TransferInternalEnum
from ..utils import OkHttpUtil
from ..utils.signature_generator import SignatureGenerator

logger = logging.getLogger(__name__)


class BinanceSpotService(AbstractAccountCommon):
    """Binance现货服务类"""

    # 常量定义 - 与Java版本保持一致
    BASE_SPOT = "https://api3.binance.com"
    TRANSFER_INTERNAL = "/sapi/v1/asset/transfer"
    ACCOUNT = "/api/v3/account"

    def __init__(self, db_session, account: ExAccount):
        """
        初始化Binance现货服务

        Args:
            db_session: 数据库会话
            account: 交易所账户
        """
        self.db_session = db_session
        self.account = account
        self.api_key = account.apikey
        self.secret_key = account.secret_key
        self.account_name = account.account_name

        logger.info(f"BinanceSpotService initialized for account: {self.account_name}")
    

    def transfer_asset(self, account_name: str, amount: Decimal, transfer_internal_enum: TransferInternalEnum) -> bool:
        """
        资产转账 - 对应Java中的transferAsset(String accountName, BigDecimal amount, TransferInternalEnum transferInternalEnum)

        Args:
            account_name: 账户名称
            amount: 转账金额
            transfer_internal_enum: 转账类型

        Returns:
            是否转账成功
        """
        query_string = f"type={transfer_internal_enum.get_value()}&asset=USDT&amount={amount}&recvWindow=5000&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        # 创建空的RequestBody，与Java版本保持一致
        header_map = self.get_header()
        url = f"{self.BASE_SPOT}{self.TRANSFER_INTERNAL}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_post(url, {}, header_map)

        import json
        order = json.loads(res)
        tran_id = order.get("tranId")

        is_success = False
        if tran_id is not None and int(tran_id) > 0:
            is_success = True
        else:
            is_success = False

        return is_success

    def get_balance(self) -> Decimal:
        """
        获取余额
        对应Java中的getBalance方法

        Returns:
            USDT余额
        """
        query_string = f"omitZeroBalances=true&recvWindow=5000&timestamp={int(time.time() * 1000)}"
        signature = SignatureGenerator.get_signature(query_string, self.secret_key)
        header_map = self.get_header()
        url = f"{self.BASE_SPOT}{self.ACCOUNT}?{query_string}&signature={signature}"
        res = OkHttpUtil.do_get(url, header_map)

        import json
        json_obj = json.loads(res)
        if json_obj.get("code") is not None:
            logger.error(f"BinanceSpotService gatBalance error>>> {json_obj.get('msg')}")
            return Decimal('0')

        balance_arr = json_obj.get("balances", [])
        balance = Decimal('0')
        for tmp in balance_arr:
            if "USDT" == tmp.get("asset"):
                balance = Decimal(str(tmp.get("free", "0")))
                break

        return balance

    def get_header(self):
        """
        获取HTTP请求头
        对应Java中的getHeader方法

        Returns:
            请求头字典
        """
        header_map = {
            "User-Agent": "binance-futures-connector-java/3.0.5",
            "Content-Type": "application/x-www-form-urlencoded",
            "X-MBX-APIKEY": self.api_key
        }
        return header_map

    def get_account_name(self) -> str:
        """
        获取账户名称

        Returns:
            账户名称
        """
        return self.account_name

    def get_account(self) -> str:
        """
        获取账户

        Returns:
            账户信息
        """
        return getattr(self.account, 'ex_account', '') if self.account else ""

    def get_exchange(self) -> ExchangeEnum:
        """
        获取交易所

        Returns:
            交易所枚举
        """
        return ExchangeEnum.BINANCE

    def get_instrument(self) -> InstrumentEnum:
        """
        获取交易工具

        Returns:
            交易工具枚举
        """
        return InstrumentEnum.SPOT

    def is_support(self) -> bool:
        """
        是否支持

        Returns:
            是否支持此服务
        """
        return True


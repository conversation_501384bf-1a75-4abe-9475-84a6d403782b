/**
 * 首页相关JS
 */
$(function () {
    // 初始化菜单
    $('#side-menu').metisMenu();

    // 菜单收缩
    $('.navbar-minimalize').click(function () {
        $("body").toggleClass("mini-navbar");
        SmoothlyMenu();
    });

    // 侧边栏滚动
    $('.sidebar-collapse').slimScroll({
        height: '100%',
        railOpacity: 0.9
    });

    // 全屏切换
    $('#fullScreen').click(function () {
        toggleFullScreen();
    });

    // 锁屏功能
    $('#lockScreen').click(function () {
        lockScreen();
    });

    // 主题切换
    $('#switchTheme').click(function () {
        switchTheme();
    });

    // 菜单切换
    $('#toggleMenu').click(function () {
        toggleSideMenu();
    });

    // 页签操作
    $('.tabLeft').on('click', scrollToTab);
    $('.tabRight').on('click', scrollToTab);
    $('.tabReload').on('click', refreshTab);

    // 标签页点击事件
    $(document).on('click', '.menuTab', function () {
        var dataId = $(this).attr('data-id');
        if (dataId) {
            // 移除所有标签页的激活状态
            $('.menuTab').removeClass('active');
            // 激活当前点击的标签页
            $(this).addClass('active');
            // 切换iframe内容
            $('.RuoYi_iframe').attr('src', dataId).attr('data-id', dataId);
        }
        return false;
    });

    // 菜单点击事件
    $('.menuItem').on('click', function () {
        var url = $(this).attr('href');
        var title = $(this).text();
        var dataId = $(this).attr('data-id') || url;

        if (url && url !== '#' && url !== 'javascript:;') {
            addTab(title, url, dataId);
        }
        return false;
    });
});

// 平滑菜单
function SmoothlyMenu() {
    if (!$("body").hasClass('mini-navbar') || $("body").hasClass('body-small')) {
        $('#side-menu').hide();
        setTimeout(function () {
            $('#side-menu').fadeIn(400);
        }, 200);
    } else if ($("body").hasClass('fixed-sidebar')) {
        $('#side-menu').hide();
        setTimeout(function () {
            $('#side-menu').fadeIn(400);
        }, 100);
    } else {
        $('#side-menu').removeAttr('style');
    }
}

// 添加选项卡
function addTab(title, url, id) {
    var isExists = $('.menuTab[data-id="' + id + '"]').length > 0;

    if (!isExists) {
        var content = '<a href="javascript:;" class="menuTab" data-id="' + id + '">' + title + ' <i class="fa fa-times-circle"></i></a>';
        $('.page-tabs-content').append(content);

        // 绑定关闭事件
        $('.menuTab[data-id="' + id + '"] i').on('click', function (e) {
            e.stopPropagation(); // 防止事件冒泡到标签页点击事件
            closeTab($(this).parent());
            return false;
        });
    }

    // 激活选项卡
    $('.menuTab').removeClass('active');
    $('.menuTab[data-id="' + id + '"]').addClass('active');

    // 加载内容
    $('.RuoYi_iframe').attr('src', url).attr('data-id', id);

    // 滚动到当前选项卡
    scrollToActiveTab();
}

// 关闭选项卡
function closeTab($tab) {
    var isActive = $tab.hasClass('active');
    var dataId = $tab.attr('data-id');

    $tab.remove();

    if (isActive) {
        var $lastTab = $('.menuTab').last();
        if ($lastTab.length > 0) {
            $lastTab.addClass('active');
            var lastUrl = $lastTab.attr('data-id');
            $('.RuoYi_iframe').attr('src', lastUrl).attr('data-id', lastUrl);
        }
    }
}

// 滚动到激活的选项卡
function scrollToActiveTab() {
    var $activeTab = $('.menuTab.active');
    if ($activeTab.length > 0) {
        var activeTabOffset = $activeTab.position().left;
        var tabsWidth = $('.page-tabs').width();
        var contentWidth = $('.page-tabs-content').width();

        if (activeTabOffset > tabsWidth) {
            $('.page-tabs-content').animate({
                marginLeft: -(activeTabOffset - tabsWidth + 120)
            }, 'fast');
        } else if (activeTabOffset < 0) {
            $('.page-tabs-content').animate({
                marginLeft: -activeTabOffset
            }, 'fast');
        }
    }
}

// 选项卡滚动
function scrollToTab() {
    var marginLeft = Math.abs(parseInt($('.page-tabs-content').css('margin-left')));
    var tabsWidth = $('.page-tabs').width();
    var contentWidth = $('.page-tabs-content').width();

    if ($(this).hasClass('tabLeft')) {
        if (marginLeft > 0) {
            $('.page-tabs-content').animate({
                marginLeft: Math.min(0, marginLeft - tabsWidth)
            }, 'fast');
        }
    } else {
        if (marginLeft < contentWidth - tabsWidth) {
            $('.page-tabs-content').animate({
                marginLeft: -(marginLeft + tabsWidth)
            }, 'fast');
        }
    }
}

// 刷新选项卡
function refreshTab() {
    var $activeTab = $('.menuTab.active');
    if ($activeTab.length > 0) {
        var dataId = $activeTab.attr('data-id');
        $('.RuoYi_iframe').attr('src', dataId);
    }
}

// 窗口大小改变时调整
$(window).resize(function () {
    var $sideMenu = $('#side-menu');
    if ($sideMenu.length > 0) {
        $sideMenu.css('height', $(window).height() - 42);
    }
});

// 全屏切换功能
function toggleFullScreen() {
    if (!document.fullscreenElement && !document.webkitFullscreenElement &&
        !document.mozFullScreenElement && !document.msFullscreenElement) {
        // 进入全屏
        var docElm = document.documentElement;
        if (docElm.requestFullscreen) {
            docElm.requestFullscreen();
        } else if (docElm.webkitRequestFullScreen) {
            docElm.webkitRequestFullScreen();
        } else if (docElm.mozRequestFullScreen) {
            docElm.mozRequestFullScreen();
        } else if (docElm.msRequestFullscreen) {
            docElm.msRequestFullscreen();
        }
        $('#fullScreen i').removeClass('fa-arrows-alt').addClass('fa-compress');
        $('#fullScreen').attr('title', '退出全屏');
    } else {
        // 退出全屏
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
            document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
        $('#fullScreen i').removeClass('fa-compress').addClass('fa-arrows-alt');
        $('#fullScreen').attr('title', '全屏显示');
    }
}

// 锁屏功能
function lockScreen() {
    // 保存当前激活的标签页
    var $activeTab = $('.menuTab.active');
    if ($activeTab.length > 0) {
        localStorage.setItem('lockPath', $activeTab.attr('data-id'));
    }

    // 跳转到锁屏页面
    window.location.href = '/lockscreen';
}

// 主题切换功能
function switchTheme() {
    var body = $('body');
    var currentTheme = localStorage.getItem('theme') || 'light';

    if (currentTheme === 'light') {
        // 切换到暗黑主题
        body.removeClass('theme-light').addClass('theme-dark');
        localStorage.setItem('theme', 'dark');
        $('#switchTheme i').removeClass('fa-adjust').addClass('fa-sun-o');
        $('#switchTheme').attr('title', '切换到明亮主题');
    } else {
        // 切换到明亮主题
        body.removeClass('theme-dark').addClass('theme-light');
        localStorage.setItem('theme', 'light');
        $('#switchTheme i').removeClass('fa-sun-o').addClass('fa-adjust');
        $('#switchTheme').attr('title', '切换到暗黑主题');
    }
}

// 菜单切换功能
function toggleSideMenu() {
    var body = $('body');
    var isMinimized = body.hasClass('mini-navbar');

    if (isMinimized) {
        // 展开菜单
        body.removeClass('mini-navbar');
        localStorage.setItem('menuState', 'expanded');
        $('#toggleMenu i').removeClass('fa-indent').addClass('fa-outdent');
        $('#toggleMenu').attr('title', '收起菜单');
    } else {
        // 收起菜单
        body.addClass('mini-navbar');
        localStorage.setItem('menuState', 'minimized');
        $('#toggleMenu i').removeClass('fa-outdent').addClass('fa-indent');
        $('#toggleMenu').attr('title', '展开菜单');
    }

    // 触发菜单动画
    SmoothlyMenu();
}

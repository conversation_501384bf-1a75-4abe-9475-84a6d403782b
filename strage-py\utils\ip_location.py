"""
IP地址地理位置查询工具
"""
import requests
import logging
import json
from functools import lru_cache

logger = logging.getLogger(__name__)

class IPLocationService:
    """IP地址地理位置服务"""
    
    @staticmethod
    @lru_cache(maxsize=1000)
    def get_location_by_ip(ip_address):
        """
        根据IP地址获取地理位置信息
        
        Args:
            ip_address: IP地址
            
        Returns:
            str: 地理位置描述
        """
        if not ip_address or ip_address in ['127.0.0.1', 'localhost', '::1']:
            return '内网IP'
        
        # 检查是否为内网IP
        if IPLocationService._is_private_ip(ip_address):
            return '内网IP'
        
        try:
            # 使用免费的IP地理位置查询API
            # 这里使用ip-api.com的免费服务
            url = f"http://ip-api.com/json/{ip_address}?lang=zh-CN"
            
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    country = data.get('country', '')
                    region = data.get('regionName', '')
                    city = data.get('city', '')
                    
                    # 构建地理位置描述
                    location_parts = []
                    if country:
                        location_parts.append(country)
                    if region and region != country:
                        location_parts.append(region)
                    if city and city != region:
                        location_parts.append(city)
                    
                    if location_parts:
                        return ' '.join(location_parts)
                    else:
                        return '未知位置'
                else:
                    return '未知位置'
            else:
                logger.warning(f"IP地理位置查询失败，状态码: {response.status_code}")
                return '未知位置'
                
        except requests.exceptions.Timeout:
            logger.warning(f"IP地理位置查询超时: {ip_address}")
            return '查询超时'
        except requests.exceptions.RequestException as e:
            logger.warning(f"IP地理位置查询异常: {e}")
            return '查询失败'
        except Exception as e:
            logger.error(f"IP地理位置查询错误: {e}")
            return '未知位置'
    
    @staticmethod
    def _is_private_ip(ip_address):
        """
        检查是否为内网IP地址
        
        Args:
            ip_address: IP地址
            
        Returns:
            bool: 是否为内网IP
        """
        try:
            import ipaddress
            ip = ipaddress.ip_address(ip_address)
            return ip.is_private
        except:
            # 简单的内网IP判断
            if ip_address.startswith('192.168.') or \
               ip_address.startswith('10.') or \
               ip_address.startswith('172.'):
                return True
            return False
    
    @staticmethod
    def get_location_by_ip_simple(ip_address):
        """
        简化版本的IP地理位置查询（不依赖外部API）
        
        Args:
            ip_address: IP地址
            
        Returns:
            str: 地理位置描述
        """
        if not ip_address or ip_address in ['127.0.0.1', 'localhost', '::1']:
            return '内网IP'
        
        # 检查是否为内网IP
        if IPLocationService._is_private_ip(ip_address):
            return '内网IP'
        
        # 简单的地区判断（基于IP段）
        try:
            parts = ip_address.split('.')
            if len(parts) == 4:
                first_octet = int(parts[0])
                
                # 中国大陆IP段（简化版本）
                if first_octet in [1, 14, 27, 36, 39, 42, 49, 58, 59, 60, 61, 101, 103, 106, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 180, 182, 183, 202, 203, 210, 211, 218, 219, 220, 221, 222, 223]:
                    return '中国'
                
                # 美国IP段
                elif first_octet in [3, 4, 6, 7, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 28, 29, 30, 32, 33, 34, 35, 38, 40, 44, 45, 47, 48, 50, 52, 53, 54, 55, 56, 57, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 96, 97, 98, 99, 100, 104, 107, 108, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 142, 143, 144, 146, 147, 148, 149, 152, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 173, 174, 184, 192, 198, 199, 204, 205, 206, 207, 208, 209, 216]:
                    return '美国'
                
                # 其他情况
                else:
                    return '海外'
            else:
                return '未知位置'
                
        except:
            return '未知位置'

# 为了兼容性，提供一个简单的函数接口
def get_ip_location(ip_address):
    """
    获取IP地址的地理位置
    
    Args:
        ip_address: IP地址
        
    Returns:
        str: 地理位置描述
    """
    # 优先使用简化版本，避免外部API依赖
    return IPLocationService.get_location_by_ip_simple(ip_address)

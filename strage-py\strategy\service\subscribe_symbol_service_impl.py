"""
订阅交易对服务实现类
迁移自: com.project.strategy.service.impl.SubscribeSymbolServiceImpl
"""
import logging
from datetime import datetime
from typing import List, Optional

from .i_subscribe_symbol_service import ISubscribeSymbolService
from ..dao.subscribe_symbol_dao import SubscribeSymbolDAO
from ..domain.entity.subscribe_symbol import SubscribeSymbol

logger = logging.getLogger(__name__)


class SubscribeSymbolServiceImpl(ISubscribeSymbolService):
    """订阅交易对服务实现类"""
    
    def __init__(self):
        """
        初始化订阅交易对服务
        """
        self.subscribe_symbol_dao = SubscribeSymbolDAO()
        logger.info("SubscribeSymbolServiceImpl initialized")
    
    def select_subscribe_symbol_by_id(self, id: int) -> Optional[SubscribeSymbol]:
        """
        根据ID查询订阅交易对
        对应Java中的selectSubscribeSymbolById方法

        Args:
            id: 订阅ID

        Returns:
            订阅交易对对象，不存在返回None
        """
        return self.subscribe_symbol_dao.select_subscribe_symbol_by_id(id)
    
    def select_subscribe_symbol_list(self, subscribe_symbol: SubscribeSymbol) -> List[SubscribeSymbol]:
        """
        查询订阅交易对列表
        对应Java中的selectSubscribeSymbolList方法

        Args:
            subscribe_symbol: 查询条件对象

        Returns:
            订阅交易对列表
        """
        subscribe_symbol.state = 1
        return self.subscribe_symbol_dao.select_subscribe_symbol_list(subscribe_symbol)
    
    def insert_subscribe_symbol(self, subscribe_symbol: SubscribeSymbol) -> int:
        """
        插入订阅交易对
        对应Java中的insertSubscribeSymbol方法

        Args:
            subscribe_symbol: 订阅交易对对象

        Returns:
            插入的记录数量
        """
        subscribe_symbol.create_time = datetime.now()
        return self.subscribe_symbol_dao.insert_subscribe_symbol(subscribe_symbol)
    
    def update_subscribe_symbol(self, subscribe_symbol: SubscribeSymbol) -> int:
        """
        更新订阅交易对
        对应Java中的updateSubscribeSymbol方法

        Args:
            subscribe_symbol: 订阅交易对对象

        Returns:
            更新的记录数量
        """
        subscribe_symbol.update_time = datetime.now()
        return self.subscribe_symbol_dao.update_subscribe_symbol(subscribe_symbol)
    
    def delete_subscribe_symbol_by_ids(self, ids: str) -> int:
        """
        根据ID列表批量删除订阅交易对
        对应Java中的deleteSubscribeSymbolByIds方法

        Args:
            ids: ID字符串，逗号分隔

        Returns:
            删除的记录数量
        """
        return self.subscribe_symbol_dao.delete_subscribe_symbol_by_ids(ids)
    
    def delete_subscribe_symbol_by_id(self, id: int) -> int:
        """
        根据ID删除订阅交易对
        对应Java中的deleteSubscribeSymbolById方法

        Args:
            id: 订阅ID

        Returns:
            删除的记录数量
        """
        return self.subscribe_symbol_dao.delete_subscribe_symbol_by_id(id)


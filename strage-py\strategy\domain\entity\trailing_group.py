"""
跟踪组实体类
迁移自: com.project.strategy.domain.entity.TrailingGroup
"""
from dataclasses import dataclass
from typing import Optional
from datetime import datetime


@dataclass
class TrailingGroup:
    """跟踪组实体"""
    
    id: Optional[int] = None
    user_id: Optional[int] = None
    parent_id: Optional[int] = None
    group_name: Optional[str] = None
    profit_state: Optional[int] = None
    state: Optional[int] = None  # 0: 不启用， 1：进行中，2：完成并复制新的策略组，3：完成但有账户余额不足-停，4：完成但连续亏3次-停
    loss_times: Optional[int] = None  # 亏损次数
    message: Optional[str] = None  # 备注
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保数值类型的正确性
        int_fields = ['id', 'user_id', 'parent_id', 'profit_state', 'state', 'loss_times']
        for field_name in int_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, int):
                try:
                    setattr(self, field_name, int(value))
                except (ValueError, TypeError):
                    setattr(self, field_name, None)
    
    def is_disabled(self) -> bool:
        """检查是否未启用"""
        return self.state == 0
    
    def is_active(self) -> bool:
        """检查是否进行中"""
        return self.state == 1
    
    def is_completed_and_copied(self) -> bool:
        """检查是否完成并复制新的策略组"""
        return self.state == 2
    
    def is_stopped_insufficient_balance(self) -> bool:
        """检查是否因账户余额不足而停止"""
        return self.state == 3
    
    def is_stopped_continuous_loss(self) -> bool:
        """检查是否因连续亏损而停止"""
        return self.state == 4
    
    def get_state_name(self) -> str:
        """获取状态名称"""
        state_names = {
            0: "未启用",
            1: "进行中",
            2: "完成并复制新策略组",
            3: "余额不足停止",
            4: "连续亏损停止"
        }
        return state_names.get(self.state, "未知状态")
    
    def is_valid(self) -> bool:
        """检查组信息是否有效"""
        return (
            self.user_id is not None and
            self.group_name is not None and
            len(self.group_name.strip()) > 0
        )
    
    def should_stop_by_loss(self, max_loss_times: int = 3) -> bool:
        """
        检查是否应该因连续亏损而停止
        
        Args:
            max_loss_times: 最大亏损次数
            
        Returns:
            是否应该停止
        """
        return (self.loss_times is not None and 
                self.loss_times >= max_loss_times)
    
    def increment_loss_times(self) -> None:
        """增加亏损次数"""
        if self.loss_times is None:
            self.loss_times = 1
        else:
            self.loss_times += 1
    
    def reset_loss_times(self) -> None:
        """重置亏损次数"""
        self.loss_times = 0
    
    @classmethod
    def builder(cls) -> 'TrailingGroupBuilder':
        """创建构建器"""
        return TrailingGroupBuilder()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TrailingGroup':
        """从字典创建实例"""
        # 处理datetime字段
        if 'create_time' in data and data['create_time']:
            if isinstance(data['create_time'], str):
                try:
                    data['create_time'] = datetime.fromisoformat(data['create_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['create_time'] = None
        
        if 'update_time' in data and data['update_time']:
            if isinstance(data['update_time'], str):
                try:
                    data['update_time'] = datetime.fromisoformat(data['update_time'].replace('Z', '+00:00'))
                except ValueError:
                    data['update_time'] = None
        
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"TrailingGroup(id={self.id}, group_name='{self.group_name}', "
                f"state={self.get_state_name()}, loss_times={self.loss_times})")
    
    def __repr__(self) -> str:
        """对象表示"""
        return (f"TrailingGroup(id={self.id}, user_id={self.user_id}, "
                f"parent_id={self.parent_id}, group_name='{self.group_name}', "
                f"state={self.state}, loss_times={self.loss_times})")


class TrailingGroupBuilder:
    """TrailingGroup构建器"""
    
    def __init__(self):
        self._data = {}
    
    def id(self, id: int) -> 'TrailingGroupBuilder':
        self._data['id'] = id
        return self
    
    def user_id(self, user_id: int) -> 'TrailingGroupBuilder':
        self._data['user_id'] = user_id
        return self
    
    def parent_id(self, parent_id: int) -> 'TrailingGroupBuilder':
        self._data['parent_id'] = parent_id
        return self
    
    def group_name(self, group_name: str) -> 'TrailingGroupBuilder':
        self._data['group_name'] = group_name
        return self
    
    def profit_state(self, profit_state: int) -> 'TrailingGroupBuilder':
        self._data['profit_state'] = profit_state
        return self
    
    def state(self, state: int) -> 'TrailingGroupBuilder':
        self._data['state'] = state
        return self
    
    def loss_times(self, loss_times: int) -> 'TrailingGroupBuilder':
        self._data['loss_times'] = loss_times
        return self
    
    def message(self, message: str) -> 'TrailingGroupBuilder':
        self._data['message'] = message
        return self
    
    def create_time(self, create_time: datetime) -> 'TrailingGroupBuilder':
        self._data['create_time'] = create_time
        return self
    
    def update_time(self, update_time: datetime) -> 'TrailingGroupBuilder':
        self._data['update_time'] = update_time
        return self
    
    def build(self) -> TrailingGroup:
        """构建TrailingGroup实例"""
        return TrailingGroup(**self._data)

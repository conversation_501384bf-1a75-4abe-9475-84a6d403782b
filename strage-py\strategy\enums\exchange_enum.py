"""
交易所枚举类
迁移自: com.project.strategy.enums.ExchangeEnum
"""
from enum import Enum
from typing import Optional


class ExchangeEnum(Enum):
    """交易所枚举"""
    
    BINANCE = (1, "BINANCE", "币安")
    OKX = (2, "OKX", "OKX")
    OTHERS = (-1, "OTHERS", "Others")
    
    def __init__(self, code: int, name: str, desc: str):
        """
        初始化交易所枚举
        
        Args:
            code: 交易所代码
            name: 交易所名称
            desc: 交易所描述
        """
        self.code = code
        self.type_name = name  # 使用type_name避免与Enum.name冲突
        self.desc = desc
    
    def get_code(self) -> int:
        """获取交易所代码"""
        return self.code
    
    def get_name(self) -> str:
        """获取交易所名称"""
        return self.type_name
    
    def get_desc(self) -> str:
        """获取交易所描述"""
        return self.desc
    
    @classmethod
    def parse_code(cls, code: int) -> Optional['ExchangeEnum']:
        """
        根据代码解析交易所枚举
        
        Args:
            code: 交易所代码
            
        Returns:
            对应的交易所枚举，如果未找到则返回None
        """
        for exchange_enum in cls:
            if exchange_enum.get_code() == code:
                return exchange_enum
        return None
    
    @classmethod
    def parse_name(cls, name: str) -> Optional['ExchangeEnum']:
        """
        根据名称解析交易所枚举
        
        Args:
            name: 交易所名称
            
        Returns:
            对应的交易所枚举，如果未找到则返回None
        """
        for exchange_enum in cls:
            if exchange_enum.get_name() == name:
                return exchange_enum
        return None
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.type_name}({self.code}): {self.desc}"
    
    def __repr__(self) -> str:
        """对象表示"""
        return f"ExchangeEnum.{self.name}"
